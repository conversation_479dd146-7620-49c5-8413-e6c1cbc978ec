#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

import json
import re

def smart_translate_dialogue(dialogue_text):
    """智能翻译对话内容"""
    
    # 保护特殊标记，避免被翻译
    protected_patterns = [
        (r'<start_chat>', '【START_CHAT】'),
        (r'<end_chat>', '【END_CHAT】'),
        (r'<Human (\d+)>', r'【HUMAN_\1】'),
        (r'<Assistant (\d+)>', r'【ASSISTANT_\1】'),
        (r'\(word count: (\d+) words?\)', r'【WORD_COUNT_\1】'),
        (r'/([a-z]+)/', r'【PHONEME_\1】'),  # 保护音素符号
    ]
    
    # 应用保护
    text = dialogue_text
    for pattern, replacement in protected_patterns:
        text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)
    
    # 分句翻译映射
    sentence_translations = {
        # 语音治疗相关完整句子
        "How can speech disorders be treated?": "语音障碍如何治疗？",
        "How do I say the /f/ sound?": "我如何发/f/音？",
        "How can I help my child with lip movement?": "我如何帮助我的孩子练习唇部运动？",
        "Why is the K sound hard for kids to say?": "为什么K音对孩子来说很难发？",
        "How can I help my child practice the /j/ sound?": "我如何帮助我的孩子练习/j/音？",
        "Can you guide me through a speech therapy exercise for a child?": "你能指导我为孩子做语音治疗练习吗？",
        "I need help understanding how to teach the /r/ sound to a child.": "我需要帮助了解如何教孩子发/r/音。",
        "What if the cereal falls off?": "如果谷物圈掉了怎么办？",
        "What if I feel air for /b/?": "如果我发/b/音时感觉到气流怎么办？",
        "Can you give an example word?": "你能给个例子单词吗？",
        "What if I still can't tell them apart?": "如果我仍然无法区分它们怎么办？",
        "Can you explain how mirroring helps in therapy?": "你能解释镜像在治疗中如何帮助吗？",
        "What if I want to try some exercises at home?": "如果我想在家尝试一些练习怎么办？",
        "How can we help a child practice the K sound?": "我们如何帮助孩子练习K音？",
        "Can you give an example of a game?": "你能给个游戏的例子吗？",
        "How can I improve my public speaking skills?": "我如何提高我的公共演讲技能？",
        "What are some common mistakes to avoid?": "有哪些常见错误需要避免？",
        
        # 常见回答开头
        "To help your child with lip movement": "为了帮助你的孩子练习唇部运动",
        "To make the /f/ sound": "要发/f/音",
        "Speech disorders can often be treated": "语音障碍通常可以通过治疗",
        "The /k/ sound is made in the back of the throat": "/k/音是在喉咙后部发出的",
        "Mirroring is a helpful technique": "镜像是一个有用的技巧",
        "That's a great idea!": "这是个好主意！",
        "Sure! Let's use the word": "当然！我们用这个词",
        "If you feel air when making": "如果你在发音时感觉到气流",
        "You're not alone if you find it hard": "如果你觉得困难，你并不孤单",
        
        # 专业术语和短语
        "speech-language pathologist": "语音语言病理学家",
        "licensed speech language pathologist": "持证语音语言病理学家",
        "articulation therapy": "构音治疗",
        "speech therapy": "语音治疗",
        "visual feedback": "视觉反馈",
        "mouth movements": "口部运动",
        "tongue position": "舌位",
        "vocal cords": "声带",
        "breath control": "呼吸控制",
        "pronunciation": "发音",
        "articulation": "构音",
        "speech sounds": "语音",
        "phoneme": "音素",
        "speech development": "语音发展",
        "communication skills": "沟通技能",
        "speech intelligibility": "语音清晰度",
        "speech exercises": "语音练习",
        "gestural cueing": "手势提示",
        "interactive games": "互动游戏",
        "positive reinforcement": "正面强化",
        "consistent practice": "持续练习",
        
        # 其他常见短语
        "word count": "字数要求",
        "further asks": "进一步询问",
        "gives specific instructions": "给出具体指示",
        "expresses his/her needs": "表达他/她的需求",
        "asks with curiosity": "好奇地询问",
        "asks in a childlike tone": "用孩子般的语气询问",
        "answers in a way that a child can understand": "用孩子能理解的方式回答",
        "detailed explanation": "详细解释",
        "from the perspective of real life": "从现实生活的角度",
    }
    
    # 应用句子级翻译
    for english, chinese in sentence_translations.items():
        text = re.sub(re.escape(english), chinese, text, flags=re.IGNORECASE)
    
    # 恢复保护的标记
    restore_patterns = [
        (r'【START_CHAT】', '<开始对话>'),
        (r'【END_CHAT】', '<结束对话>'),
        (r'【HUMAN_(\d+)】', r'<人类 \1>'),
        (r'【ASSISTANT_(\d+)】', r'<助手 \1>'),
        (r'【WORD_COUNT_(\d+)】', r'(字数要求：\1字)'),
        (r'【PHONEME_([a-z]+)】', r'/\1/'),
    ]
    
    for pattern, replacement in restore_patterns:
        text = re.sub(pattern, replacement, text)
    
    return text

def create_bilingual_jsonl(input_file, output_file):
    """创建双语对照的JSONL文件"""
    
    translated_count = 0
    
    with open(input_file, "r", encoding="utf8") as f_in, \
         open(output_file, "w", encoding="utf8") as f_out:
        
        for line_num, line in enumerate(f_in, 1):
            if not line.strip():
                continue
                
            try:
                data = json.loads(line.strip())
                
                # 创建双语版本
                bilingual_data = {
                    "id": line_num,
                    "rounds": data.get("rounds", 0),
                    "word_counts": data.get("word_counts", {}),
                }
                
                # 处理对话内容
                if "dialogue" in data and data["dialogue"]:
                    original_dialogue = data["dialogue"]
                    chinese_dialogue = smart_translate_dialogue(original_dialogue)
                    
                    bilingual_data["dialogue"] = {
                        "english": original_dialogue,
                        "chinese": chinese_dialogue
                    }
                
                # 处理标题
                if "title" in data and data["title"]:
                    bilingual_data["title"] = {
                        "english": data["title"],
                        "chinese": smart_translate_dialogue(data["title"])
                    }
                
                # 处理参考内容
                if "reference" in data and data["reference"]:
                    bilingual_data["reference"] = {
                        "english": data["reference"],
                        "chinese": smart_translate_dialogue(data["reference"])
                    }
                
                # 保留其他字段
                for key in ["prompt", "meta"]:
                    if key in data:
                        bilingual_data[key] = data[key]
                
                f_out.write(json.dumps(bilingual_data, ensure_ascii=False) + "\n")
                translated_count += 1
                
                if line_num % 25 == 0:
                    print(f"已翻译 {line_num} 个对话...")
                    
            except json.JSONDecodeError as e:
                print(f"第 {line_num} 行JSON解析错误: {e}")
                continue
    
    return translated_count

if __name__ == "__main__":
    print("🌐 开始智能翻译对话内容...")
    
    # 显示翻译示例
    sample = "How do I say the /f/ sound?"
    translated = smart_translate_dialogue(sample)
    print(f"翻译示例: '{sample}' → '{translated}'")
    
    print("\n" + "="*50)
    
    # 执行翻译
    count = create_bilingual_jsonl(
        "output-refgpt-qwen.jsonl",
        "output-refgpt-qwen-bilingual.jsonl"
    )
    
    print(f"\n✅ 翻译完成！")
    print(f"成功翻译 {count} 个对话")
    print(f"双语文件: output-refgpt-qwen-bilingual.jsonl")
    print(f"\n📖 文件结构:")
    print("- dialogue.english: 英文原文")
    print("- dialogue.chinese: 中文翻译")
    print("- 保留所有原始字段")
