import json

input_path = 'paragraphs_output.jsonl'  # 原始多行 JSONL 文件路径
output_path = 'filtered_output.jsonl'   # 过滤后输出路径

with open(input_path, 'r', encoding='utf-8') as infile, \
     open(output_path, 'w', encoding='utf-8') as outfile:
    for line in infile:
        line = line.strip()
        if not line:
            continue
        obj = json.loads(line)
        # 仅保留 type 既不是 header 也不是 footer 的条目
        if obj.get('type') not in ('header', 'footer'):
            outfile.write(json.dumps(obj, ensure_ascii=False) + '\n')
