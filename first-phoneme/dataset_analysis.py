#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

"""
婴幼儿语音构音监测数据集分析工具
"""

import json
from collections import Counter, defaultdict

def analyze_dataset(file_path: str):
    """分析数据集质量和分布"""
    
    with open(file_path, "r", encoding="utf8") as f:
        data = [json.loads(line.strip()) for line in f if line.strip()]
    
    print(f"📊 数据集分析报告")
    print(f"="*60)
    print(f"总记录数: {len(data)}")
    
    # 年龄分布分析
    ages = [record["child_profile"]["age_months"] for record in data]
    age_groups = {
        "12-18个月": len([age for age in ages if 12 <= age < 18]),
        "18-24个月": len([age for age in ages if 18 <= age < 24]),
        "24-36个月": len([age for age in ages if 24 <= age < 36]),
        "36-48个月": len([age for age in ages if 36 <= age <= 48])
    }
    
    print(f"\n👶 年龄分布:")
    for age_group, count in age_groups.items():
        percentage = count / len(data) * 100
        print(f"  {age_group}: {count} 个 ({percentage:.1f}%)")
    
    # 性别分布
    genders = Counter([record["child_profile"]["gender"] for record in data])
    print(f"\n👫 性别分布:")
    for gender, count in genders.items():
        percentage = count / len(data) * 100
        print(f"  {gender}: {count} 个 ({percentage:.1f}%)")
    
    # 严重程度分布
    severity_levels = Counter([record["severity_assessment"]["severity_level"] for record in data])
    print(f"\n🎯 严重程度分布:")
    for level, count in severity_levels.items():
        percentage = count / len(data) * 100
        print(f"  {level}: {count} 个 ({percentage:.1f}%)")
    
    # 对话类型分布
    dialogue_types = Counter([record["dialogue_type"] for record in data])
    print(f"\n💬 对话类型分布:")
    for dtype, count in dialogue_types.items():
        percentage = count / len(data) * 100
        print(f"  {dtype}: {count} 个 ({percentage:.1f}%)")
    
    # 语言环境分布
    lang_envs = Counter([record["child_profile"]["language_environment"] for record in data])
    print(f"\n🌍 语言环境分布:")
    for env, count in lang_envs.items():
        percentage = count / len(data) * 100
        print(f"  {env}: {count} 个 ({percentage:.1f}%)")
    
    # 发展关注点分布
    concerns = Counter([record["child_profile"]["developmental_concerns"] for record in data])
    print(f"\n⚠️ 发展关注点分布:")
    for concern, count in concerns.items():
        percentage = count / len(data) * 100
        concern_str = concern if concern else "无关注点"
        print(f"  {concern_str}: {count} 个 ({percentage:.1f}%)")
    
    # 准确率分布分析
    accuracies = [record["severity_assessment"]["overall_accuracy"] for record in data]
    mean_accuracy = sum(accuracies) / len(accuracies)
    std_accuracy = (sum((x - mean_accuracy) ** 2 for x in accuracies) / len(accuracies)) ** 0.5

    print(f"\n📈 语音准确率统计:")
    print(f"  平均准确率: {mean_accuracy:.1f}%")
    print(f"  准确率范围: {min(accuracies):.1f}% - {max(accuracies):.1f}%")
    print(f"  标准差: {std_accuracy:.1f}%")
    
    # 音素错误模式分析
    error_patterns = defaultdict(int)
    for record in data:
        phoneme_assessment = record["phoneme_assessment"]
        for phoneme, positions in phoneme_assessment.items():
            for position, result in positions.items():
                if result != "correct":
                    error_patterns[result] += 1
    
    print(f"\n🔍 音素错误模式分析:")
    total_errors = sum(error_patterns.values())
    for pattern, count in sorted(error_patterns.items(), key=lambda x: x[1], reverse=True):
        percentage = count / total_errors * 100
        print(f"  {pattern}: {count} 次 ({percentage:.1f}%)")
    
    # 建议类型分析
    all_recommendations = []
    for record in data:
        all_recommendations.extend(record["recommendations"])
    
    recommendation_counts = Counter(all_recommendations)
    print(f"\n💡 建议类型分布:")
    for rec, count in recommendation_counts.most_common():
        percentage = count / len(data) * 100
        print(f"  {rec}: {count} 次 ({percentage:.1f}%)")
    
    return {
        "total_records": len(data),
        "age_distribution": age_groups,
        "severity_distribution": dict(severity_levels),
        "dialogue_type_distribution": dict(dialogue_types),
        "accuracy_stats": {
            "mean": mean_accuracy,
            "min": min(accuracies),
            "max": max(accuracies),
            "std": std_accuracy
        },
        "error_patterns": dict(error_patterns)
    }

def validate_data_quality(file_path: str):
    """验证数据质量"""
    
    with open(file_path, "r", encoding="utf8") as f:
        data = [json.loads(line.strip()) for line in f if line.strip()]
    
    print(f"\n🔍 数据质量验证:")
    print(f"="*40)
    
    issues = []
    
    for i, record in enumerate(data):
        # 检查必要字段
        required_fields = ["record_id", "child_profile", "phoneme_assessment", 
                          "severity_assessment", "recommendations", "dialogue"]
        
        for field in required_fields:
            if field not in record:
                issues.append(f"记录 {i+1}: 缺少字段 '{field}'")
        
        # 检查年龄合理性
        age = record.get("child_profile", {}).get("age_months", 0)
        if not (12 <= age <= 48):
            issues.append(f"记录 {i+1}: 年龄不在合理范围 (12-48个月)")
        
        # 检查准确率合理性
        accuracy = record.get("severity_assessment", {}).get("overall_accuracy", 0)
        if not (0 <= accuracy <= 100):
            issues.append(f"记录 {i+1}: 准确率不在合理范围 (0-100%)")
        
        # 检查对话内容
        dialogue = record.get("dialogue", "")
        if not dialogue or len(dialogue) < 50:
            issues.append(f"记录 {i+1}: 对话内容过短或为空")
        
        # 检查音素评估完整性
        phoneme_assessment = record.get("phoneme_assessment", {})
        if len(phoneme_assessment) < 20:  # 应该有22个音素
            issues.append(f"记录 {i+1}: 音素评估不完整")
    
    if issues:
        print(f"❌ 发现 {len(issues)} 个问题:")
        for issue in issues[:10]:  # 只显示前10个问题
            print(f"  - {issue}")
        if len(issues) > 10:
            print(f"  ... 还有 {len(issues) - 10} 个问题")
    else:
        print(f"✅ 数据质量良好，未发现问题")
    
    return len(issues) == 0

def show_sample_dialogues(file_path: str, num_samples: int = 3):
    """显示示例对话"""
    
    with open(file_path, "r", encoding="utf8") as f:
        data = [json.loads(line.strip()) for line in f if line.strip()]
    
    print(f"\n💬 示例对话展示:")
    print(f"="*50)
    
    # 按对话类型分组显示
    dialogue_types = {}
    for record in data:
        dtype = record["dialogue_type"]
        if dtype not in dialogue_types:
            dialogue_types[dtype] = []
        dialogue_types[dtype].append(record)
    
    for dtype, records in dialogue_types.items():
        print(f"\n📝 {dtype} 类型对话示例:")
        sample_record = records[0]  # 取第一个作为示例
        
        child_info = sample_record["child_profile"]
        severity = sample_record["severity_assessment"]
        
        print(f"儿童信息: {child_info['age_years_months']}, {child_info['gender']}")
        print(f"严重程度: {severity['severity_level']} (准确率: {severity['overall_accuracy']}%)")
        print(f"对话内容:")
        
        dialogue_lines = sample_record["dialogue"].split('\n')
        for line in dialogue_lines:
            if line.strip():
                print(f"  {line}")
        print()

if __name__ == "__main__":
    file_path = "test_assessment_dataset.jsonl"
    
    # 分析数据集
    stats = analyze_dataset(file_path)
    
    # 验证数据质量
    is_valid = validate_data_quality(file_path)
    
    # 显示示例对话
    show_sample_dialogues(file_path)
    
    print(f"\n📋 总结:")
    print(f"- 数据集包含 {stats['total_records']} 条记录")
    print(f"- 覆盖 {len(stats['age_distribution'])} 个年龄段")
    print(f"- 包含 {len(stats['dialogue_type_distribution'])} 种对话类型")
    print(f"- 数据质量: {'✅ 良好' if is_valid else '❌ 需要改进'}")
    print(f"- 平均语音准确率: {stats['accuracy_stats']['mean']:.1f}%")
    
    print(f"\n🚀 下一步建议:")
    print(f"1. 扩大数据集规模到1000+条记录")
    print(f"2. 平衡各年龄段和严重程度的分布")
    print(f"3. 增加更多样化的对话场景")
    print(f"4. 邀请专业人士验证内容准确性")
    print(f"5. 添加更多的家庭练习指导内容")
