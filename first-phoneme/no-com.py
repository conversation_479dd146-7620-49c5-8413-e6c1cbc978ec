import json

input_path = 'filtered_output.jsonl'   # 上一步已过滤 header/footer 的文件
output_path = 'no_com_output.jsonl'    # 进一步过滤后的输出文件

with open(input_path, 'r', encoding='utf-8') as infile, \
     open(output_path, 'w', encoding='utf-8') as outfile:
    for line in infile:
        line = line.strip()
        if not line:
            continue
        obj = json.loads(line)
        text = obj.get('text', '')
        # 如果 text 中包含 'com'（不区分大小写），则跳过该条目
        if '.com' in text.lower():
            continue
        # 否则写入输出
        outfile.write(json.dumps(obj, ensure_ascii=False) + '\n')

print(f"过滤完成，结果写入 {output_path}")
