#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

"""
构音数据集分析工具
专门分析基于52词汇测试的语音构音数据集
"""

import json
from collections import Counter, defaultdict
from typing import Dict, List, Any

class GouyinDatasetAnalyzer:
    """构音数据集分析器"""
    
    def __init__(self, dataset_file: str):
        self.dataset_file = dataset_file
        self.data = self.load_dataset()
        
    def load_dataset(self) -> List[Dict[str, Any]]:
        """加载数据集"""
        try:
            with open(self.dataset_file, "r", encoding="utf8") as f:
                data = [json.loads(line.strip()) for line in f if line.strip()]
            print(f"✅ 成功加载 {len(data)} 条记录")
            return data
        except Exception as e:
            print(f"❌ 加载数据集失败: {e}")
            return []
    
    def analyze_basic_statistics(self):
        """分析基本统计信息"""
        print(f"\n📊 基本统计信息")
        print(f"="*50)
        
        if not self.data:
            print("❌ 无数据可分析")
            return
        
        print(f"总记录数: {len(self.data)}")
        
        # 年龄分布
        ages = [record["basic_info"]["age_months"] for record in self.data]
        age_groups = {
            "12-18个月": len([age for age in ages if 12 <= age < 18]),
            "18-24个月": len([age for age in ages if 18 <= age < 24]),
            "24-36个月": len([age for age in ages if 24 <= age < 36]),
            "36-48个月": len([age for age in ages if 36 <= age <= 48])
        }
        
        print(f"\n👶 年龄分布:")
        for age_group, count in age_groups.items():
            percentage = count / len(self.data) * 100
            print(f"  {age_group}: {count} 个 ({percentage:.1f}%)")
        
        # 性别分布
        genders = Counter([record["basic_info"]["gender"] for record in self.data])
        print(f"\n👫 性别分布:")
        for gender, count in genders.items():
            percentage = count / len(self.data) * 100
            print(f"  {gender}: {count} 个 ({percentage:.1f}%)")
        
        # 合作水平分布
        cooperation_levels = Counter([record["basic_info"]["cooperation_level"] for record in self.data])
        print(f"\n🤝 合作水平分布:")
        for level, count in cooperation_levels.items():
            percentage = count / len(self.data) * 100
            print(f"  {level}: {count} 个 ({percentage:.1f}%)")
    
    def analyze_assessment_results(self):
        """分析评估结果"""
        print(f"\n📈 评估结果分析")
        print(f"="*50)
        
        # 准确率分布
        accuracies = [record["overall_results"]["accuracy_percentage"] for record in self.data]
        mean_accuracy = sum(accuracies) / len(accuracies)
        min_accuracy = min(accuracies)
        max_accuracy = max(accuracies)
        
        print(f"语音准确率统计:")
        print(f"  平均准确率: {mean_accuracy:.1f}%")
        print(f"  准确率范围: {min_accuracy:.1f}% - {max_accuracy:.1f}%")
        
        # 表现水平分布
        performance_levels = Counter([record["overall_results"]["performance_level"] for record in self.data])
        print(f"\n🎯 表现水平分布:")
        for level, count in performance_levels.items():
            percentage = count / len(self.data) * 100
            print(f"  {level}: {count} 个 ({percentage:.1f}%)")
        
        # 严重程度分布
        severity_levels = Counter([record["overall_results"]["severity_level"] for record in self.data])
        print(f"\n⚠️ 严重程度分布:")
        for level, count in severity_levels.items():
            percentage = count / len(self.data) * 100
            print(f"  {level}: {count} 个 ({percentage:.1f}%)")
        
        # 清晰度分布
        intelligibility_levels = Counter([record["overall_results"]["intelligibility"] for record in self.data])
        print(f"\n🔊 语音清晰度分布:")
        for level, count in intelligibility_levels.items():
            percentage = count / len(self.data) * 100
            print(f"  {level}: {count} 个 ({percentage:.1f}%)")
    
    def analyze_phoneme_errors(self):
        """分析音素错误模式"""
        print(f"\n🔍 音素错误模式分析")
        print(f"="*50)
        
        # 统计所有错误模式
        all_error_patterns = defaultdict(int)
        main_error_patterns = Counter()
        
        for record in self.data:
            phoneme_analysis = record["phoneme_analysis"]
            
            # 统计各种错误类型
            for error_type, count in phoneme_analysis["error_patterns"].items():
                all_error_patterns[error_type] += count
            
            # 统计主要错误模式
            main_pattern = phoneme_analysis["main_error_pattern"]
            main_error_patterns[main_pattern] += 1
        
        print(f"错误类型统计:")
        total_errors = sum(all_error_patterns.values())
        for error_type, count in sorted(all_error_patterns.items(), key=lambda x: x[1], reverse=True):
            percentage = count / total_errors * 100 if total_errors > 0 else 0
            print(f"  {error_type}: {count} 次 ({percentage:.1f}%)")
        
        print(f"\n主要错误模式分布:")
        for pattern, count in main_error_patterns.most_common():
            percentage = count / len(self.data) * 100
            print(f"  {pattern}: {count} 个案例 ({percentage:.1f}%)")
        
        # 分析错误一致性
        consistency_levels = Counter([record["phoneme_analysis"]["error_consistency"] for record in self.data])
        print(f"\n🎯 错误一致性分布:")
        for level, count in consistency_levels.items():
            percentage = count / len(self.data) * 100
            print(f"  {level}: {count} 个 ({percentage:.1f}%)")
    
    def analyze_problem_phonemes(self):
        """分析问题音素"""
        print(f"\n🎵 问题音素分析")
        print(f"="*50)
        
        # 统计所有问题音素
        problem_phonemes = defaultdict(int)
        
        for record in self.data:
            priority_phonemes = record["recommendations"]["priority_phonemes"]
            for phoneme in priority_phonemes:
                problem_phonemes[phoneme] += 1
        
        print(f"最常见的问题音素 (前15个):")
        for phoneme, count in sorted(problem_phonemes.items(), key=lambda x: x[1], reverse=True)[:15]:
            percentage = count / len(self.data) * 100
            print(f"  {phoneme}: {count} 次 ({percentage:.1f}%)")
    
    def analyze_recommendations(self):
        """分析建议内容"""
        print(f"\n💡 建议内容分析")
        print(f"="*50)
        
        # 统计治疗方法
        all_therapy_approaches = []
        for record in self.data:
            all_therapy_approaches.extend(record["recommendations"]["therapy_approaches"])
        
        therapy_counts = Counter(all_therapy_approaches)
        print(f"最常见的治疗方法:")
        for approach, count in therapy_counts.most_common(10):
            percentage = count / len(self.data) * 100
            print(f"  {approach}: {count} 次 ({percentage:.1f}%)")
        
        # 统计家庭活动
        all_home_activities = []
        for record in self.data:
            all_home_activities.extend(record["recommendations"]["home_activities"])
        
        activity_counts = Counter(all_home_activities)
        print(f"\n最常见的家庭活动:")
        for activity, count in activity_counts.most_common(10):
            percentage = count / len(self.data) * 100
            print(f"  {activity}: {count} 次 ({percentage:.1f}%)")
        
        # 统计随访计划
        all_follow_up = []
        for record in self.data:
            all_follow_up.extend(record["recommendations"]["follow_up_plan"])
        
        follow_up_counts = Counter(all_follow_up)
        print(f"\n随访计划分布:")
        for plan, count in follow_up_counts.most_common():
            percentage = count / len(self.data) * 100
            print(f"  {plan}: {count} 次 ({percentage:.1f}%)")
    
    def analyze_dialogue_quality(self):
        """分析对话质量"""
        print(f"\n💬 对话质量分析")
        print(f"="*50)
        
        dialogue_lengths = []
        dialogue_types = Counter()
        
        for record in self.data:
            dialogue = record["dialogue"]
            dialogue_length = len(dialogue)
            dialogue_lengths.append(dialogue_length)
            
            dialogue_type = record["dialogue_type"]
            dialogue_types[dialogue_type] += 1
        
        # 对话长度统计
        mean_length = sum(dialogue_lengths) / len(dialogue_lengths)
        min_length = min(dialogue_lengths)
        max_length = max(dialogue_lengths)
        
        print(f"对话长度统计:")
        print(f"  平均长度: {mean_length:.0f} 字符")
        print(f"  长度范围: {min_length} - {max_length} 字符")
        
        # 对话类型分布
        print(f"\n对话类型分布:")
        for dtype, count in dialogue_types.items():
            percentage = count / len(self.data) * 100
            print(f"  {dtype}: {count} 个 ({percentage:.1f}%)")
    
    def show_sample_records(self, num_samples: int = 3):
        """显示样本记录"""
        print(f"\n📝 样本记录展示")
        print(f"="*50)
        
        for i in range(min(num_samples, len(self.data))):
            record = self.data[i]
            basic_info = record["basic_info"]
            overall_results = record["overall_results"]
            
            print(f"\n样本 {i+1}:")
            print(f"  儿童信息: {basic_info['age_description']}, {basic_info['gender']}")
            print(f"  准确率: {overall_results['accuracy_percentage']}%")
            print(f"  表现水平: {overall_results['performance_level']}")
            print(f"  严重程度: {overall_results['severity_level']}")
            print(f"  主要问题音素: {', '.join(record['recommendations']['priority_phonemes'][:3])}")
            
            # 显示对话片段
            dialogue = record["dialogue"]
            dialogue_preview = dialogue[:200] + "..." if len(dialogue) > 200 else dialogue
            print(f"  对话预览: {dialogue_preview}")
    
    def validate_data_quality(self):
        """验证数据质量"""
        print(f"\n🔍 数据质量验证")
        print(f"="*50)
        
        issues = []
        
        for i, record in enumerate(self.data):
            record_id = record.get("record_id", f"记录{i+1}")
            
            # 检查必要字段
            required_fields = [
                "basic_info", "word_assessment", "phoneme_analysis",
                "overall_results", "recommendations", "dialogue"
            ]
            
            for field in required_fields:
                if field not in record:
                    issues.append(f"{record_id}: 缺少字段 '{field}'")
            
            # 检查年龄合理性
            age = record.get("basic_info", {}).get("age_months", 0)
            if not (12 <= age <= 48):
                issues.append(f"{record_id}: 年龄不合理 ({age}个月)")
            
            # 检查准确率合理性
            accuracy = record.get("overall_results", {}).get("accuracy_percentage", 0)
            if not (0 <= accuracy <= 100):
                issues.append(f"{record_id}: 准确率不合理 ({accuracy}%)")
            
            # 检查词汇评估完整性
            word_assessment = record.get("word_assessment", {})
            if len(word_assessment) != 52:
                issues.append(f"{record_id}: 词汇评估不完整 ({len(word_assessment)}/52)")
            
            # 检查对话内容
            dialogue = record.get("dialogue", "")
            if not dialogue or len(dialogue) < 100:
                issues.append(f"{record_id}: 对话内容过短")
        
        if issues:
            print(f"❌ 发现 {len(issues)} 个问题:")
            for issue in issues[:10]:  # 只显示前10个问题
                print(f"  - {issue}")
            if len(issues) > 10:
                print(f"  ... 还有 {len(issues) - 10} 个问题")
            return False
        else:
            print(f"✅ 数据质量良好，未发现问题")
            return True
    
    def generate_report(self):
        """生成完整分析报告"""
        print(f"🚀 构音数据集分析报告")
        print(f"="*60)
        print(f"数据集文件: {self.dataset_file}")
        print(f"分析时间: {datetime.datetime.now().isoformat()}")
        
        # 执行所有分析
        self.analyze_basic_statistics()
        self.analyze_assessment_results()
        self.analyze_phoneme_errors()
        self.analyze_problem_phonemes()
        self.analyze_recommendations()
        self.analyze_dialogue_quality()
        self.show_sample_records()
        is_valid = self.validate_data_quality()
        
        # 生成总结
        print(f"\n📋 分析总结")
        print(f"="*50)
        print(f"- 数据集规模: {len(self.data)} 条记录")
        print(f"- 年龄覆盖: 12-48个月")
        print(f"- 词汇测试: 52个标准化词汇")
        print(f"- 数据质量: {'✅ 良好' if is_valid else '❌ 需要改进'}")
        
        if self.data:
            mean_accuracy = sum(record["overall_results"]["accuracy_percentage"] for record in self.data) / len(self.data)
            print(f"- 平均准确率: {mean_accuracy:.1f}%")
        
        print(f"\n🎯 应用建议:")
        print(f"1. 数据集可用于大模型微调训练")
        print(f"2. 建议扩大数据集规模到1000+条记录")
        print(f"3. 可用于构音评估和指导系统开发")
        print(f"4. 适合婴幼儿语音发展监测应用")

if __name__ == "__main__":
    import datetime
    
    # 分析测试数据集
    analyzer = GouyinDatasetAnalyzer("test_gouyin_dataset.jsonl")
    analyzer.generate_report()
