#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

"""
基于gouyin-1文件夹中构音语音能力评估记录表的数据集构建工具
专门处理包含52个词汇及目标音的评估表结构
"""

import pandas as pd
import json
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple

class GouyinDatasetBuilder:
    """构音评估数据集构建器"""
    
    def __init__(self, excel_file: str = "gouyin-1/构音语音能力评估记录表（all）.xlsx"):
        self.excel_file = excel_file
        self.word_list = []  # 52个词汇列表
        self.target_phonemes = []  # 对应的目标音
        self.assessment_structure = {}
        self.load_excel_data()
        
    def load_excel_data(self):
        """加载Excel数据"""
        try:
            # 读取所有工作表
            excel_data = pd.read_excel(self.excel_file, sheet_name=None, header=None)
            
            print(f"📊 分析Excel文件: {self.excel_file}")
            print(f"工作表数量: {len(excel_data)}")
            print(f"工作表名称: {list(excel_data.keys())}")
            
            # 分析每个工作表
            for sheet_name, df in excel_data.items():
                print(f"\n📋 工作表: {sheet_name}")
                print(f"  行数: {len(df)}, 列数: {len(df.columns)}")
                
                # 如果是第一个工作表，尝试提取52个词汇
                if sheet_name.lower() in ['sheet1', 'page1', '工作表1']:
                    self.extract_word_list(df)
                
                # 显示前几行内容
                self.show_sheet_sample(df, sheet_name)
            
            self.assessment_structure = {
                "total_sheets": len(excel_data),
                "sheet_names": list(excel_data.keys()),
                "word_count": len(self.word_list),
                "target_phonemes_count": len(self.target_phonemes)
            }
            
        except Exception as e:
            print(f"❌ 读取Excel文件失败: {e}")
            self.assessment_structure = {"error": str(e)}
    
    def extract_word_list(self, df: pd.DataFrame):
        """从第一个工作表提取52个词汇和目标音"""
        print(f"\n🔍 提取词汇列表...")

        words = []
        phonemes = []
        word_phoneme_pairs = []

        # 根据表格结构，假设第2列是词汇，第4列是目标音
        # 跳过标题行，从第2行开始
        for i in range(1, len(df)):
            # 提取词汇（第2列，索引1）
            if len(df.columns) > 1:
                word_cell = df.iloc[i, 1]
                if pd.notna(word_cell):
                    word = str(word_cell).strip()
                    # 检查是否为中文词汇
                    if len(word) >= 1 and len(word) <= 4:
                        if any('\u4e00' <= char <= '\u9fff' for char in word):
                            words.append(word)

                            # 提取对应的目标音（第4列，索引3）
                            if len(df.columns) > 3:
                                phoneme_cell = df.iloc[i, 3]
                                if pd.notna(phoneme_cell):
                                    phoneme = str(phoneme_cell).strip()
                                    phonemes.append(phoneme)
                                    word_phoneme_pairs.append((word, phoneme))
                                else:
                                    phonemes.append("未知")
                                    word_phoneme_pairs.append((word, "未知"))
                            else:
                                phonemes.append("未知")
                                word_phoneme_pairs.append((word, "未知"))

        # 如果没有提取到足够的词汇，使用预定义的52个词汇
        if len(words) < 20:
            print("⚠️ 从Excel提取的词汇较少，使用预定义词汇列表")
            self.word_list, self.target_phonemes = self.get_default_word_list()
        else:
            self.word_list = words[:52] if len(words) >= 52 else words
            self.target_phonemes = phonemes[:len(self.word_list)]

        print(f"提取到 {len(self.word_list)} 个词汇")
        print(f"提取到 {len(self.target_phonemes)} 个目标音")

        # 显示前10个示例
        print(f"\n📝 词汇示例:")
        for i in range(min(10, len(self.word_list))):
            phoneme = self.target_phonemes[i] if i < len(self.target_phonemes) else "未知"
            print(f"  {i+1:2d}. {self.word_list[i]} -> {phoneme}")

    def get_default_word_list(self) -> Tuple[List[str], List[str]]:
        """获取预定义的52个词汇列表"""

        # 基于常见的构音评估词汇
        word_phoneme_pairs = [
            ("包", "b"), ("抛", "p"), ("毛", "m"), ("跑", "p"), ("宝", "b"),
            ("刀", "d"), ("桃", "t"), ("脑", "n"), ("老", "l"), ("高", "g"),
            ("考", "k"), ("好", "h"), ("猫", "m"), ("鸟", "n"), ("草", "c"),
            ("早", "z"), ("少", "sh"), ("找", "zh"), ("小", "x"), ("教", "j"),
            ("桥", "q"), ("要", "y"), ("我", "w"), ("鱼", "y"), ("书", "sh"),
            ("树", "sh"), ("猪", "zh"), ("车", "ch"), ("茶", "ch"), ("蛇", "sh"),
            ("鞋", "x"), ("叶", "y"), ("月", "y"), ("肉", "r"), ("六", "l"),
            ("绿", "l"), ("花", "h"), ("瓜", "g"), ("画", "h"), ("话", "h"),
            ("火", "h"), ("河", "h"), ("黑", "h"), ("红", "h"), ("黄", "h"),
            ("白", "b"), ("蓝", "l"), ("紫", "z"), ("粉", "f"), ("灰", "h"),
            ("大", "d"), ("小", "x")
        ]

        words = [pair[0] for pair in word_phoneme_pairs]
        phonemes = [pair[1] for pair in word_phoneme_pairs]

        return words, phonemes
    
    def show_sheet_sample(self, df: pd.DataFrame, sheet_name: str):
        """显示工作表样本内容"""
        print(f"  样本内容:")
        
        sample_rows = min(5, len(df))
        sample_cols = min(5, len(df.columns))
        
        for i in range(sample_rows):
            row_content = []
            for j in range(sample_cols):
                cell_value = df.iloc[i, j]
                if pd.notna(cell_value):
                    cell_str = str(cell_value).strip()[:20]  # 限制长度
                    row_content.append(cell_str)
                else:
                    row_content.append("")
            
            if any(content for content in row_content):
                print(f"    行{i+1}: {' | '.join(row_content)}")
    
    def generate_assessment_record(self, age_months: int) -> Dict[str, Any]:
        """生成评估记录"""
        
        # 基本信息
        basic_info = {
            "child_id": f"GY{random.randint(1000, 9999)}",
            "age_months": age_months,
            "age_description": f"{age_months // 12}岁{age_months % 12}个月",
            "gender": random.choice(["男", "女"]),
            "assessment_date": datetime.now().strftime("%Y-%m-%d"),
            "assessor": f"评估师{random.randint(1, 8)}",
            "test_duration": f"{random.randint(20, 45)}分钟",
            "cooperation_level": random.choice(["良好", "一般", "需要鼓励", "较差"])
        }
        
        # 词汇评估结果
        word_assessment = self.generate_word_assessment(age_months)
        
        # 音素分析
        phoneme_analysis = self.analyze_phoneme_patterns(word_assessment)
        
        # 整体评估结果
        overall_results = self.calculate_overall_results(word_assessment, age_months)
        
        # 个性化建议
        recommendations = self.generate_recommendations(overall_results, phoneme_analysis, age_months)
        
        return {
            "basic_info": basic_info,
            "word_assessment": word_assessment,
            "phoneme_analysis": phoneme_analysis,
            "overall_results": overall_results,
            "recommendations": recommendations,
            "assessment_metadata": {
                "total_words_tested": len(word_assessment),
                "assessment_method": "标准化词汇测试",
                "reference_norms": "中文儿童语音发展常模"
            }
        }
    
    def generate_word_assessment(self, age_months: int) -> Dict[str, Dict[str, str]]:
        """生成词汇评估结果"""
        
        assessment = {}
        
        # 根据年龄确定基础正确率
        if age_months < 24:
            base_accuracy = 0.4
        elif age_months < 36:
            base_accuracy = 0.6
        elif age_months < 48:
            base_accuracy = 0.8
        else:
            base_accuracy = 0.9
        
        # 为每个词汇生成评估结果
        for i, word in enumerate(self.word_list):
            target_phoneme = self.target_phonemes[i] if i < len(self.target_phonemes) else "未知"
            
            # 随机决定是否正确
            is_correct = random.random() < base_accuracy
            
            if is_correct:
                result = "正确"
                actual_production = target_phoneme
            else:
                # 生成错误类型
                error_types = ["替代", "省略", "歪曲", "增加"]
                error_type = random.choice(error_types)
                result = error_type
                actual_production = self.generate_error_production(target_phoneme, error_type)
            
            assessment[word] = {
                "target_phoneme": target_phoneme,
                "actual_production": actual_production,
                "result": result,
                "stimulability": random.choice(["可刺激", "不可刺激", "部分可刺激"]) if result != "正确" else "不适用"
            }
        
        return assessment
    
    def generate_error_production(self, target: str, error_type: str) -> str:
        """生成错误发音"""
        
        if error_type == "省略":
            return "省略"
        elif error_type == "增加":
            return f"{target}+额外音"
        elif error_type == "歪曲":
            return f"{target}(歪曲)"
        else:  # 替代
            # 常见替代模式
            substitutions = {
                "/s/": "/t/", "/z/": "/d/", "/sh/": "/s/", "/zh/": "/z/",
                "/r/": "/l/", "/l/": "/n/", "/f/": "/p/", "/th/": "/s/"
            }
            return substitutions.get(target, f"{target}→其他音")
    
    def analyze_phoneme_patterns(self, word_assessment: Dict) -> Dict[str, Any]:
        """分析音素错误模式"""
        
        error_patterns = {
            "替代": 0, "省略": 0, "歪曲": 0, "增加": 0
        }
        
        phoneme_errors = {}
        total_errors = 0
        
        for word, result in word_assessment.items():
            if result["result"] != "正确":
                error_type = result["result"]
                error_patterns[error_type] += 1
                total_errors += 1
                
                target_phoneme = result["target_phoneme"]
                if target_phoneme not in phoneme_errors:
                    phoneme_errors[target_phoneme] = []
                phoneme_errors[target_phoneme].append(error_type)
        
        # 识别主要错误模式
        main_error_pattern = max(error_patterns.items(), key=lambda x: x[1])[0] if total_errors > 0 else "无"
        
        return {
            "error_patterns": error_patterns,
            "phoneme_errors": phoneme_errors,
            "total_errors": total_errors,
            "main_error_pattern": main_error_pattern,
            "error_consistency": self.calculate_error_consistency(phoneme_errors)
        }
    
    def calculate_error_consistency(self, phoneme_errors: Dict) -> str:
        """计算错误一致性"""
        if not phoneme_errors:
            return "无错误"
        
        consistent_count = 0
        total_phonemes = len(phoneme_errors)
        
        for phoneme, errors in phoneme_errors.items():
            if len(set(errors)) == 1:  # 同一音素的错误类型一致
                consistent_count += 1
        
        consistency_rate = consistent_count / total_phonemes
        
        if consistency_rate >= 0.8:
            return "高度一致"
        elif consistency_rate >= 0.5:
            return "部分一致"
        else:
            return "不一致"
    
    def calculate_overall_results(self, word_assessment: Dict, age_months: int) -> Dict[str, Any]:
        """计算整体评估结果"""
        
        total_words = len(word_assessment)
        correct_words = sum(1 for result in word_assessment.values() if result["result"] == "正确")
        accuracy = (correct_words / total_words * 100) if total_words > 0 else 0
        
        # 根据年龄确定期望准确率
        expected_accuracy = self.get_expected_accuracy(age_months)
        
        # 确定表现水平
        if accuracy >= expected_accuracy:
            performance_level = "符合年龄预期"
        elif accuracy >= expected_accuracy * 0.8:
            performance_level = "接近年龄预期"
        elif accuracy >= expected_accuracy * 0.6:
            performance_level = "低于年龄预期"
        else:
            performance_level = "明显低于年龄预期"
        
        # 确定清晰度等级
        if accuracy >= 90:
            intelligibility = "高度清晰"
        elif accuracy >= 75:
            intelligibility = "大部分清晰"
        elif accuracy >= 50:
            intelligibility = "部分清晰"
        else:
            intelligibility = "难以理解"
        
        return {
            "total_words": total_words,
            "correct_words": correct_words,
            "accuracy_percentage": round(accuracy, 1),
            "expected_accuracy": expected_accuracy,
            "performance_level": performance_level,
            "intelligibility": intelligibility,
            "severity_level": self.determine_severity(performance_level)
        }
    
    def get_expected_accuracy(self, age_months: int) -> float:
        """获取年龄对应的期望准确率"""
        if age_months < 18:
            return 30.0
        elif age_months < 24:
            return 50.0
        elif age_months < 30:
            return 65.0
        elif age_months < 36:
            return 75.0
        elif age_months < 42:
            return 85.0
        else:
            return 90.0
    
    def determine_severity(self, performance_level: str) -> str:
        """确定严重程度"""
        severity_map = {
            "符合年龄预期": "正常",
            "接近年龄预期": "轻度",
            "低于年龄预期": "中度",
            "明显低于年龄预期": "重度"
        }
        return severity_map.get(performance_level, "未知")
    
    def generate_recommendations(self, overall_results: Dict, phoneme_analysis: Dict, age_months: int) -> Dict[str, List[str]]:
        """生成个性化建议"""
        
        recommendations = {
            "immediate_goals": [],
            "therapy_approaches": [],
            "home_activities": [],
            "priority_phonemes": [],
            "follow_up_plan": []
        }
        
        severity = overall_results["severity_level"]
        main_error = phoneme_analysis["main_error_pattern"]
        
        # 基于严重程度的建议
        if severity == "重度":
            recommendations["immediate_goals"] = [
                "建立基础音素发音能力", "提高整体语音清晰度", "增强语音意识"
            ]
            recommendations["therapy_approaches"] = [
                "密集语音治疗", "多感官刺激法", "口部运动训练"
            ]
            recommendations["follow_up_plan"] = ["每月复查", "密切监测进展"]
            
        elif severity == "中度":
            recommendations["immediate_goals"] = [
                "改善特定音素发音", "提高词汇水平准确性", "加强语音规则学习"
            ]
            recommendations["therapy_approaches"] = [
                "目标音素训练", "最小对比练习", "语音意识训练"
            ]
            recommendations["follow_up_plan"] = ["6周后复查", "调整训练计划"]
            
        elif severity == "轻度":
            recommendations["immediate_goals"] = [
                "精细化发音技能", "提高复杂词汇准确性", "预防发音退步"
            ]
            recommendations["therapy_approaches"] = [
                "精准发音训练", "语音自我监控", "复杂语境练习"
            ]
            recommendations["follow_up_plan"] = ["3个月后复查", "维持性训练"]
            
        else:  # 正常
            recommendations["immediate_goals"] = [
                "维持当前发音水平", "发展更复杂语音技能", "准备学龄期语音要求"
            ]
            recommendations["therapy_approaches"] = [
                "预防性监测", "发展性活动", "语音游戏"
            ]
            recommendations["follow_up_plan"] = ["6个月后复查", "常规发展监测"]
        
        # 基于主要错误模式的建议
        if main_error == "替代":
            recommendations["therapy_approaches"].append("音素对比训练")
            recommendations["home_activities"].extend([
                "最小对比词练习", "听觉辨别游戏", "发音镜子练习"
            ])
            
        elif main_error == "省略":
            recommendations["therapy_approaches"].append("音节结构训练")
            recommendations["home_activities"].extend([
                "音节拍打游戏", "慢速发音练习", "音素意识活动"
            ])
            
        elif main_error == "歪曲":
            recommendations["therapy_approaches"].append("口部运动训练")
            recommendations["home_activities"].extend([
                "口部按摩", "舌部运动练习", "气流控制游戏"
            ])
        
        # 基于年龄的家庭活动建议
        if age_months < 24:
            recommendations["home_activities"].extend([
                "语音模仿游戏", "简单儿歌朗诵", "亲子阅读"
            ])
        elif age_months < 36:
            recommendations["home_activities"].extend([
                "故事复述", "词汇扩展游戏", "韵律练习"
            ])
        else:
            recommendations["home_activities"].extend([
                "复杂句子练习", "语音规则学习", "自我纠错训练"
            ])
        
        # 优先训练音素
        if phoneme_analysis["phoneme_errors"]:
            error_counts = {}
            for phoneme, errors in phoneme_analysis["phoneme_errors"].items():
                error_counts[phoneme] = len(errors)
            
            # 按错误频率排序，取前5个
            priority_phonemes = sorted(error_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            recommendations["priority_phonemes"] = [phoneme for phoneme, _ in priority_phonemes]
        
        return recommendations
    
    def create_dialogue_from_assessment(self, assessment: Dict[str, Any]) -> Dict[str, Any]:
        """基于评估记录创建对话"""
        
        basic_info = assessment["basic_info"]
        overall_results = assessment["overall_results"]
        recommendations = assessment["recommendations"]
        
        dialogue = f"""<开始对话>
<人类 1>: 您好，我想了解一下我家{basic_info['age_description']}孩子的语音发展情况。我们做了52个词的发音测试。
<助手 1>: 您好！我来为您详细分析孩子的构音评估结果。根据52个词汇的测试，孩子的发音准确率为{overall_results['accuracy_percentage']}%，{overall_results['performance_level']}。
<人类 2>: 这个结果怎么样？孩子的发音问题严重吗？
<助手 2>: 孩子目前的语音清晰度为{overall_results['intelligibility']}，严重程度为{overall_results['severity_level']}。在{overall_results['total_words']}个测试词汇中，有{overall_results['correct_words']}个发音正确。主要需要关注{', '.join(recommendations['priority_phonemes'][:3])}这些音素。
<人类 3>: 我们应该怎么帮助孩子改善发音？
<助手 3>: 建议采用{', '.join(recommendations['therapy_approaches'][:2])}的方法。在家里可以进行{', '.join(recommendations['home_activities'][:3])}。重点训练目标是{', '.join(recommendations['immediate_goals'][:2])}。
<人类 4>: 多长时间需要复查？预计什么时候能看到改善？
<助手 4>: 根据孩子的情况，{', '.join(recommendations['follow_up_plan'])}。通过系统训练，预计{self.get_improvement_timeline(overall_results['severity_level'])}会看到明显改善。
<结束对话>"""
        
        return {
            "dialogue_type": "comprehensive_word_assessment",
            "child_age_months": basic_info["age_months"],
            "accuracy_percentage": overall_results["accuracy_percentage"],
            "severity_level": overall_results["severity_level"],
            "dialogue": dialogue,
            "assessment_summary": {
                "total_words": overall_results["total_words"],
                "correct_words": overall_results["correct_words"],
                "main_issues": recommendations["priority_phonemes"][:3]
            }
        }
    
    def get_improvement_timeline(self, severity: str) -> str:
        """获取改善时间线"""
        timelines = {
            "正常": "继续保持当前水平即可",
            "轻度": "1-2个月的练习",
            "中度": "3-6个月的系统训练",
            "重度": "6个月以上的密集训练"
        }
        return timelines.get(severity, "需要持续专业指导")
    
    def build_dataset(self, num_records: int = 200, output_file: str = "gouyin_assessment_dataset.jsonl"):
        """构建完整数据集"""
        
        print(f"🚀 构建基于52词汇测试的语音构音数据集...")
        print(f"目标记录数: {num_records}")
        print(f"词汇数量: {len(self.word_list)}")
        
        with open(output_file, "w", encoding="utf8") as f:
            for i in range(num_records):
                # 生成年龄（重点关注12-48个月）
                age_months = random.randint(12, 48)
                
                # 生成评估记录
                assessment = self.generate_assessment_record(age_months)
                
                # 生成对话
                dialogue = self.create_dialogue_from_assessment(assessment)
                
                # 合并数据
                dataset_entry = {
                    "record_id": f"GY_{datetime.now().strftime('%Y%m%d')}_{i+1:04d}",
                    "source": "gouyin_52_word_assessment",
                    "created_at": datetime.now().isoformat(),
                    **assessment,
                    **dialogue
                }
                
                f.write(json.dumps(dataset_entry, ensure_ascii=False) + "\n")
                
                if (i + 1) % 50 == 0:
                    print(f"已生成 {i + 1} 条记录...")
        
        print(f"✅ 数据集构建完成！")
        print(f"输出文件: {output_file}")
        print(f"总记录数: {num_records}")

if __name__ == "__main__":
    builder = GouyinDatasetBuilder()
    
    print("📋 Excel结构分析:")
    print(json.dumps(builder.assessment_structure, ensure_ascii=False, indent=2))
    
    if builder.word_list:
        print(f"\n💬 生成示例评估记录:")
        sample_assessment = builder.generate_assessment_record(30)
        sample_dialogue = builder.create_dialogue_from_assessment(sample_assessment)
        
        print("评估结果示例:")
        print(f"- 总词汇数: {sample_assessment['overall_results']['total_words']}")
        print(f"- 正确词汇数: {sample_assessment['overall_results']['correct_words']}")
        print(f"- 准确率: {sample_assessment['overall_results']['accuracy_percentage']}%")
        print(f"- 表现水平: {sample_assessment['overall_results']['performance_level']}")
        
        print(f"\n对话示例:")
        print(sample_dialogue['dialogue'][:500] + "...")
        
        print(f"\n🔧 构建测试数据集:")
        builder.build_dataset(num_records=50, output_file="test_gouyin_dataset.jsonl")
    else:
        print("❌ 未能提取到词汇列表，请检查Excel文件格式")
