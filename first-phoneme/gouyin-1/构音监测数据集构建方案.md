# 婴幼儿语音构音监测数据集构建方案

## 🎯 项目目标

基于`gouyin-1/构音语音能力评估记录表（all）.xlsx`中的52个词汇及目标音，构建专业的婴幼儿语音构音监测数据集，用于微调训练大模型，实现育幼健康监测-指导功能。

## 📊 Excel评估表分析结果

### 表格结构概览
- **总工作表数**: 7个 (Page1-Page7)
- **核心词汇表**: Page1包含52个标准化测试词汇
- **音位对比评估**: Page2-Page5包含不同类型的音位对比测试
- **评分系统**: Page6-Page7包含评分标准和结果记录

### Page1: 52个核心词汇
成功提取了完整的52个词汇及其对应目标音：

| 序号 | 词汇 | 目标音 | 序号 | 词汇 | 目标音 |
|------|------|--------|------|------|--------|
| 1 | 桌 | zh | 27 | 菇 | g |
| 2 | 象 | iang | 28 | 哭 | k |
| 3 | 包 | b | 29 | 壳 | k |
| 4 | 抛 | p | 30 | 纸 | zh |
| 5 | 猫 | m | ... | ... | ... |

### Page2-Page5: 音位对比测试
- **Page2**: 送气/不送气对比 (双唇音、舌尖中音、舌根音)
- **Page3**: 塞音/鼻音对比、擦音/无擦音对比
- **Page4**: 前鼻韵母/后鼻韵母对比
- **Page5**: 声调对比 (一声/二声、一声/三声等)

## 🛠️ 数据集构建工具

### 核心工具: `gouyin_dataset_builder.py`

```python
class GouyinDatasetBuilder:
    """构音评估数据集构建器"""
    
    def __init__(self, excel_file="gouyin-1/构音语音能力评估记录表（all）.xlsx"):
        self.excel_file = excel_file
        self.word_list = []      # 52个词汇
        self.target_phonemes = [] # 对应目标音
```

### 主要功能模块

#### 1. Excel数据提取
- 自动解析7个工作表结构
- 提取52个标准化测试词汇
- 识别对应的目标音素
- 分析音位对比测试项目

#### 2. 评估记录生成
```python
def generate_assessment_record(self, age_months: int) -> Dict[str, Any]:
    """生成完整的评估记录"""
    return {
        "basic_info": {...},           # 儿童基本信息
        "word_assessment": {...},      # 52个词汇的评估结果
        "phoneme_analysis": {...},     # 音素错误模式分析
        "overall_results": {...},      # 整体评估结果
        "recommendations": {...}       # 个性化建议
    }
```

#### 3. 专业对话生成
基于评估结果生成自然的专业对话：
- 初次评估咨询
- 结果解释说明
- 家庭指导建议
- 复查计划制定

## 📋 数据集特点

### 1. 专业性强
- **标准化测试**: 基于52个经过验证的测试词汇
- **科学评估**: 包含音素分析、错误模式识别
- **发展导向**: 符合中文儿童语音发展规律
- **临床相关**: 反映真实的临床评估流程

### 2. 数据结构完整
```json
{
  "record_id": "GY_20250718_0001",
  "basic_info": {
    "child_id": "GY8483",
    "age_months": 29,
    "age_description": "2岁5个月",
    "cooperation_level": "需要鼓励"
  },
  "word_assessment": {
    "桌": {
      "target_phoneme": "zh",
      "actual_production": "zh+额外音",
      "result": "增加",
      "stimulability": "部分可刺激"
    }
  },
  "phoneme_analysis": {
    "error_patterns": {"替代": 4, "省略": 6, "歪曲": 6, "增加": 7},
    "main_error_pattern": "增加",
    "error_consistency": "高度一致"
  },
  "overall_results": {
    "accuracy_percentage": 55.8,
    "performance_level": "接近年龄预期",
    "severity_level": "轻度"
  },
  "recommendations": {
    "priority_phonemes": ["i", "d", "zh"],
    "therapy_approaches": ["精准发音训练", "语音自我监控"],
    "home_activities": ["故事复述", "词汇扩展游戏"]
  }
}
```

### 3. 对话内容自然
```
<开始对话>
<人类 1>: 您好，我想了解一下我家2岁5个月孩子的语音发展情况。我们做了52个词的发音测试。
<助手 1>: 您好！我来为您详细分析孩子的构音评估结果。根据52个词汇的测试，孩子的发音准确率为55.8%，接近年龄预期。
<人类 2>: 这个结果怎么样？孩子的发音问题严重吗？
<助手 2>: 孩子目前的语音清晰度为部分清晰，严重程度为轻度。在52个测试词汇中，有29个发音正确。主要需要关注i, d, zh这些音素。
...
```

## 🚀 实施步骤

### 步骤1: 环境准备
```bash
# 安装依赖
python3 -m pip install pandas openpyxl

# 确认文件存在
ls gouyin-1/构音语音能力评估记录表（all）.xlsx
```

### 步骤2: 运行数据集构建工具
```bash
# 生成测试数据集
python3 gouyin_dataset_builder.py

# 输出结果
# - Excel结构分析报告
# - 52个词汇提取结果
# - 示例评估记录和对话
# - test_gouyin_dataset.jsonl (50条测试记录)
```

### 步骤3: 大规模数据生成
```python
# 修改记录数量
builder = GouyinDatasetBuilder()
builder.build_dataset(
    num_records=2000,  # 生成2000条记录
    output_file="production_gouyin_dataset.jsonl"
)
```

### 步骤4: 数据质量验证
```python
# 运行数据分析工具
python3 dataset_analysis.py test_gouyin_dataset.jsonl
```

## 📈 预期成果

### 数据集规模
- **测试阶段**: 50-200条记录
- **开发阶段**: 500-1000条记录  
- **生产阶段**: 2000-5000条记录

### 覆盖范围
- **年龄段**: 12-48个月全覆盖
- **词汇测试**: 52个标准化词汇
- **音素类型**: 声母、韵母、声调全覆盖
- **错误模式**: 替代、省略、歪曲、增加
- **严重程度**: 正常、轻度、中度、重度

### 应用场景
1. **标准化评估**: 基于52词汇的快速筛查
2. **进展监测**: 定期复查和对比分析
3. **家长指导**: 个性化的家庭练习建议
4. **专业培训**: 语音治疗师的辅助工具

## 💡 技术创新点

### 1. 基于真实评估表
- 直接从临床使用的Excel评估表提取数据
- 保持与实际评估流程的一致性
- 确保专业术语和标准的准确性

### 2. 多维度评估体系
- 词汇水平评估 (52个标准词汇)
- 音素水平分析 (错误模式识别)
- 整体能力评估 (准确率、清晰度)
- 发展水平判断 (年龄适宜性)

### 3. 个性化建议生成
- 基于年龄的发展期望
- 基于错误模式的针对性训练
- 基于严重程度的干预强度
- 基于家庭环境的实用建议

### 4. 自然对话生成
- 模拟真实的临床咨询场景
- 包含专业解释和通俗说明
- 体现家长关注点和专业回应
- 提供具体可行的指导建议

## 🔧 自定义扩展

### 1. 添加新词汇
```python
# 在get_default_word_list()中添加
word_phoneme_pairs.append(("新词", "目标音"))
```

### 2. 调整年龄标准
```python
# 修改get_expected_accuracy()
def get_expected_accuracy(self, age_months: int) -> float:
    # 自定义年龄-准确率对应关系
```

### 3. 扩展错误模式
```python
# 在generate_error_production()中添加新的错误类型
```

### 4. 增加对话类型
```python
def create_progress_review_dialogue(self, assessment):
    # 实现进展复查对话
```

## 📊 质量保证

### 1. 数据验证
- 年龄合理性检查 (12-48个月)
- 准确率范围验证 (0-100%)
- 词汇完整性检查 (52个词汇)
- 对话逻辑性验证

### 2. 专业审核
- 语音病理学专家审核
- 儿童发展专家验证
- 临床实践经验整合
- 家长反馈收集

### 3. 持续改进
- 定期更新词汇列表
- 优化错误模式识别
- 改进建议生成算法
- 增强对话自然性

## 🎯 下一步计划

### 短期目标 (1-2周)
1. ✅ 完成Excel数据提取和分析
2. ✅ 构建基础数据集生成工具
3. ✅ 生成测试数据集验证可行性
4. 🔄 邀请专家审核数据质量

### 中期目标 (1个月)
1. 生成1000-2000条高质量训练数据
2. 完善数据质量控制流程
3. 开始大模型微调实验
4. 收集初步效果反馈

### 长期目标 (3个月)
1. 构建完整的评估-训练-应用闭环
2. 开发实时评估和指导系统
3. 扩展到更多年龄段和语言环境
4. 建立持续学习和更新机制

---

通过这套基于真实临床评估表的数据集构建方案，我们能够为婴幼儿语音构音监测领域提供专业、准确、实用的AI训练数据，推动育幼健康监测-指导功能的智能化发展！
