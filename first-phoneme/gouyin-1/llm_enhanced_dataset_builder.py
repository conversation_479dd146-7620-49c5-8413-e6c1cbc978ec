#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

"""
基于LLM增强的婴幼儿语音构音监测数据集构建工具
结合Qwen API生成高质量、多样化的专业数据集
"""

import json
import random
import requests
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import pandas as pd

class LLMEnhancedDatasetBuilder:
    """LLM增强的数据集构建器"""
    
    def __init__(self, qwen_api_key: str, qwen_api_url: str = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"):
        self.qwen_api_key = qwen_api_key
        self.qwen_api_url = qwen_api_url
        self.excel_file = "gouyin-1/构音语音能力评估记录表（all）.xlsx"
        
        # 从Excel提取的52个词汇
        self.word_list = self.load_word_list()
        
        # 专业知识库
        self.professional_knowledge = self.build_knowledge_base()
        
    def load_word_list(self) -> List[Dict[str, str]]:
        """从Excel加载52个词汇列表"""
        try:
            df = pd.read_excel(self.excel_file, sheet_name='Page1', header=None)
            word_list = []
            
            for i in range(1, len(df)):
                if len(df.columns) > 3:
                    word = str(df.iloc[i, 1]).strip() if pd.notna(df.iloc[i, 1]) else ""
                    target_phoneme = str(df.iloc[i, 3]).strip() if pd.notna(df.iloc[i, 3]) else ""
                    
                    if word and any('\u4e00' <= char <= '\u9fff' for char in word):
                        word_list.append({
                            "word": word,
                            "target_phoneme": target_phoneme,
                            "index": i
                        })
            
            print(f"✅ 成功加载 {len(word_list)} 个词汇")
            return word_list[:52]  # 确保只取52个
            
        except Exception as e:
            print(f"❌ 加载词汇失败: {e}")
            return self.get_default_word_list()
    
    def get_default_word_list(self) -> List[Dict[str, str]]:
        """获取默认词汇列表"""
        default_words = [
            {"word": "包", "target_phoneme": "b", "index": 1},
            {"word": "抛", "target_phoneme": "p", "index": 2},
            {"word": "猫", "target_phoneme": "m", "index": 3},
            {"word": "刀", "target_phoneme": "d", "index": 4},
            {"word": "套", "target_phoneme": "t", "index": 5},
            # ... 可以继续添加更多默认词汇
        ]
        return default_words
    
    def build_knowledge_base(self) -> Dict[str, Any]:
        """构建专业知识库"""
        return {
            "developmental_milestones": {
                12: {"expected_sounds": ["p", "b", "m", "w"], "clarity_rate": 25},
                18: {"expected_sounds": ["p", "b", "m", "w", "n", "t", "d"], "clarity_rate": 50},
                24: {"expected_sounds": ["p", "b", "m", "w", "n", "t", "d", "k", "g"], "clarity_rate": 65},
                30: {"expected_sounds": ["p", "b", "m", "w", "n", "t", "d", "k", "g", "f"], "clarity_rate": 75},
                36: {"expected_sounds": ["p", "b", "m", "w", "n", "t", "d", "k", "g", "f", "l"], "clarity_rate": 85},
                42: {"expected_sounds": ["p", "b", "m", "w", "n", "t", "d", "k", "g", "f", "l", "s", "sh"], "clarity_rate": 90},
                48: {"expected_sounds": ["p", "b", "m", "w", "n", "t", "d", "k", "g", "f", "l", "s", "sh", "r", "z"], "clarity_rate": 95}
            },
            "error_patterns": {
                "替代": "用一个音素替代另一个音素，如用/t/替代/k/",
                "省略": "省略某个音素或音节，如'包'说成'ao'",
                "歪曲": "发音不准确但仍可识别，如舌位不正确",
                "增加": "在正确发音基础上增加额外音素"
            },
            "therapy_approaches": [
                "视觉提示法", "听觉训练", "触觉提示", "口部运动训练",
                "最小对比练习", "音素意识训练", "语音自我监控"
            ],
            "home_activities": [
                "亲子阅读", "语音游戏", "儿歌朗诵", "故事复述",
                "词汇扩展", "发音练习", "口部按摩"
            ]
        }
    
    def call_qwen_api(self, prompt: str, max_tokens: int = 2000) -> Optional[str]:
        """调用Qwen API"""
        headers = {
            "Authorization": f"Bearer {self.qwen_api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "qwen-turbo",
            "input": {
                "messages": [
                    {
                        "role": "system",
                        "content": "你是一位专业的语音病理学家和儿童发展专家，具有丰富的婴幼儿语音构音评估和治疗经验。"
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ]
            },
            "parameters": {
                "max_tokens": max_tokens,
                "temperature": 0.7,
                "top_p": 0.8
            }
        }
        
        try:
            response = requests.post(self.qwen_api_url, headers=headers, json=data, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if "output" in result and "text" in result["output"]:
                return result["output"]["text"].strip()
            else:
                print(f"❌ API响应格式异常: {result}")
                return None
                
        except Exception as e:
            print(f"❌ API调用失败: {e}")
            return None
    
    def generate_child_profile_with_llm(self, age_months: int) -> Dict[str, Any]:
        """使用LLM生成儿童档案"""
        
        prompt = f"""
请为一个{age_months}个月大的儿童生成一个真实的语音发展档案。

要求：
1. 基本信息：性别、家庭语言环境、发展关注点
2. 语言发展史：首次发音时间、词汇量发展、句子发展
3. 医疗史：听力状况、口腔结构、神经发育
4. 家庭环境：父母教育背景、语言刺激情况、兄弟姐妹

请以JSON格式返回，包含以下字段：
- gender: 性别
- language_environment: 语言环境
- developmental_concerns: 发展关注点
- first_words_age: 首次说话年龄(月)
- vocabulary_size: 当前词汇量
- hearing_status: 听力状况
- oral_structure: 口腔结构
- family_education: 家庭教育背景
- language_stimulation: 语言刺激水平

请确保信息符合{age_months}个月儿童的发展特点。
"""
        
        response = self.call_qwen_api(prompt)
        if response:
            try:
                # 尝试解析JSON
                profile_data = json.loads(response)
                return profile_data
            except:
                # 如果解析失败，使用默认生成
                pass
        
        # 默认生成
        return {
            "gender": random.choice(["男", "女"]),
            "language_environment": random.choice(["单语", "双语", "多语"]),
            "developmental_concerns": random.choice(["发音不清", "说话晚", "无特殊关注"]),
            "first_words_age": random.randint(8, 18),
            "vocabulary_size": random.randint(10, 500),
            "hearing_status": "正常",
            "oral_structure": "正常",
            "family_education": random.choice(["高中", "大学", "研究生"]),
            "language_stimulation": random.choice(["丰富", "一般", "较少"])
        }
    
    def generate_assessment_results_with_llm(self, age_months: int, child_profile: Dict) -> Dict[str, Any]:
        """使用LLM生成评估结果"""
        
        # 构建词汇列表字符串
        word_list_str = "、".join([f"{w['word']}({w['target_phoneme']})" for w in self.word_list[:10]])
        
        prompt = f"""
作为语音病理学专家，请为一个{age_months}个月大的{child_profile['gender']}童生成52个词汇的语音构音评估结果。

儿童背景：
- 年龄：{age_months}个月
- 语言环境：{child_profile['language_environment']}
- 家长关注：{child_profile['developmental_concerns']}
- 词汇量：{child_profile['vocabulary_size']}

测试词汇示例：{word_list_str}等52个词汇

请生成：
1. 整体准确率（考虑年龄发展水平）
2. 主要错误模式（替代、省略、歪曲、增加）
3. 问题音素列表（3-5个）
4. 语音清晰度等级
5. 表现水平评估

请以JSON格式返回：
{{
    "accuracy_percentage": 数值,
    "main_error_pattern": "主要错误模式",
    "problem_phonemes": ["音素1", "音素2", "音素3"],
    "intelligibility": "清晰度等级",
    "performance_level": "表现水平",
    "error_distribution": {{"替代": 数量, "省略": 数量, "歪曲": 数量, "增加": 数量}}
}}

确保结果符合{age_months}个月儿童的发展特点。
"""
        
        response = self.call_qwen_api(prompt)
        if response:
            try:
                return json.loads(response)
            except:
                pass
        
        # 默认生成逻辑
        expected_accuracy = self.get_expected_accuracy(age_months)
        actual_accuracy = random.uniform(expected_accuracy * 0.7, expected_accuracy * 1.2)
        
        return {
            "accuracy_percentage": round(actual_accuracy, 1),
            "main_error_pattern": random.choice(["替代", "省略", "歪曲", "增加"]),
            "problem_phonemes": random.sample(["zh", "ch", "sh", "r", "l", "s", "z"], 3),
            "intelligibility": self.determine_intelligibility(actual_accuracy),
            "performance_level": self.determine_performance_level(actual_accuracy, expected_accuracy),
            "error_distribution": {
                "替代": random.randint(0, 10),
                "省略": random.randint(0, 8),
                "歪曲": random.randint(0, 6),
                "增加": random.randint(0, 4)
            }
        }
    
    def generate_recommendations_with_llm(self, age_months: int, assessment_results: Dict, child_profile: Dict) -> Dict[str, Any]:
        """使用LLM生成个性化建议"""
        
        prompt = f"""
基于以下评估结果，为{age_months}个月大的儿童制定个性化的语音训练建议：

评估结果：
- 准确率：{assessment_results['accuracy_percentage']}%
- 主要错误：{assessment_results['main_error_pattern']}
- 问题音素：{', '.join(assessment_results['problem_phonemes'])}
- 表现水平：{assessment_results['performance_level']}

儿童背景：
- 语言环境：{child_profile['language_environment']}
- 家庭教育：{child_profile['family_education']}
- 语言刺激：{child_profile['language_stimulation']}

请提供：
1. 即时训练目标（2-3个）
2. 推荐治疗方法（3-4个）
3. 家庭练习活动（4-5个）
4. 随访计划
5. 转介建议（如需要）

请以JSON格式返回：
{{
    "immediate_goals": ["目标1", "目标2"],
    "therapy_approaches": ["方法1", "方法2", "方法3"],
    "home_activities": ["活动1", "活动2", "活动3", "活动4"],
    "follow_up_plan": "随访计划",
    "referral_needed": true/false,
    "referral_type": "转介类型"
}}
"""
        
        response = self.call_qwen_api(prompt)
        if response:
            try:
                return json.loads(response)
            except:
                pass
        
        # 默认生成
        return {
            "immediate_goals": ["改善目标音素发音", "提高语音清晰度"],
            "therapy_approaches": ["视觉提示法", "听觉训练", "口部运动"],
            "home_activities": ["亲子阅读", "发音游戏", "词汇练习", "儿歌朗诵"],
            "follow_up_plan": "3个月后复查",
            "referral_needed": False,
            "referral_type": ""
        }
    
    def generate_professional_dialogue_with_llm(self, child_profile: Dict, assessment_results: Dict, recommendations: Dict) -> str:
        """使用LLM生成专业对话"""
        
        age_months = child_profile.get('age_months', 24)
        age_desc = f"{age_months // 12}岁{age_months % 12}个月"
        
        prompt = f"""
请生成一段真实的语音病理学家与家长的咨询对话，基于以下信息：

儿童信息：
- 年龄：{age_desc}
- 性别：{child_profile['gender']}
- 家长关注：{child_profile['developmental_concerns']}

评估结果：
- 52词汇测试准确率：{assessment_results['accuracy_percentage']}%
- 主要问题：{assessment_results['main_error_pattern']}
- 语音清晰度：{assessment_results['intelligibility']}
- 表现水平：{assessment_results['performance_level']}

建议：
- 治疗方法：{', '.join(recommendations['therapy_approaches'][:2])}
- 家庭活动：{', '.join(recommendations['home_activities'][:3])}
- 随访计划：{recommendations['follow_up_plan']}

要求：
1. 对话要自然、专业
2. 包含4-5轮对话
3. 体现家长的担心和专家的专业解答
4. 提供具体可行的建议
5. 使用<开始对话>和<结束对话>标记
6. 每轮对话用<人类 X>和<助手 X>标记

请生成完整的对话内容。
"""
        
        response = self.call_qwen_api(prompt, max_tokens=1500)
        if response:
            return response
        
        # 默认对话模板
        return f"""<开始对话>
<人类 1>: 您好，我想了解一下我家{age_desc}孩子的语音发展情况。
<助手 1>: 您好！根据52个词汇的测试结果，孩子的发音准确率为{assessment_results['accuracy_percentage']}%，{assessment_results['performance_level']}。
<人类 2>: 这个结果怎么样？需要治疗吗？
<助手 2>: 孩子目前的语音清晰度为{assessment_results['intelligibility']}，主要问题是{assessment_results['main_error_pattern']}。建议采用{recommendations['therapy_approaches'][0]}等方法。
<人类 3>: 我在家里可以做些什么？
<助手 3>: 建议您进行{', '.join(recommendations['home_activities'][:3])}。每天练习15-20分钟即可。
<人类 4>: 多久能看到改善？
<助手 4>: 根据孩子的情况，{recommendations['follow_up_plan']}。通过系统训练，预计2-3个月会有明显进步。
<结束对话>"""
    
    def get_expected_accuracy(self, age_months: int) -> float:
        """获取年龄对应的期望准确率"""
        milestones = self.professional_knowledge["developmental_milestones"]
        for milestone_age in sorted(milestones.keys(), reverse=True):
            if age_months >= milestone_age:
                return milestones[milestone_age]["clarity_rate"]
        return 25.0
    
    def determine_intelligibility(self, accuracy: float) -> str:
        """确定清晰度等级"""
        if accuracy >= 90:
            return "高度清晰"
        elif accuracy >= 75:
            return "大部分清晰"
        elif accuracy >= 50:
            return "部分清晰"
        else:
            return "难以理解"
    
    def determine_performance_level(self, accuracy: float, expected: float) -> str:
        """确定表现水平"""
        if accuracy >= expected:
            return "符合年龄预期"
        elif accuracy >= expected * 0.8:
            return "接近年龄预期"
        elif accuracy >= expected * 0.6:
            return "低于年龄预期"
        else:
            return "明显低于年龄预期"
    
    def generate_enhanced_record(self, age_months: int) -> Dict[str, Any]:
        """生成LLM增强的评估记录"""
        
        print(f"🤖 正在为{age_months}个月儿童生成LLM增强记录...")
        
        # 1. 生成儿童档案
        child_profile = self.generate_child_profile_with_llm(age_months)
        child_profile['age_months'] = age_months
        child_profile['child_id'] = f"LLM{random.randint(1000, 9999)}"
        
        # 2. 生成评估结果
        assessment_results = self.generate_assessment_results_with_llm(age_months, child_profile)
        
        # 3. 生成个性化建议
        recommendations = self.generate_recommendations_with_llm(age_months, assessment_results, child_profile)
        
        # 4. 生成专业对话
        dialogue = self.generate_professional_dialogue_with_llm(child_profile, assessment_results, recommendations)
        
        # 5. 组装完整记录
        record = {
            "record_id": f"LLM_{datetime.now().strftime('%Y%m%d')}_{random.randint(1000, 9999)}",
            "source": "llm_enhanced_gouyin_assessment",
            "created_at": datetime.now().isoformat(),
            "child_profile": child_profile,
            "assessment_results": assessment_results,
            "recommendations": recommendations,
            "dialogue": dialogue,
            "word_list_used": len(self.word_list),
            "llm_enhanced": True
        }
        
        return record
    
    def build_enhanced_dataset(self, num_records: int = 100, output_file: str = "llm_enhanced_dataset.jsonl"):
        """构建LLM增强的数据集"""
        
        print(f"🚀 开始构建LLM增强的语音构音数据集...")
        print(f"目标记录数: {num_records}")
        print(f"使用词汇数: {len(self.word_list)}")
        
        successful_records = 0
        failed_records = 0
        
        with open(output_file, "w", encoding="utf8") as f:
            for i in range(num_records):
                try:
                    # 生成年龄（重点关注12-48个月）
                    age_months = random.randint(12, 48)
                    
                    # 生成LLM增强记录
                    record = self.generate_enhanced_record(age_months)
                    
                    # 写入文件
                    f.write(json.dumps(record, ensure_ascii=False) + "\n")
                    successful_records += 1
                    
                    if (i + 1) % 10 == 0:
                        print(f"已生成 {i + 1} 条记录... (成功: {successful_records}, 失败: {failed_records})")
                    
                    # API调用间隔，避免频率限制
                    time.sleep(1)
                    
                except Exception as e:
                    print(f"❌ 生成第{i+1}条记录失败: {e}")
                    failed_records += 1
                    continue
        
        print(f"✅ LLM增强数据集构建完成！")
        print(f"输出文件: {output_file}")
        print(f"成功记录: {successful_records}")
        print(f"失败记录: {failed_records}")
        print(f"成功率: {successful_records/(successful_records+failed_records)*100:.1f}%")

if __name__ == "__main__":
    # 使用示例
    API_KEY = "your-qwen-api-key-here"  # 请替换为实际的API密钥
    
    if API_KEY == "your-qwen-api-key-here":
        print("❌ 请先设置Qwen API密钥")
        print("请在代码中将 'your-qwen-api-key-here' 替换为实际的API密钥")
    else:
        builder = LLMEnhancedDatasetBuilder(API_KEY)
        
        # 生成小规模测试数据集
        print("🔧 构建LLM增强测试数据集:")
        builder.build_enhanced_dataset(num_records=5, output_file="test_llm_enhanced_dataset.jsonl")
