#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

"""
LLM增强数据集构建配置文件
"""

import os
from typing import Dict, List, Any

class LLMConfig:
    """LLM配置管理"""
    
    def __init__(self):
        # API配置
        self.QWEN_API_KEY = "sk-89f5a10520814825ad34c7eac4533ba3"
        self.QWEN_API_URL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        
        # 模型参数
        self.MODEL_NAME = "qwen-turbo"
        self.MAX_TOKENS = 2000
        self.TEMPERATURE = 0.7
        self.TOP_P = 0.8
        
        # 数据集配置
        self.BATCH_SIZE = 10
        self.API_DELAY = 1.0  # 秒
        self.MAX_RETRIES = 3
        
    def get_system_prompts(self) -> Dict[str, str]:
        """获取系统提示词"""
        return {
            "expert": "你是一位资深的语音病理学家和儿童发展专家，拥有20年的临床经验，专门从事婴幼儿语音构音评估和治疗。你熟悉中文语音发展规律，能够提供专业、准确、实用的评估和建议。",
            
            "assessor": "你是一位专业的语音构音评估师，具有丰富的52词汇标准化测试经验。你能够根据儿童的年龄、发展背景和测试表现，提供准确的评估结果和科学的分析。",
            
            "therapist": "你是一位经验丰富的语音治疗师，擅长为不同年龄段的儿童制定个性化的训练方案。你的建议既专业又实用，能够帮助家长在家中有效地进行语音训练。",
            
            "consultant": "你是一位专业的儿童语音发展咨询师，善于与家长沟通，能够用通俗易懂的语言解释专业概念，提供温暖而专业的指导。"
        }
    
    def get_prompt_templates(self) -> Dict[str, str]:
        """获取提示词模板"""
        return {
            "child_profile": """
请为一个{age_months}个月大的儿童生成一个真实、详细的语音发展档案。

要求：
1. 基本信息要符合该年龄段的发展特点
2. 发展史要有逻辑性和连贯性
3. 家庭环境要多样化但合理
4. 关注点要反映真实的家长担忧

请以JSON格式返回，包含以下字段：
- gender: 性别 (男/女)
- language_environment: 语言环境 (单语/双语/多语)
- developmental_concerns: 发展关注点
- first_words_age: 首次说话年龄(月)
- vocabulary_size: 当前词汇量
- hearing_status: 听力状况
- oral_structure: 口腔结构
- family_education: 家庭教育背景
- language_stimulation: 语言刺激水平
- cooperation_level: 配合程度

确保所有信息都符合{age_months}个月儿童的发展特点。
""",
            
            "assessment_results": """
作为专业的语音病理学家，请为以下儿童生成52个词汇的语音构音评估结果：

儿童信息：
- 年龄：{age_months}个月 ({age_description})
- 性别：{gender}
- 语言环境：{language_environment}
- 家长关注：{developmental_concerns}
- 词汇量：{vocabulary_size}
- 配合程度：{cooperation_level}

评估要求：
1. 准确率要符合年龄发展规律
2. 错误模式要有专业依据
3. 问题音素要基于中文语音发展特点
4. 评估结果要前后一致

请以JSON格式返回：
{{
    "accuracy_percentage": 准确率数值,
    "main_error_pattern": "主要错误模式",
    "problem_phonemes": ["问题音素列表"],
    "intelligibility": "语音清晰度等级",
    "performance_level": "表现水平评估",
    "error_distribution": {{"替代": 数量, "省略": 数量, "歪曲": 数量, "增加": 数量}},
    "stimulability": "可刺激性评估",
    "consistency": "错误一致性"
}}
""",
            
            "recommendations": """
基于以下评估结果，请为{age_months}个月大的儿童制定专业的个性化语音训练建议：

评估结果：
- 准确率：{accuracy_percentage}%
- 主要错误：{main_error_pattern}
- 问题音素：{problem_phonemes}
- 表现水平：{performance_level}
- 语音清晰度：{intelligibility}

儿童背景：
- 语言环境：{language_environment}
- 家庭教育：{family_education}
- 语言刺激：{language_stimulation}
- 配合程度：{cooperation_level}

请提供：
1. 即时训练目标（2-3个具体目标）
2. 推荐治疗方法（3-4个专业方法）
3. 家庭练习活动（4-5个实用活动）
4. 训练频率和时长建议
5. 随访计划
6. 转介建议（如需要）

请以JSON格式返回：
{{
    "immediate_goals": ["具体目标1", "具体目标2"],
    "therapy_approaches": ["专业方法1", "专业方法2"],
    "home_activities": ["家庭活动1", "家庭活动2"],
    "training_schedule": "训练频率和时长",
    "follow_up_plan": "随访计划",
    "referral_needed": true/false,
    "referral_type": "转介类型",
    "expected_improvement": "预期改善时间"
}}
""",
            
            "dialogue": """
请生成一段真实、自然的语音病理学家与家长的专业咨询对话：

场景设置：
- 儿童年龄：{age_description}
- 性别：{gender}
- 家长关注：{developmental_concerns}
- 评估完成：52个词汇测试

评估结果：
- 准确率：{accuracy_percentage}%
- 主要问题：{main_error_pattern}
- 语音清晰度：{intelligibility}
- 表现水平：{performance_level}
- 问题音素：{problem_phonemes}

建议内容：
- 治疗方法：{therapy_approaches}
- 家庭活动：{home_activities}
- 随访计划：{follow_up_plan}

对话要求：
1. 4-6轮自然对话
2. 体现家长的真实担忧和疑问
3. 专家回答要专业但易懂
4. 包含具体可行的建议
5. 语言温暖、鼓励性
6. 使用<开始对话>和<结束对话>标记
7. 每轮用<人类 X>和<助手 X>标记

请生成完整的专业咨询对话。
"""
        }
    
    def get_quality_criteria(self) -> Dict[str, Any]:
        """获取质量标准"""
        return {
            "age_accuracy_ranges": {
                12: (20, 40),
                18: (35, 55),
                24: (50, 70),
                30: (60, 80),
                36: (70, 90),
                42: (80, 95),
                48: (85, 98)
            },
            
            "required_fields": [
                "child_profile", "assessment_results", "recommendations", "dialogue"
            ],
            
            "dialogue_min_length": 300,
            "dialogue_max_length": 1500,
            
            "phoneme_categories": {
                "early": ["p", "b", "m", "w", "h"],
                "middle": ["t", "d", "n", "l", "k", "g", "f"],
                "late": ["s", "z", "sh", "zh", "ch", "r"]
            }
        }
    
    def validate_config(self) -> bool:
        """验证配置"""
        if not self.QWEN_API_KEY:
            print("❌ 未设置QWEN_API_KEY环境变量")
            return False
        
        if len(self.QWEN_API_KEY) < 20:
            print("❌ API密钥格式不正确")
            return False
        
        print("✅ 配置验证通过")
        return True

# 全局配置实例
config = LLMConfig()
