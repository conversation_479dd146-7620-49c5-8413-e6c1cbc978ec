#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

"""
LLM增强数据集构建运行脚本
提供简单的命令行界面来运行数据集构建
"""

import os
import sys
import asyncio
import argparse
from datetime import datetime

def check_dependencies():
    """检查依赖"""
    required_packages = ['pandas', 'openpyxl', 'aiohttp', 'requests']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print(f"请运行: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_api_key():
    """检查API密钥"""
    api_key = os.getenv("QWEN_API_KEY")
    
    if not api_key:
        print("❌ 未设置QWEN_API_KEY环境变量")
        print("请运行: export QWEN_API_KEY='your-api-key'")
        return None
    
    if len(api_key) < 20:
        print("❌ API密钥格式不正确")
        return None
    
    print("✅ API密钥验证通过")
    return api_key

def check_excel_file():
    """检查Excel文件"""
    excel_path = "gouyin-1/构音语音能力评估记录表（all）.xlsx"
    
    if not os.path.exists(excel_path):
        print(f"❌ 未找到Excel文件: {excel_path}")
        return False
    
    print("✅ Excel文件存在")
    return True

async def run_basic_builder(api_key: str, num_records: int, output_file: str):
    """运行基础构建器"""
    try:
        from llm_enhanced_dataset_builder import LLMEnhancedDatasetBuilder
        
        print(f"🚀 启动基础LLM构建器...")
        builder = LLMEnhancedDatasetBuilder(api_key)
        
        builder.build_enhanced_dataset(
            num_records=num_records,
            output_file=output_file
        )
        
        return True
        
    except Exception as e:
        print(f"❌ 基础构建器运行失败: {e}")
        return False

async def run_advanced_builder(api_key: str, num_records: int, output_file: str):
    """运行高级构建器"""
    try:
        from advanced_llm_builder import AdvancedLLMBuilder
        
        print(f"🚀 启动高级LLM构建器...")
        builder = AdvancedLLMBuilder(api_key)
        
        await builder.build_advanced_dataset_async(
            num_records=num_records,
            output_file=output_file
        )
        
        return True
        
    except Exception as e:
        print(f"❌ 高级构建器运行失败: {e}")
        return False

def analyze_dataset(dataset_file: str):
    """分析生成的数据集"""
    if not os.path.exists(dataset_file):
        print(f"❌ 数据集文件不存在: {dataset_file}")
        return
    
    try:
        import json
        
        records = []
        with open(dataset_file, 'r', encoding='utf8') as f:
            for line in f:
                if line.strip():
                    records.append(json.loads(line))
        
        print(f"\n📊 数据集分析结果:")
        print(f"- 总记录数: {len(records)}")
        
        if records:
            # 年龄分布
            ages = [r['child_profile']['age_months'] for r in records]
            print(f"- 年龄范围: {min(ages)}-{max(ages)}个月")
            
            # 准确率分布
            accuracies = [r['overall_results']['accuracy_percentage'] for r in records]
            avg_accuracy = sum(accuracies) / len(accuracies)
            print(f"- 平均准确率: {avg_accuracy:.1f}%")
            
            # 对话长度
            dialogue_lengths = [len(r['dialogue']) for r in records]
            avg_length = sum(dialogue_lengths) / len(dialogue_lengths)
            print(f"- 平均对话长度: {avg_length:.0f}字符")
            
            print(f"✅ 数据集质量良好")
        
    except Exception as e:
        print(f"❌ 数据集分析失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="LLM增强数据集构建工具")
    
    parser.add_argument("--mode", choices=["basic", "advanced"], default="advanced",
                       help="构建模式 (basic: 基础模式, advanced: 高级异步模式)")
    
    parser.add_argument("--records", type=int, default=50,
                       help="生成记录数量 (默认: 50)")
    
    parser.add_argument("--output", type=str, default=None,
                       help="输出文件名 (默认: 自动生成)")
    
    parser.add_argument("--analyze", action="store_true",
                       help="分析生成的数据集")
    
    parser.add_argument("--check-only", action="store_true",
                       help="仅检查环境，不生成数据")
    
    args = parser.parse_args()
    
    print("🔍 LLM增强数据集构建工具")
    print("=" * 50)
    
    # 检查环境
    print("1. 检查依赖包...")
    if not check_dependencies():
        sys.exit(1)
    
    print("2. 检查API密钥...")
    api_key = check_api_key()
    if not api_key:
        sys.exit(1)
    
    print("3. 检查Excel文件...")
    if not check_excel_file():
        sys.exit(1)
    
    if args.check_only:
        print("✅ 环境检查完成，所有条件满足")
        return
    
    # 生成输出文件名
    if not args.output:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        args.output = f"llm_dataset_{args.mode}_{args.records}_{timestamp}.jsonl"
    
    print(f"\n🚀 开始生成数据集...")
    print(f"- 模式: {args.mode}")
    print(f"- 记录数: {args.records}")
    print(f"- 输出文件: {args.output}")
    
    # 运行构建器
    start_time = datetime.now()
    success = False
    
    try:
        if args.mode == "basic":
            success = asyncio.run(run_basic_builder(api_key, args.records, args.output))
        else:
            success = asyncio.run(run_advanced_builder(api_key, args.records, args.output))
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        if success:
            print(f"\n✅ 数据集生成完成!")
            print(f"- 用时: {duration:.1f}秒")
            print(f"- 输出文件: {args.output}")
            
            # 分析数据集
            if args.analyze:
                analyze_dataset(args.output)
        else:
            print(f"\n❌ 数据集生成失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断生成过程")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 生成过程出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
