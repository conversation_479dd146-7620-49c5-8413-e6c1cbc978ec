#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

import json
import random
import numpy as np

def analyze_merged_file():
    """分析合并后的文件"""
    
    with open("merged_references.jsonl", "r", encoding="utf8") as f:
        references = [json.loads(line.strip()) for line in f if line.strip()]
    
    print(f"合并后的文件包含 {len(references)} 个条目")
    
    for i, ref in enumerate(references):
        word_count = len(ref["desc"].split())
        print(f"条目 {i+1}: {ref['title']} - {word_count} 单词")
        print(f"内容预览: {ref['desc'][:200]}...")
        print()

if __name__ == "__main__":
    analyze_merged_file()
