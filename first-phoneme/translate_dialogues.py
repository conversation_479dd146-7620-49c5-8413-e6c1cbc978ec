#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

import json
import re

def translate_dialogue_content(dialogue_text):
    """翻译对话内容"""
    
    # 定义翻译映射
    translations = {
        # 基本对话结构
        "<start_chat>": "<开始对话>",
        "<end_chat>": "<结束对话>",
        "Human": "人类",
        "Assistant": "助手",
        "word count": "字数要求",
        "words": "字",
        
        # 语音治疗相关术语
        "speech therapy": "语音治疗",
        "speech disorders": "语音障碍",
        "articulation": "构音",
        "pronunciation": "发音",
        "phoneme": "音素",
        "sound": "音",
        "therapy": "治疗",
        "pathologist": "病理学家",
        "speech-language pathologist": "语音语言病理学家",
        "mirror": "镜子",
        "tongue": "舌头",
        "lips": "嘴唇",
        "teeth": "牙齿",
        "mouth": "嘴巴",
        "voice": "声音",
        "air": "气流",
        "breathing": "呼吸",
        "exercises": "练习",
        "practice": "练习",
        "child": "孩子",
        "children": "孩子们",
        "kid": "小孩",
        "kids": "小孩们",
        
        # 常见问题和回答
        "How can I": "我如何能",
        "How do I": "我如何",
        "What is": "什么是",
        "What are": "什么是",
        "Can you": "你能",
        "Could you": "你能",
        "Please": "请",
        "Thank you": "谢谢",
        "You're welcome": "不客气",
        "That's great": "太好了",
        "Good job": "做得好",
        "Well done": "做得很好",
        
        # 其他常见词汇
        "help": "帮助",
        "learn": "学习",
        "teach": "教",
        "understand": "理解",
        "explain": "解释",
        "example": "例子",
        "try": "尝试",
        "practice": "练习",
        "improve": "改善",
        "better": "更好",
        "difficult": "困难",
        "easy": "容易",
        "important": "重要",
        "useful": "有用",
        "effective": "有效",
        "problem": "问题",
        "solution": "解决方案",
        "method": "方法",
        "technique": "技巧",
        "activity": "活动",
        "game": "游戏",
        "fun": "有趣",
        "patient": "耐心",
        "consistent": "坚持",
        "progress": "进步",
        "development": "发展",
        "skill": "技能",
        "ability": "能力",
    }
    
    # 执行翻译
    translated = dialogue_text
    for english, chinese in translations.items():
        # 使用正则表达式进行不区分大小写的替换
        pattern = re.compile(re.escape(english), re.IGNORECASE)
        translated = pattern.sub(chinese, translated)
    
    return translated

def translate_jsonl_file(input_file, output_file):
    """翻译整个JSONL文件"""
    
    translated_dialogues = []
    
    with open(input_file, "r", encoding="utf8") as f:
        for line_num, line in enumerate(f, 1):
            if not line.strip():
                continue
                
            try:
                dialogue_data = json.loads(line.strip())
                
                # 翻译对话内容
                if "dialogue" in dialogue_data:
                    original_dialogue = dialogue_data["dialogue"]
                    translated_dialogue = translate_dialogue_content(original_dialogue)
                    dialogue_data["dialogue_chinese"] = translated_dialogue
                    dialogue_data["dialogue_original"] = original_dialogue
                
                # 翻译标题
                if "title" in dialogue_data and dialogue_data["title"]:
                    dialogue_data["title_chinese"] = translate_dialogue_content(dialogue_data["title"])
                    dialogue_data["title_original"] = dialogue_data["title"]
                
                # 翻译参考内容
                if "reference" in dialogue_data and dialogue_data["reference"]:
                    dialogue_data["reference_chinese"] = translate_dialogue_content(dialogue_data["reference"])
                    dialogue_data["reference_original"] = dialogue_data["reference"]
                
                translated_dialogues.append(dialogue_data)
                
                if line_num % 50 == 0:
                    print(f"已处理 {line_num} 个对话...")
                    
            except json.JSONDecodeError as e:
                print(f"第 {line_num} 行JSON解析错误: {e}")
                continue
    
    # 保存翻译结果
    with open(output_file, "w", encoding="utf8") as f:
        for dialogue in translated_dialogues:
            f.write(json.dumps(dialogue, ensure_ascii=False) + "\n")
    
    print(f"\n✅ 翻译完成！")
    print(f"原文件: {input_file}")
    print(f"翻译文件: {output_file}")
    print(f"总共处理了 {len(translated_dialogues)} 个对话")
    
    return len(translated_dialogues)

def show_sample_translation():
    """显示翻译示例"""
    print("📖 翻译示例:")
    
    sample_text = """<start_chat><Human 1>:(word count: 20 words)How do I say the /f/ sound? <Assistant 1>:(word count: 200 words)To make the /f/ sound, you don't need to turn your voice on like a light switch. Just blow air out through your teeth, like you're cooling a bowl of soup.<end_chat>"""
    
    translated = translate_dialogue_content(sample_text)
    
    print("原文:")
    print(sample_text[:200] + "...")
    print("\n译文:")
    print(translated[:200] + "...")

if __name__ == "__main__":
    print("🌐 开始翻译对话内容...")
    
    # 显示翻译示例
    show_sample_translation()
    
    print("\n" + "="*50)
    
    # 执行翻译
    count = translate_jsonl_file(
        "output-refgpt-qwen.jsonl",
        "output-refgpt-qwen-chinese.jsonl"
    )
    
    print(f"\n💡 提示:")
    print("- 翻译结果保存在 output-refgpt-qwen-chinese.jsonl")
    print("- 每个对话都包含原文和译文")
    print("- 可以根据需要进一步调整翻译质量")
