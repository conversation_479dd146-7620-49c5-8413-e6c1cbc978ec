2025-07-17 15:21:38,044 - root - WARNING - 
                    **************************************
                    All tasks finished. Breaking main loop.
                    **************************************
                    
2025-07-17 15:21:38,044 - root - INFO - Parallel processing complete. Results saved to output-refgpt.jsonl
2025-07-17 15:23:21,478 - root - WARNING - 
                    **************************************
                    All tasks finished. Breaking main loop.
                    **************************************
                    
2025-07-17 15:23:21,478 - root - INFO - Parallel processing complete. Results saved to output-refgpt.jsonl
2025-07-17 15:23:42,897 - root - WARNING - 
                    **************************************
                    All tasks finished. Breaking main loop.
                    **************************************
                    
2025-07-17 15:23:42,897 - root - INFO - Parallel processing complete. Results saved to output-refgpt.jsonl
2025-07-17 15:24:21,808 - root - WARNING - 
                    **************************************
                    All tasks finished. Breaking main loop.
                    **************************************
                    
2025-07-17 15:24:21,808 - root - INFO - Parallel processing complete. Results saved to output-refgpt.jsonl
2025-07-17 15:25:24,957 - root - WARNING - 
                    **************************************
                    All tasks finished. Breaking main loop.
                    **************************************
                    
2025-07-17 15:25:24,957 - root - INFO - Parallel processing complete. Results saved to output-refgpt.jsonl
2025-07-17 15:31:20,048 - root - WARNING - 
                    **************************************
                    All tasks finished. Breaking main loop.
                    **************************************
                    
2025-07-17 15:31:20,048 - root - INFO - Parallel processing complete. Results saved to output-refgpt.jsonl
2025-07-17 15:31:28,550 - root - WARNING - 
                    **************************************
                    All tasks finished. Breaking main loop.
                    **************************************
                    
2025-07-17 15:31:28,550 - root - INFO - Parallel processing complete. Results saved to output-refgpt.jsonl
2025-07-17 15:32:13,082 - root - WARNING - 
                    **************************************
                    All tasks finished. Breaking main loop.
                    **************************************
                    
2025-07-17 15:32:13,082 - root - INFO - Parallel processing complete. Results saved to output-refgpt.jsonl
2025-07-17 15:32:18,739 - root - WARNING - 
                    **************************************
                    All tasks finished. Breaking main loop.
                    **************************************
                    
2025-07-17 15:32:18,739 - root - INFO - Parallel processing complete. Results saved to output-refgpt.jsonl
2025-07-17 15:32:27,587 - root - WARNING - 
                    **************************************
                    All tasks finished. Breaking main loop.
                    **************************************
                    
2025-07-17 15:32:27,587 - root - INFO - Parallel processing complete. Results saved to output-refgpt.jsonl
2025-07-17 15:34:58,095 - root - INFO - Starting request #0
2025-07-17 15:34:58,095 - root - WARNING - Request 0 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:34:58,107 - root - INFO - Starting request #1
2025-07-17 15:34:58,107 - root - WARNING - Request 1 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:34:58,119 - root - INFO - Starting request #2
2025-07-17 15:34:58,119 - root - WARNING - Request 2 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:34:58,131 - root - INFO - Starting request #3
2025-07-17 15:34:58,131 - root - WARNING - Request 3 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:34:58,144 - root - INFO - Starting request #4
2025-07-17 15:34:58,144 - root - WARNING - Request 4 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:34:58,155 - root - INFO - Starting request #5
2025-07-17 15:34:58,156 - root - WARNING - Request 5 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:34:58,293 - root - WARNING - Request 2 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:34:58,295 - root - INFO - Starting request #2
2025-07-17 15:34:58,295 - root - WARNING - Request 2 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:34:58,312 - root - WARNING - Request 5 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:34:58,312 - root - WARNING - Request 1 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:34:58,317 - root - INFO - Starting request #5
2025-07-17 15:34:58,317 - root - WARNING - Request 5 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:34:58,328 - root - INFO - Starting request #1
2025-07-17 15:34:58,329 - root - WARNING - Request 1 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:34:58,335 - root - WARNING - Request 3 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:34:58,335 - root - WARNING - Request 4 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:34:58,447 - root - WARNING - Request 2 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:34:58,493 - root - WARNING - Request 5 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:34:58,526 - root - WARNING - Request 0 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:34:58,870 - root - WARNING - Request 1 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:34:59,545 - root - INFO - Starting request #3
2025-07-17 15:34:59,546 - root - WARNING - Request 3 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:35:00,059 - root - WARNING - Request 3 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:35:05,683 - root - INFO - Starting request #4
2025-07-17 15:35:05,685 - root - WARNING - Request 4 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:35:05,901 - root - WARNING - Request 4 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:35:11,829 - root - INFO - Starting request #2
2025-07-17 15:35:11,829 - root - WARNING - Request 2 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:35:12,016 - root - WARNING - Request 2 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:35:17,971 - root - INFO - Starting request #5
2025-07-17 15:35:17,972 - root - WARNING - Request 5 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:35:18,774 - root - WARNING - Request 5 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:35:24,116 - root - INFO - Starting request #0
2025-07-17 15:35:24,117 - root - WARNING - Request 0 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:35:24,267 - root - WARNING - Request 0 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:35:30,256 - root - INFO - Starting request #1
2025-07-17 15:35:30,258 - root - WARNING - Request 1 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:35:30,385 - root - WARNING - Request 1 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:35:36,406 - root - INFO - Starting request #3
2025-07-17 15:35:36,407 - root - WARNING - Request 3 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:35:36,879 - root - WARNING - Request 3 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:35:42,551 - root - INFO - Starting request #4
2025-07-17 15:35:42,553 - root - WARNING - Request 4 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:35:42,986 - root - WARNING - Request 4 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:35:48,691 - root - INFO - Starting request #2
2025-07-17 15:35:48,692 - root - WARNING - Request 2 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:35:49,177 - root - WARNING - Request 2 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:35:54,836 - root - INFO - Starting request #5
2025-07-17 15:35:54,836 - root - WARNING - Request 5 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:35:55,124 - root - WARNING - Request 5 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:36:00,985 - root - INFO - Starting request #0
2025-07-17 15:36:00,987 - root - WARNING - Request 0 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:36:01,422 - root - WARNING - Request 0 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:36:07,126 - root - INFO - Starting request #1
2025-07-17 15:36:07,127 - root - WARNING - Request 1 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:36:07,530 - root - WARNING - Request 1 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:36:13,269 - root - INFO - Starting request #3
2025-07-17 15:36:13,270 - root - WARNING - Request 3 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:36:13,409 - root - WARNING - Request 3 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:36:19,415 - root - INFO - Starting request #4
2025-07-17 15:36:19,415 - root - WARNING - Request 4 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:36:19,838 - root - WARNING - Request 4 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:36:25,562 - root - INFO - Starting request #2
2025-07-17 15:36:25,563 - root - WARNING - Request 2 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:36:25,707 - root - WARNING - Request 2 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:36:25,708 - root - ERROR - Request 2 failed after all attempts.
2025-07-17 15:36:31,702 - root - INFO - Starting request #5
2025-07-17 15:36:31,704 - root - WARNING - Request 5 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:36:31,858 - root - WARNING - Request 5 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:36:31,859 - root - ERROR - Request 5 failed after all attempts.
2025-07-17 15:36:37,843 - root - INFO - Starting request #0
2025-07-17 15:36:37,843 - root - WARNING - Request 0 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:36:38,289 - root - WARNING - Request 0 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:36:43,988 - root - INFO - Starting request #1
2025-07-17 15:36:43,988 - root - WARNING - Request 1 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:36:44,187 - root - WARNING - Request 1 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:36:44,188 - root - ERROR - Request 1 failed after all attempts.
2025-07-17 15:36:50,132 - root - INFO - Starting request #3
2025-07-17 15:36:50,133 - root - WARNING - Request 3 is switched to API key sk-89f5a10520814825ad34c7eac4533ba3 to start.
2025-07-17 15:36:50,279 - root - WARNING - Request 3 failed with Exception 404, message='Attempt to decode JSON with unexpected mimetype: ', url='https://dashscope.aliyuncs.com/compatible-mode/v1'
2025-07-17 15:36:50,280 - root - ERROR - Request 3 failed after all attempts.
