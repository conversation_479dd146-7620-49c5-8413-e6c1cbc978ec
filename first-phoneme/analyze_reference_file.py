#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

import json
import random
import numpy as np

def analyze_reference_file(filepath, assistant_word_count=300, human_word_count=100, num_turn_ratios=[0, 1, 2, 2, 0], language="en"):
    """分析参考文件，检查哪些条目符合长度要求"""
    
    # 读取文件
    with open(filepath, "r", encoding="utf8") as f:
        references = [json.loads(line.strip()) for line in f if line.strip()]
    
    print(f"总共加载了 {len(references)} 个参考条目")
    
    # 模拟脚本的逻辑
    valid_count = 0
    too_short_count = 0
    missing_desc_count = 0
    
    # 分析每个条目
    for i, ref in enumerate(references):
        if "desc" not in ref:
            missing_desc_count += 1
            continue
            
        # 模拟脚本中的轮数和字数计算
        selected_round = [1, 2, 3, 4, 5]
        rounds = random.choices(selected_round, weights=num_turn_ratios)[0]
        assistant_word_counts = (np.random.normal(loc=assistant_word_count, scale=50, size=rounds).astype(int) // 50 * 50).tolist()
        human_word_counts = (np.random.normal(loc=human_word_count, scale=50, size=rounds).astype(int) // 50 * 50).tolist()
        
        total_word_count = sum(assistant_word_counts)
        
        desc_text = ref["desc"]
        
        if language == "en":
            word_count = len(desc_text.split(' '))
            required_words = int(0.8 * total_word_count)
            
            if word_count >= required_words:
                valid_count += 1
                if valid_count <= 5:  # 显示前5个有效条目
                    print(f"\n✅ 有效条目 {valid_count}:")
                    print(f"   索引: {i}")
                    print(f"   字数: {word_count} (需要: {required_words})")
                    print(f"   标题: {ref.get('title', '无标题')}")
                    print(f"   内容预览: {desc_text[:100]}...")
            else:
                too_short_count += 1
                if too_short_count <= 3:  # 显示前3个太短的条目
                    print(f"\n❌ 太短条目 {too_short_count}:")
                    print(f"   索引: {i}")
                    print(f"   字数: {word_count} (需要: {required_words})")
                    print(f"   内容: {desc_text}")
        else:  # 中文
            char_count = len(desc_text)
            required_chars = int(0.8 * total_word_count)
            
            if char_count >= required_chars:
                valid_count += 1
                if valid_count <= 5:
                    print(f"\n✅ 有效条目 {valid_count}:")
                    print(f"   索引: {i}")
                    print(f"   字符数: {char_count} (需要: {required_chars})")
                    print(f"   标题: {ref.get('title', '无标题')}")
                    print(f"   内容预览: {desc_text[:100]}...")
            else:
                too_short_count += 1
                if too_short_count <= 3:
                    print(f"\n❌ 太短条目 {too_short_count}:")
                    print(f"   索引: {i}")
                    print(f"   字符数: {char_count} (需要: {required_chars})")
                    print(f"   内容: {desc_text}")
    
    print(f"\n📊 分析结果:")
    print(f"   总条目数: {len(references)}")
    print(f"   有效条目: {valid_count}")
    print(f"   太短条目: {too_short_count}")
    print(f"   缺少desc字段: {missing_desc_count}")
    print(f"   有效率: {valid_count/len(references)*100:.1f}%")
    
    return valid_count, too_short_count, missing_desc_count

if __name__ == "__main__":
    print("🔍 分析 no_com_output-desc.jsonl 文件...")
    analyze_reference_file(
        "no_com_output-desc.jsonl",
        assistant_word_count=300,
        human_word_count=100,
        num_turn_ratios=[0, 1, 2, 2, 0],
        language="en"
    )
    
    print("\n" + "="*50)
    print("🔍 对比分析 wiki.jsonl 文件...")
    analyze_reference_file(
        "wiki.jsonl",
        assistant_word_count=300,
        human_word_count=100,
        num_turn_ratios=[0, 1, 2, 2, 0],
        language="en"
    )
