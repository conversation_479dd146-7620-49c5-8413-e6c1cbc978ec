#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

"""
婴幼儿语音构音监测数据集构建工具
基于构音语音能力评估记录表生成训练数据
"""

import json
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any

class AssessmentDatasetBuilder:
    """构音评估数据集构建器"""
    
    def __init__(self):
        self.phonemes = {
            # 早期发展音素 (12-24个月)
            "early": ["/p/", "/b/", "/m/", "/w/", "/h/", "/n/", "/t/", "/d/"],
            # 中期发展音素 (24-36个月)  
            "middle": ["/k/", "/g/", "/f/", "/v/", "/ng/", "/l/", "/j/", "/ch/"],
            # 晚期发展音素 (36-48个月)
            "late": ["/s/", "/z/", "/sh/", "/zh/", "/r/", "/th/"]
        }
        
        self.error_types = ["correct", "substitution", "omission", "distortion", "addition"]
        self.severity_levels = ["normal", "mild", "moderate", "severe"]
        self.word_positions = ["initial", "medial", "final"]
        
        # 发展里程碑
        self.developmental_milestones = {
            12: {"expected_phonemes": ["/p/", "/b/", "/m/", "/w/"], "vocabulary_size": "10-50"},
            18: {"expected_phonemes": ["/p/", "/b/", "/m/", "/w/", "/h/", "/n/"], "vocabulary_size": "50-200"},
            24: {"expected_phonemes": ["/p/", "/b/", "/m/", "/w/", "/h/", "/n/", "/t/", "/d/"], "vocabulary_size": "200-300"},
            36: {"expected_phonemes": ["/p/", "/b/", "/m/", "/w/", "/h/", "/n/", "/t/", "/d/", "/k/", "/g/", "/f/"], "vocabulary_size": "300-1000"},
            48: {"expected_phonemes": ["/p/", "/b/", "/m/", "/w/", "/h/", "/n/", "/t/", "/d/", "/k/", "/g/", "/f/", "/v/", "/ng/", "/l/"], "vocabulary_size": "1000+"}
        }

    def generate_child_profile(self) -> Dict[str, Any]:
        """生成儿童基本信息"""
        age_months = random.randint(12, 48)
        
        return {
            "child_id": f"C{random.randint(1000, 9999)}",
            "age_months": age_months,
            "age_years_months": f"{age_months // 12}岁{age_months % 12}个月",
            "gender": random.choice(["male", "female"]),
            "assessment_date": (datetime.now() - timedelta(days=random.randint(0, 365))).strftime("%Y-%m-%d"),
            "language_environment": random.choice(["monolingual", "bilingual", "multilingual"]),
            "developmental_concerns": random.choice([None, "speech_delay", "hearing_concerns", "oral_motor_issues"])
        }

    def generate_phoneme_assessment(self, age_months: int) -> Dict[str, Dict[str, str]]:
        """生成音素评估结果"""
        assessment = {}
        
        # 根据年龄确定应该掌握的音素
        expected_phonemes = []
        for milestone_age, data in self.developmental_milestones.items():
            if age_months >= milestone_age:
                expected_phonemes = data["expected_phonemes"]
        
        # 为每个相关音素生成评估结果
        all_phonemes = self.phonemes["early"] + self.phonemes["middle"] + self.phonemes["late"]
        
        for phoneme in all_phonemes:
            # 根据年龄和音素难度调整正确率
            if phoneme in expected_phonemes:
                correct_probability = 0.8  # 应该掌握的音素有80%正确率
            else:
                correct_probability = 0.3  # 未到发展期的音素有30%正确率
            
            assessment[phoneme] = {}
            for position in self.word_positions:
                if random.random() < correct_probability:
                    assessment[phoneme][position] = "correct"
                else:
                    assessment[phoneme][position] = random.choice(["substitution", "omission", "distortion"])
        
        return assessment

    def calculate_severity(self, phoneme_assessment: Dict) -> Dict[str, Any]:
        """计算严重程度"""
        total_items = len(phoneme_assessment) * 3  # 每个音素3个位置
        correct_items = sum(1 for phoneme_data in phoneme_assessment.values() 
                          for result in phoneme_data.values() if result == "correct")
        
        accuracy = correct_items / total_items
        
        if accuracy >= 0.9:
            severity = "normal"
            intelligibility = "highly_clear"
        elif accuracy >= 0.7:
            severity = "mild"
            intelligibility = "mostly_clear"
        elif accuracy >= 0.5:
            severity = "moderate"
            intelligibility = "somewhat_clear"
        else:
            severity = "severe"
            intelligibility = "difficult_to_understand"
        
        return {
            "overall_accuracy": round(accuracy * 100, 1),
            "severity_level": severity,
            "intelligibility": intelligibility,
            "total_errors": total_items - correct_items
        }

    def generate_recommendations(self, child_profile: Dict, phoneme_assessment: Dict, severity: Dict) -> List[str]:
        """生成个性化建议"""
        recommendations = []
        age_months = child_profile["age_months"]
        
        # 基于年龄的建议
        if age_months < 24:
            recommendations.append("encourage_vocal_play")
            recommendations.append("model_simple_words")
        elif age_months < 36:
            recommendations.append("practice_two_syllable_words")
            recommendations.append("use_visual_cues")
        else:
            recommendations.append("focus_on_complex_sounds")
            recommendations.append("practice_sentence_level")
        
        # 基于错误模式的建议
        error_patterns = {}
        for phoneme, positions in phoneme_assessment.items():
            for position, result in positions.items():
                if result != "correct":
                    if result not in error_patterns:
                        error_patterns[result] = []
                    error_patterns[result].append(f"{phoneme}_{position}")
        
        if "substitution" in error_patterns:
            recommendations.append("minimal_pair_practice")
        if "omission" in error_patterns:
            recommendations.append("syllable_awareness_activities")
        if "distortion" in error_patterns:
            recommendations.append("oral_motor_exercises")
        
        # 基于严重程度的建议
        if severity["severity_level"] in ["moderate", "severe"]:
            recommendations.append("professional_speech_therapy")
            recommendations.append("frequent_practice_sessions")
        
        return recommendations

    def generate_assessment_record(self) -> Dict[str, Any]:
        """生成完整的评估记录"""
        child_profile = self.generate_child_profile()
        phoneme_assessment = self.generate_phoneme_assessment(child_profile["age_months"])
        severity = self.calculate_severity(phoneme_assessment)
        recommendations = self.generate_recommendations(child_profile, phoneme_assessment, severity)
        
        return {
            "record_id": f"AR_{datetime.now().strftime('%Y%m%d')}_{random.randint(1000, 9999)}",
            "child_profile": child_profile,
            "phoneme_assessment": phoneme_assessment,
            "severity_assessment": severity,
            "recommendations": recommendations,
            "assessor_notes": self.generate_assessor_notes(severity),
            "follow_up_plan": self.generate_follow_up_plan(severity["severity_level"])
        }

    def generate_assessor_notes(self, severity: Dict) -> str:
        """生成评估师备注"""
        notes_templates = {
            "normal": "儿童语音发展符合年龄预期，构音清晰度良好。建议继续观察发展情况。",
            "mild": "存在轻度构音问题，主要表现在个别音素的发音不准确。建议加强家庭练习。",
            "moderate": "构音问题较为明显，影响部分语音清晰度。建议进行专业语音治疗。",
            "severe": "构音问题严重，显著影响交流效果。强烈建议立即开始密集的语音治疗。"
        }
        return notes_templates.get(severity["severity_level"], "需要进一步评估。")

    def generate_follow_up_plan(self, severity_level: str) -> Dict[str, Any]:
        """生成随访计划"""
        follow_up_plans = {
            "normal": {"interval_months": 6, "focus": "developmental_monitoring"},
            "mild": {"interval_months": 3, "focus": "targeted_practice"},
            "moderate": {"interval_months": 2, "focus": "therapy_progress"},
            "severe": {"interval_months": 1, "focus": "intensive_intervention"}
        }
        return follow_up_plans.get(severity_level, {"interval_months": 3, "focus": "general_monitoring"})

    def create_dialogue_from_record(self, record: Dict[str, Any], dialogue_type: str = "assessment") -> Dict[str, Any]:
        """基于评估记录创建对话"""
        child = record["child_profile"]
        severity = record["severity_assessment"]
        
        if dialogue_type == "initial_assessment":
            return self.create_initial_assessment_dialogue(record)
        elif dialogue_type == "parent_consultation":
            return self.create_parent_consultation_dialogue(record)
        elif dialogue_type == "therapy_guidance":
            return self.create_therapy_guidance_dialogue(record)
        else:
            return self.create_general_assessment_dialogue(record)

    def create_initial_assessment_dialogue(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """创建初次评估对话"""
        child = record["child_profile"]
        severity = record["severity_assessment"]
        
        dialogue = f"""<开始对话>
<人类 1>: 您好，我想了解一下我家{child['age_years_months']}孩子的语音发展情况。
<助手 1>: 您好！我来帮您评估孩子的语音构音能力。首先，请告诉我孩子平时说话的情况如何？
<人类 2>: 孩子现在会说一些简单的词，但有些音发不清楚。
<助手 2>: 这很正常。根据评估结果，您的孩子语音准确率为{severity['overall_accuracy']}%，属于{severity['severity_level']}水平。主要问题集中在某些音素的发音上。
<人类 3>: 这种情况需要治疗吗？
<助手 3>: 根据评估结果，建议{self.get_treatment_recommendation(severity['severity_level'])}。我会为您制定具体的家庭练习计划。
<结束对话>"""
        
        return {
            "dialogue_type": "initial_assessment",
            "child_age_months": child["age_months"],
            "severity_level": severity["severity_level"],
            "dialogue": dialogue,
            "assessment_record_id": record["record_id"]
        }

    def get_treatment_recommendation(self, severity_level: str) -> str:
        """获取治疗建议"""
        recommendations = {
            "normal": "继续观察，定期复查",
            "mild": "加强家庭练习，3个月后复查",
            "moderate": "开始专业语音治疗",
            "severe": "立即开始密集的语音治疗"
        }
        return recommendations.get(severity_level, "进一步评估")

    def get_parent_advice(self, recommendations: List[str]) -> str:
        """获取家长建议"""
        if "professional_speech_therapy" in recommendations:
            return "寻求专业语音治疗师的帮助"
        elif "frequent_practice_sessions" in recommendations:
            return "增加日常练习频率"
        else:
            return "在家进行适当的语音练习"

    def get_home_practice_advice(self, recommendations: List[str]) -> str:
        """获取家庭练习建议"""
        advice = []
        if "oral_motor_exercises" in recommendations:
            advice.append("进行口部肌肉练习")
        if "minimal_pair_practice" in recommendations:
            advice.append("练习相似音素的区分")
        if "syllable_awareness_activities" in recommendations:
            advice.append("进行音节意识训练")

        return "、".join(advice) if advice else "多与孩子进行语言交流"

    def get_therapy_focus(self, record: Dict[str, Any]) -> str:
        """获取治疗重点"""
        phoneme_assessment = record["phoneme_assessment"]
        problem_phonemes = []

        for phoneme, positions in phoneme_assessment.items():
            for position, result in positions.items():
                if result != "correct":
                    problem_phonemes.append(phoneme)
                    break

        if len(problem_phonemes) > 10:
            return "基础音素的建立和巩固"
        elif len(problem_phonemes) > 5:
            return "重点音素的针对性训练"
        else:
            return "个别音素的精细化训练"

    def get_progress_timeline(self, severity_level: str) -> str:
        """获取进展时间线"""
        timelines = {
            "normal": "1-2个月内会有明显改善",
            "mild": "2-3个月内会有显著进步",
            "moderate": "3-6个月的持续训练会看到效果",
            "severe": "需要6个月以上的密集训练"
        }
        return timelines.get(severity_level, "需要持续观察")

    def interpret_results(self, severity: Dict[str, Any]) -> str:
        """解释评估结果"""
        interpretations = {
            "normal": "孩子的语音发展符合年龄预期",
            "mild": "存在轻微的构音问题，但不影响日常交流",
            "moderate": "构音问题较为明显，需要专业指导",
            "severe": "构音问题严重，需要立即干预"
        }
        return interpretations.get(severity["severity_level"], "需要进一步评估")

    def get_general_recommendation(self, severity_level: str) -> str:
        """获取一般建议"""
        recommendations = {
            "normal": "继续观察发展情况",
            "mild": "加强家庭语言环境",
            "moderate": "考虑专业语音评估",
            "severe": "立即寻求专业帮助"
        }
        return recommendations.get(severity_level, "定期复查")

    def create_parent_consultation_dialogue(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """创建家长咨询对话"""
        child = record["child_profile"]
        recommendations = record["recommendations"]

        dialogue = f"""<开始对话>
<人类 1>: 医生，我家{child['age_years_months']}的孩子说话不清楚，我应该怎么办？
<助手 1>: 我理解您的担心。根据评估，孩子确实存在一些构音问题。让我为您详细解释一下情况和建议。
<人类 2>: 这种情况严重吗？会影响孩子以后的学习吗？
<助手 2>: 根据评估结果，建议{self.get_parent_advice(recommendations)}。早期干预非常重要，大多数构音问题都可以通过适当的训练得到改善。
<人类 3>: 我在家里可以做些什么来帮助孩子？
<助手 3>: 您可以{self.get_home_practice_advice(recommendations)}。记住要保持耐心，给孩子正面的鼓励。
<结束对话>"""

        return {
            "dialogue_type": "parent_consultation",
            "child_age_months": child["age_months"],
            "dialogue": dialogue,
            "assessment_record_id": record["record_id"]
        }

    def create_therapy_guidance_dialogue(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """创建治疗指导对话"""
        child = record["child_profile"]
        severity = record["severity_assessment"]

        dialogue = f"""<开始对话>
<人类 1>: 请为我制定一个针对{child['age_years_months']}孩子的语音治疗计划。
<助手 1>: 好的，根据评估结果，孩子的语音准确率为{severity['overall_accuracy']}%。我来为您制定个性化的治疗方案。
<人类 2>: 治疗的重点应该放在哪些方面？
<助手 2>: 主要需要关注{self.get_therapy_focus(record)}。建议每周进行2-3次训练，每次20-30分钟。
<人类 3>: 大概需要多长时间能看到效果？
<助手 3>: 根据孩子的情况，预计{self.get_progress_timeline(severity['severity_level'])}。我们会定期评估进展并调整治疗方案。
<结束对话>"""

        return {
            "dialogue_type": "therapy_guidance",
            "child_age_months": child["age_months"],
            "severity_level": severity["severity_level"],
            "dialogue": dialogue,
            "assessment_record_id": record["record_id"]
        }

    def create_general_assessment_dialogue(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """创建一般评估对话"""
        child = record["child_profile"]
        severity = record["severity_assessment"]

        dialogue = f"""<开始对话>
<人类 1>: 我想了解一下{child['age_years_months']}孩子的语音发展是否正常。
<助手 1>: 我来为您进行详细的语音构音评估。请描述一下孩子目前的说话情况。
<人类 2>: 孩子能说很多词，但有时候我们听不太懂。
<助手 2>: 根据标准化评估，孩子的语音清晰度为{severity['intelligibility']}，整体准确率{severity['overall_accuracy']}%。
<人类 3>: 这个结果意味着什么？
<助手 3>: 这表示{self.interpret_results(severity)}。建议{self.get_general_recommendation(severity['severity_level'])}。
<结束对话>"""

        return {
            "dialogue_type": "general_assessment",
            "child_age_months": child["age_months"],
            "severity_level": severity["severity_level"],
            "dialogue": dialogue,
            "assessment_record_id": record["record_id"]
        }

    def build_dataset(self, num_records: int = 1000, output_file: str = "assessment_dataset.jsonl") -> None:
        """构建完整数据集"""
        print(f"🚀 开始构建婴幼儿语音构音监测数据集...")
        print(f"目标记录数: {num_records}")
        
        with open(output_file, "w", encoding="utf8") as f:
            for i in range(num_records):
                # 生成评估记录
                record = self.generate_assessment_record()
                
                # 生成多种类型的对话
                dialogue_types = ["initial_assessment", "parent_consultation", "therapy_guidance"]
                selected_type = random.choice(dialogue_types)
                
                dialogue = self.create_dialogue_from_record(record, selected_type)
                
                # 合并数据
                dataset_entry = {
                    **record,
                    **dialogue,
                    "created_at": datetime.now().isoformat()
                }
                
                f.write(json.dumps(dataset_entry, ensure_ascii=False) + "\n")
                
                if (i + 1) % 100 == 0:
                    print(f"已生成 {i + 1} 条记录...")
        
        print(f"✅ 数据集构建完成！")
        print(f"输出文件: {output_file}")
        print(f"总记录数: {num_records}")

if __name__ == "__main__":
    builder = AssessmentDatasetBuilder()
    
    # 生成示例记录
    print("📋 生成示例评估记录:")
    sample_record = builder.generate_assessment_record()
    print(json.dumps(sample_record, ensure_ascii=False, indent=2))
    
    print("\n" + "="*50)
    
    # 生成示例对话
    print("💬 生成示例对话:")
    sample_dialogue = builder.create_dialogue_from_record(sample_record, "initial_assessment")
    print(json.dumps(sample_dialogue, ensure_ascii=False, indent=2))
    
    print("\n" + "="*50)
    
    # 构建小规模数据集进行测试
    print("🔧 构建测试数据集:")
    builder.build_dataset(num_records=10, output_file="test_assessment_dataset.jsonl")
