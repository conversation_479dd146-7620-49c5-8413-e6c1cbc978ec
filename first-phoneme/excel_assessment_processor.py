#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

"""
Excel格式构音语音能力评估记录表处理工具
用于提取评估表结构并生成数据集
"""

import pandas as pd
import json
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

class ExcelAssessmentProcessor:
    """Excel评估表处理器"""
    
    def __init__(self, excel_file: str = "构音语音能力评估记录表.xlsx"):
        self.excel_file = excel_file
        self.assessment_structure = {}
        self.sample_data = {}
        
    def analyze_excel_structure(self) -> Dict[str, Any]:
        """分析Excel文件结构"""
        try:
            # 读取所有工作表
            excel_data = pd.read_excel(self.excel_file, sheet_name=None, header=None)
            
            structure = {
                "total_sheets": len(excel_data),
                "sheet_names": list(excel_data.keys()),
                "sheets_info": {}
            }
            
            print(f"📊 Excel文件结构分析")
            print(f"="*50)
            print(f"文件名: {self.excel_file}")
            print(f"工作表数量: {len(excel_data)}")
            print(f"工作表名称: {list(excel_data.keys())}")
            
            for sheet_name, df in excel_data.items():
                sheet_info = {
                    "rows": len(df),
                    "columns": len(df.columns),
                    "non_empty_cells": df.count().sum(),
                    "sample_content": []
                }
                
                # 提取前几行作为样本
                for i in range(min(10, len(df))):
                    row_data = []
                    for j in range(min(5, len(df.columns))):
                        cell_value = df.iloc[i, j]
                        if pd.notna(cell_value):
                            row_data.append(str(cell_value))
                        else:
                            row_data.append("")
                    sheet_info["sample_content"].append(row_data)
                
                structure["sheets_info"][sheet_name] = sheet_info
                
                print(f"\n📋 工作表: {sheet_name}")
                print(f"  行数: {sheet_info['rows']}")
                print(f"  列数: {sheet_info['columns']}")
                print(f"  非空单元格: {sheet_info['non_empty_cells']}")
                print(f"  样本内容:")
                for idx, row in enumerate(sheet_info["sample_content"][:5]):
                    if any(cell.strip() for cell in row if cell):
                        print(f"    行{idx+1}: {' | '.join(row[:3])}")
            
            self.assessment_structure = structure
            return structure
            
        except Exception as e:
            print(f"❌ 读取Excel文件失败: {e}")
            return {"error": str(e)}
    
    def extract_assessment_fields(self) -> Dict[str, List[str]]:
        """提取评估字段信息"""
        try:
            excel_data = pd.read_excel(self.excel_file, sheet_name=None, header=None)
            
            assessment_fields = {
                "basic_info": [],
                "phoneme_assessment": [],
                "evaluation_criteria": [],
                "recommendations": [],
                "other_fields": []
            }
            
            # 常见的评估字段关键词
            field_keywords = {
                "basic_info": ["姓名", "年龄", "性别", "出生", "日期", "评估师", "家长"],
                "phoneme_assessment": ["音素", "发音", "构音", "/p/", "/b/", "/t/", "/d/", "/k/", "/g/", 
                                     "/f/", "/v/", "/s/", "/z/", "/sh/", "/ch/", "/r/", "/l/", "/m/", "/n/"],
                "evaluation_criteria": ["正确", "错误", "替代", "省略", "歪曲", "清晰度", "准确率", "评分"],
                "recommendations": ["建议", "训练", "练习", "治疗", "指导", "改善", "提高"]
            }
            
            for sheet_name, df in excel_data.items():
                print(f"\n🔍 分析工作表: {sheet_name}")
                
                # 遍历所有单元格寻找关键字段
                for i in range(len(df)):
                    for j in range(len(df.columns)):
                        cell_value = df.iloc[i, j]
                        if pd.notna(cell_value):
                            cell_str = str(cell_value).strip()
                            if cell_str:
                                # 分类字段
                                categorized = False
                                for category, keywords in field_keywords.items():
                                    if any(keyword in cell_str for keyword in keywords):
                                        if cell_str not in assessment_fields[category]:
                                            assessment_fields[category].append(cell_str)
                                        categorized = True
                                        break
                                
                                if not categorized and len(cell_str) > 1:
                                    if cell_str not in assessment_fields["other_fields"]:
                                        assessment_fields["other_fields"].append(cell_str)
            
            # 显示提取结果
            for category, fields in assessment_fields.items():
                if fields:
                    print(f"\n📝 {category}:")
                    for field in fields[:10]:  # 只显示前10个
                        print(f"  - {field}")
                    if len(fields) > 10:
                        print(f"  ... 还有 {len(fields) - 10} 个字段")
            
            return assessment_fields
            
        except Exception as e:
            print(f"❌ 提取字段失败: {e}")
            return {}
    
    def create_assessment_template(self) -> Dict[str, Any]:
        """基于Excel结构创建评估模板"""
        
        # 标准化的构音评估模板
        template = {
            "basic_info": {
                "child_name": "儿童姓名",
                "child_id": "编号",
                "age_months": "年龄(月)",
                "gender": "性别",
                "birth_date": "出生日期",
                "assessment_date": "评估日期",
                "assessor": "评估师",
                "parent_concerns": "家长关注点"
            },
            "phoneme_inventory": {
                # 声母
                "initials": {
                    "/p/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/b/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/t/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/d/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/k/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/g/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/f/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/v/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/s/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/z/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/sh/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/zh/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/ch/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/j/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/r/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/l/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/m/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/n/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/ng/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/h/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/w/": {"word_initial": "", "word_medial": "", "word_final": ""},
                    "/y/": {"word_initial": "", "word_medial": "", "word_final": ""}
                }
            },
            "assessment_results": {
                "overall_intelligibility": "",  # 整体清晰度
                "phoneme_accuracy": "",         # 音素准确率
                "error_patterns": [],           # 错误模式
                "severity_level": "",           # 严重程度
                "stimulability": ""             # 可刺激性
            },
            "recommendations": {
                "immediate_goals": [],          # 即时目标
                "therapy_approach": [],         # 治疗方法
                "home_practice": [],            # 家庭练习
                "follow_up_schedule": "",       # 随访计划
                "referrals": []                 # 转介建议
            },
            "progress_tracking": {
                "baseline_date": "",
                "progress_notes": [],
                "next_assessment": ""
            }
        }
        
        return template
    
    def generate_sample_data(self, num_samples: int = 5) -> List[Dict[str, Any]]:
        """生成样本数据"""
        
        template = self.create_assessment_template()
        samples = []
        
        # 预定义的选项
        genders = ["男", "女"]
        severity_levels = ["正常", "轻度", "中度", "重度"]
        error_types = ["正确", "替代", "省略", "歪曲", "增加"]
        intelligibility_levels = ["完全清晰", "大部分清晰", "部分清晰", "难以理解"]
        
        for i in range(num_samples):
            sample = template.copy()
            
            # 填充基本信息
            age_months = random.randint(12, 48)
            sample["basic_info"] = {
                "child_name": f"儿童{i+1:03d}",
                "child_id": f"C{i+1:04d}",
                "age_months": age_months,
                "gender": random.choice(genders),
                "birth_date": (datetime.now() - timedelta(days=age_months*30)).strftime("%Y-%m-%d"),
                "assessment_date": datetime.now().strftime("%Y-%m-%d"),
                "assessor": f"评估师{random.randint(1, 5)}",
                "parent_concerns": random.choice(["发音不清", "说话晚", "无特殊关注", "听不懂"])
            }
            
            # 填充音素评估
            for phoneme in sample["phoneme_inventory"]["initials"]:
                for position in ["word_initial", "word_medial", "word_final"]:
                    # 根据年龄调整正确率
                    if age_months >= 36:
                        correct_prob = 0.8
                    elif age_months >= 24:
                        correct_prob = 0.6
                    else:
                        correct_prob = 0.4
                    
                    if random.random() < correct_prob:
                        sample["phoneme_inventory"]["initials"][phoneme][position] = "正确"
                    else:
                        sample["phoneme_inventory"]["initials"][phoneme][position] = random.choice(error_types[1:])
            
            # 填充评估结果
            sample["assessment_results"] = {
                "overall_intelligibility": random.choice(intelligibility_levels),
                "phoneme_accuracy": f"{random.randint(40, 95)}%",
                "error_patterns": random.sample(["替代", "省略", "歪曲"], random.randint(1, 3)),
                "severity_level": random.choice(severity_levels),
                "stimulability": random.choice(["良好", "一般", "较差"])
            }
            
            # 填充建议
            sample["recommendations"] = {
                "immediate_goals": ["改善目标音素发音", "提高语音清晰度"],
                "therapy_approach": ["视觉提示法", "听觉训练", "口部运动练习"],
                "home_practice": ["每日练习15分钟", "家长示范正确发音"],
                "follow_up_schedule": "3个月后复查",
                "referrals": [] if sample["assessment_results"]["severity_level"] in ["正常", "轻度"] else ["语音治疗师"]
            }
            
            samples.append(sample)
        
        return samples
    
    def create_dataset_from_excel(self, output_file: str = "excel_based_dataset.jsonl", num_records: int = 100):
        """基于Excel结构创建数据集"""
        
        print(f"🚀 基于Excel评估表创建数据集...")
        
        # 分析Excel结构
        structure = self.analyze_excel_structure()
        
        # 提取字段信息
        fields = self.extract_assessment_fields()
        
        # 生成样本数据
        samples = self.generate_sample_data(num_records)
        
        # 保存数据集
        with open(output_file, "w", encoding="utf8") as f:
            for i, sample in enumerate(samples):
                # 添加元数据
                record = {
                    "record_id": f"ER_{datetime.now().strftime('%Y%m%d')}_{i+1:04d}",
                    "source": "excel_assessment_form",
                    "created_at": datetime.now().isoformat(),
                    **sample
                }
                
                f.write(json.dumps(record, ensure_ascii=False) + "\n")
        
        print(f"✅ 数据集创建完成!")
        print(f"输出文件: {output_file}")
        print(f"记录数量: {num_records}")
        
        return {
            "excel_structure": structure,
            "extracted_fields": fields,
            "sample_count": num_records,
            "output_file": output_file
        }

def main():
    """主函数"""
    processor = ExcelAssessmentProcessor()
    
    print("📋 Excel构音语音能力评估记录表处理")
    print("="*60)
    
    try:
        # 创建数据集
        result = processor.create_dataset_from_excel(num_records=20)
        
        print(f"\n📊 处理结果:")
        print(f"- Excel工作表数: {result['excel_structure'].get('total_sheets', 0)}")
        print(f"- 提取字段类别: {len(result['extracted_fields'])}")
        print(f"- 生成记录数: {result['sample_count']}")
        print(f"- 输出文件: {result['output_file']}")
        
        # 显示样本记录
        print(f"\n💡 样本记录预览:")
        with open(result['output_file'], "r", encoding="utf8") as f:
            sample_record = json.loads(f.readline())
            print(json.dumps(sample_record, ensure_ascii=False, indent=2)[:1000] + "...")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        print(f"\n💡 建议:")
        print(f"1. 确保Excel文件存在且可读")
        print(f"2. 检查文件格式是否正确")
        print(f"3. 尝试手动分析Excel内容结构")

if __name__ == "__main__":
    main()
