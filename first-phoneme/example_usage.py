#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

"""
使用 Qwen API 的示例脚本
展示如何使用修改后的 parallel_generate-refgpt-fact.py
"""

import json
import os

def create_sample_reference_file():
    """创建一个示例参考文件"""
    sample_data = [
        {
            "desc": "人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大。可以设想，未来人工智能带来的科技产品，将会是人类智慧的容器。",
            "title": "人工智能简介"
        },
        {
            "desc": "机器学习是人工智能的一个重要分支，它是一种通过算法使计算机系统能够自动学习和改进的技术。机器学习算法通过分析大量数据来识别模式，并使用这些模式来对新数据进行预测或决策。常见的机器学习类型包括监督学习、无监督学习和强化学习。",
            "title": "机器学习基础"
        }
    ]
    
    with open("sample_references.jsonl", "w", encoding="utf8") as f:
        for item in sample_data:
            f.write(json.dumps(item, ensure_ascii=False) + "\n")
    
    print("✅ 创建了示例参考文件: sample_references.jsonl")

def show_usage_examples():
    """显示使用示例"""
    print("\n📖 使用修改后的脚本的示例命令：")
    print("\n1. 基本使用（使用默认的 qwen-turbo 模型）：")
    print("python3 parallel_generate-refgpt-fact.py \\")
    print("    --save_filepath output_qwen.jsonl \\")
    print("    --reference_filepaths sample_references.jsonl \\")
    print("    --api_config api_config.jsonl \\")
    print("    --language zh \\")
    print("    --num_chat_to_generate 2")
    
    print("\n2. 使用不同的 Qwen 模型：")
    print("python3 parallel_generate-refgpt-fact.py \\")
    print("    --save_filepath output_qwen_plus.jsonl \\")
    print("    --reference_filepaths sample_references.jsonl \\")
    print("    --api_config api_config.jsonl \\")
    print("    --model qwen-plus \\")  # 使用更强的模型
    print("    --language zh \\")
    print("    --num_chat_to_generate 2")
    
    print("\n3. 英文对话生成：")
    print("python3 parallel_generate-refgpt-fact.py \\")
    print("    --save_filepath output_qwen_en.jsonl \\")
    print("    --reference_filepaths sample_references.jsonl \\")
    print("    --api_config api_config.jsonl \\")
    print("    --model qwen-turbo \\")
    print("    --language en \\")
    print("    --num_chat_to_generate 2")

def show_available_models():
    """显示可用的 Qwen 模型"""
    print("\n🤖 可用的 Qwen 模型：")
    models = [
        ("qwen-turbo", "通用模型，速度快，成本低"),
        ("qwen-plus", "增强模型，能力更强"),
        ("qwen-max", "最强模型，推理能力最佳"),
        ("qwen-max-longcontext", "长文本模型，支持更长的上下文")
    ]
    
    for model, desc in models:
        print(f"  • {model}: {desc}")

def show_changes_summary():
    """显示修改总结"""
    print("\n🔧 主要修改内容：")
    print("1. ✅ 修改请求头格式：从 'api-key' 改为 'Authorization: Bearer {api-key}'")
    print("2. ✅ 添加 model 参数：在 API 请求中指定使用的 Qwen 模型")
    print("3. ✅ 更新 API 端点：使用 Qwen 兼容的 OpenAI API 格式")
    print("4. ✅ 添加 --model 命令行参数：允许用户指定不同的 Qwen 模型")
    print("5. ✅ 更新配置文件：修正 API URL 格式")

if __name__ == "__main__":
    print("🚀 Qwen API 集成完成！")
    
    # 创建示例文件
    create_sample_reference_file()
    
    # 显示修改总结
    show_changes_summary()
    
    # 显示可用模型
    show_available_models()
    
    # 显示使用示例
    show_usage_examples()
    
    print("\n💡 提示：")
    print("- 确保您的 API key 有足够的额度")
    print("- 根据需要调整 --max_requests_per_minute 和 --max_tokens_per_minute 参数")
    print("- 可以通过 --model 参数选择不同的 Qwen 模型")
