import json

def extract_paragraphs_to_jsonl(input_json_path, output_jsonl_path):
    """
    Reads a JSON file with nested 'pages' and 'content', extracts each paragraph item,
    and writes each as a separate JSON object line in the output JSONL file.
    """
    with open(input_json_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    with open(output_jsonl_path, 'w', encoding='utf-8') as out_f:
        for page in data.get('pages', []):
            for item in page.get('content', []):
                # Write only text and type fields as separate JSON objects
                entry = {"text": item.get("text", ""), "type": item.get("type", "")}
                json_line = json.dumps(entry, ensure_ascii=False)
                out_f.write(json_line + "\n")

# Example usage:
input_json = 'tmp-convert-17527203865685462.json'     # Your original JSON file path
output_jsonl = 'paragraphs_output.jsonl'  # Desired output path

extract_paragraphs_to_jsonl(input_json, output_jsonl)

print(f"Extracted paragraphs written to {output_jsonl}")
