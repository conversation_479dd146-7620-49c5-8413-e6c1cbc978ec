#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

"""
基于构音评估表+Qwen LLM的语音评估数据集生成器
参考RefGPT的高效并发处理方法
生成父母描述→LLM判断→专业指导的数据集
"""

import json
import random
import asyncio
import aiohttp
import aiofiles
import argparse
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import pandas as pd

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SpeechAssessmentGenerator:
    """语音评估数据生成器"""
    
    def __init__(self, api_key: str, api_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"):
        self.api_key = api_key
        self.api_url = api_url
        self.session = None
        
        # 加载52个词汇和音素
        self.word_phoneme_pairs = self.load_word_list()
        
        # 构音问题类型
        self.error_types = {
            "替代": "用其他音替代目标音",
            "省略": "省略某个音素或音节", 
            "歪曲": "发音不准确但可识别",
            "增加": "添加额外的音素"
        }
        
        # 年龄段特点
        self.age_characteristics = {
            "12-18": {"common_errors": ["省略", "替代"], "vocabulary": "单词", "concern_level": "观察"},
            "18-24": {"common_errors": ["替代", "歪曲"], "vocabulary": "短句", "concern_level": "轻度关注"},
            "24-36": {"common_errors": ["歪曲", "替代"], "vocabulary": "句子", "concern_level": "需要注意"},
            "36-48": {"common_errors": ["歪曲", "增加"], "vocabulary": "复杂句", "concern_level": "建议评估"}
        }
    
    def load_word_list(self) -> List[Dict[str, str]]:
        """加载52个词汇列表"""
        try:
            # 尝试从Excel加载
            df = pd.read_excel("gouyin-1/构音语音能力评估记录表（all）.xlsx", sheet_name='Page1', header=None)
            word_list = []
            
            for i in range(1, min(53, len(df))):
                if len(df.columns) > 3:
                    word = str(df.iloc[i, 1]).strip() if pd.notna(df.iloc[i, 1]) else ""
                    target_phoneme = str(df.iloc[i, 3]).strip() if pd.notna(df.iloc[i, 3]) else ""
                    
                    if word and any('\u4e00' <= char <= '\u9fff' for char in word):
                        word_list.append({"word": word, "phoneme": target_phoneme})
            
            logger.info(f"从Excel加载了 {len(word_list)} 个词汇")
            return word_list
            
        except Exception as e:
            logger.warning(f"Excel加载失败: {e}，使用默认词汇列表")
            return self.get_default_words()
    
    def get_default_words(self) -> List[Dict[str, str]]:
        """默认52个词汇列表"""
        return [
            {"word": "包", "phoneme": "b"}, {"word": "抛", "phoneme": "p"}, {"word": "猫", "phoneme": "m"},
            {"word": "飞", "phoneme": "f"}, {"word": "刀", "phoneme": "d"}, {"word": "套", "phoneme": "t"},
            {"word": "闹", "phoneme": "n"}, {"word": "鹿", "phoneme": "l"}, {"word": "高", "phoneme": "g"},
            {"word": "考", "phoneme": "k"}, {"word": "河", "phoneme": "h"}, {"word": "鸡", "phoneme": "j"},
            {"word": "七", "phoneme": "q"}, {"word": "吸", "phoneme": "x"}, {"word": "猪", "phoneme": "zh"},
            {"word": "出", "phoneme": "ch"}, {"word": "书", "phoneme": "sh"}, {"word": "肉", "phoneme": "r"},
            {"word": "紫", "phoneme": "z"}, {"word": "粗", "phoneme": "c"}, {"word": "四", "phoneme": "s"},
            {"word": "蓝", "phoneme": "an"}, {"word": "狼", "phoneme": "ang"}, {"word": "心", "phoneme": "in"},
            {"word": "星", "phoneme": "ing"}, {"word": "船", "phoneme": "uan"}, {"word": "床", "phoneme": "uang"},
            {"word": "拔", "phoneme": "a"}, {"word": "鹅", "phoneme": "e"}, {"word": "一", "phoneme": "i"},
            {"word": "家", "phoneme": "ia"}, {"word": "浇", "phoneme": "iao"}, {"word": "乌", "phoneme": "u"},
            {"word": "雨", "phoneme": "yu"}, {"word": "椅", "phoneme": "yi"}, {"word": "鼻", "phoneme": "bi"},
            {"word": "蛙", "phoneme": "wa"}, {"word": "娃", "phoneme": "wa2"}, {"word": "瓦", "phoneme": "wa3"},
            {"word": "袜", "phoneme": "wa4"}, {"word": "杯", "phoneme": "bei"}, {"word": "泡", "phoneme": "pao"},
            {"word": "稻", "phoneme": "dao"}, {"word": "菇", "phoneme": "gu"}, {"word": "哭", "phoneme": "ku"},
            {"word": "壳", "phoneme": "ke"}, {"word": "纸", "phoneme": "zhi"}, {"word": "室", "phoneme": "shi"},
            {"word": "自", "phoneme": "zi"}, {"word": "刺", "phoneme": "ci"}, {"word": "桌", "phoneme": "zhuo"},
            {"word": "象", "phoneme": "xiang"}, {"word": "窗", "phoneme": "chuang"}
        ]
    
    def generate_parent_description(self, age_months: int) -> Dict[str, Any]:
        """生成父母描述的发音问题"""
        
        # 选择年龄段
        if 12 <= age_months < 18:
            age_group = "12-18"
        elif 18 <= age_months < 24:
            age_group = "18-24"
        elif 24 <= age_months < 36:
            age_group = "24-36"
        else:
            age_group = "36-48"
        
        age_char = self.age_characteristics[age_group]
        
        # 随机选择词汇和错误类型
        word_info = random.choice(self.word_phoneme_pairs)
        error_type = random.choice(age_char["common_errors"])
        
        # 生成父母描述的模板
        parent_descriptions = {
            "替代": [
                f"我家孩子说'{word_info['word']}'的时候，总是发成别的音，听起来不太对",
                f"孩子说'{word_info['word']}'时，发音和正确的不一样，像是用其他音代替了",
                f"'{word_info['word']}'这个字，孩子说出来的音和我们说的不同"
            ],
            "省略": [
                f"孩子说'{word_info['word']}'的时候，感觉少了什么音，说得不完整",
                f"'{word_info['word']}'这个词，孩子说的时候好像漏掉了一部分",
                f"孩子说'{word_info['word']}'时，听起来比正确的发音短一些"
            ],
            "歪曲": [
                f"孩子说'{word_info['word']}'时，发音听起来怪怪的，不太清楚",
                f"'{word_info['word']}'这个音，孩子说得不太准确，有点模糊",
                f"孩子说'{word_info['word']}'的发音不太标准，听起来有些扭曲"
            ],
            "增加": [
                f"孩子说'{word_info['word']}'时，好像多加了一些音，说得比较长",
                f"'{word_info['word']}'这个词，孩子说的时候会添加额外的音",
                f"孩子说'{word_info['word']}'时，发音比正常的要复杂一些"
            ]
        }
        
        # 添加年龄和背景信息
        age_desc = f"{age_months // 12}岁{age_months % 12}个月"
        gender = random.choice(["男孩", "女孩"])
        
        context_info = [
            f"我家{gender}{age_desc}了",
            f"孩子现在{age_desc}",
            f"我的{gender}孩子{age_desc}"
        ]
        
        description = random.choice(parent_descriptions[error_type])
        context = random.choice(context_info)
        
        # 添加父母的担心程度
        concern_levels = {
            "观察": "我想了解一下这是否正常",
            "轻度关注": "我有点担心，想咨询一下",
            "需要注意": "我比较担心，这需要干预吗",
            "建议评估": "我很担心，是否需要专业评估"
        }
        
        concern = concern_levels[age_char["concern_level"]]
        
        full_description = f"{context}，{description}。{concern}？"
        
        return {
            "age_months": age_months,
            "age_description": age_desc,
            "gender": gender,
            "target_word": word_info["word"],
            "target_phoneme": word_info["phoneme"],
            "error_type": error_type,
            "parent_description": full_description,
            "concern_level": age_char["concern_level"]
        }
    
    def create_assessment_prompt(self, parent_info: Dict[str, Any]) -> str:
        """创建LLM评估提示词"""
        
        system_prompt = """你是一位专业的语音病理学家和儿童发展专家，具有丰富的婴幼儿语音构音评估和治疗经验。你需要根据家长的描述，判断孩子是否存在构音问题，并给出专业的指导建议。

请按照以下格式回复：

1. **问题判断**：[正常发展/轻度问题/中度问题/需要专业评估]
2. **问题分析**：[详细分析发音问题的类型和可能原因]
3. **年龄适宜性**：[评估该问题在此年龄段是否常见]
4. **指导建议**：[具体的家庭练习方法和注意事项]
5. **随访建议**：[何时复查或寻求专业帮助]

请确保建议专业、实用、易于家长理解和执行。"""

        user_prompt = f"""
家长描述：{parent_info['parent_description']}

请根据以上描述进行专业评估和指导。

补充信息：
- 孩子年龄：{parent_info['age_description']}
- 涉及词汇："{parent_info['target_word']}"
- 目标音素：{parent_info['target_phoneme']}
"""
        
        return system_prompt, user_prompt
    
    async def call_qwen_api(self, system_prompt: str, user_prompt: str) -> Optional[str]:
        """异步调用Qwen API"""
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "qwen-turbo-latest",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 1500
        }
        
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            async with self.session.post(self.api_url, headers=headers, json=data, timeout=30) as response:
                response.raise_for_status()
                result = await response.json()
                
                if "choices" in result and len(result["choices"]) > 0:
                    return result["choices"][0]["message"]["content"].strip()
                else:
                    logger.error(f"API响应格式异常: {result}")
                    return None
                    
        except Exception as e:
            logger.error(f"API调用失败: {e}")
            return None
    
    async def generate_single_record(self, age_months: int) -> Optional[Dict[str, Any]]:
        """生成单条评估记录"""
        
        try:
            # 1. 生成父母描述
            parent_info = self.generate_parent_description(age_months)
            
            # 2. 创建LLM提示词
            system_prompt, user_prompt = self.create_assessment_prompt(parent_info)
            
            # 3. 调用LLM获取专业评估
            llm_response = await self.call_qwen_api(system_prompt, user_prompt)
            
            if not llm_response:
                return None
            
            # 4. 构建完整记录
            record = {
                "record_id": f"SA_{datetime.now().strftime('%Y%m%d')}_{random.randint(1000, 9999)}",
                "source": "speech_assessment_llm",
                "created_at": datetime.now().isoformat(),
                "child_info": {
                    "age_months": parent_info["age_months"],
                    "age_description": parent_info["age_description"],
                    "gender": parent_info["gender"]
                },
                "assessment_case": {
                    "target_word": parent_info["target_word"],
                    "target_phoneme": parent_info["target_phoneme"],
                    "suspected_error_type": parent_info["error_type"],
                    "parent_description": parent_info["parent_description"],
                    "concern_level": parent_info["concern_level"]
                },
                "professional_assessment": llm_response,
                "dialogue": self.format_as_dialogue(parent_info, llm_response),
                "metadata": {
                    "generation_method": "llm_assisted",
                    "api_model": "qwen-turbo-latest",
                    "assessment_type": "parent_reported"
                }
            }
            
            return record
            
        except Exception as e:
            logger.error(f"生成记录失败: {e}")
            return None
    
    def format_as_dialogue(self, parent_info: Dict[str, Any], llm_response: str) -> str:
        """将评估格式化为对话形式"""
        
        dialogue = f"""<开始对话>
<家长 1>: {parent_info['parent_description']}
<专家 1>: 我来为您分析一下孩子的情况。

{llm_response}

希望这些建议对您有帮助。如果您还有其他问题，随时可以咨询。
<结束对话>"""
        
        return dialogue
    
    async def generate_batch_records(self, num_records: int, output_file: str, 
                                   max_concurrent: int = 10, delay: float = 0.5):
        """批量生成评估记录"""
        
        logger.info(f"开始生成 {num_records} 条语音评估记录...")
        
        # 创建信号量控制并发数
        semaphore = asyncio.Semaphore(max_concurrent)
        successful_records = 0
        failed_records = 0
        
        async def generate_with_semaphore():
            async with semaphore:
                age_months = random.randint(12, 48)
                record = await self.generate_single_record(age_months)
                await asyncio.sleep(delay)  # 控制API调用频率
                return record
        
        # 创建任务列表
        tasks = [generate_with_semaphore() for _ in range(num_records)]
        
        # 打开输出文件
        async with aiofiles.open(output_file, 'w', encoding='utf-8') as f:
            
            # 处理任务
            for i, task in enumerate(asyncio.as_completed(tasks)):
                try:
                    record = await task
                    
                    if record:
                        await f.write(json.dumps(record, ensure_ascii=False) + '\n')
                        successful_records += 1
                    else:
                        failed_records += 1
                    
                    # 进度报告
                    if (i + 1) % 50 == 0:
                        logger.info(f"已完成 {i + 1}/{num_records} 条记录 "
                                  f"(成功: {successful_records}, 失败: {failed_records})")
                        
                except Exception as e:
                    logger.error(f"处理任务失败: {e}")
                    failed_records += 1
        
        # 关闭session
        if self.session:
            await self.session.close()
        
        logger.info(f"生成完成！成功: {successful_records}, 失败: {failed_records}")
        logger.info(f"输出文件: {output_file}")
        
        return successful_records, failed_records

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="语音评估数据集生成器")
    parser.add_argument("--api_key", required=True, help="Qwen API密钥")
    parser.add_argument("--num_records", type=int, default=1000, help="生成记录数量")
    parser.add_argument("--output_file", default="speech_assessment_dataset.jsonl", help="输出文件名")
    parser.add_argument("--max_concurrent", type=int, default=10, help="最大并发数")
    parser.add_argument("--delay", type=float, default=0.5, help="API调用间隔(秒)")
    
    args = parser.parse_args()
    
    # 创建生成器
    generator = SpeechAssessmentGenerator(args.api_key)
    
    # 生成数据集
    start_time = time.time()
    successful, failed = await generator.generate_batch_records(
        num_records=args.num_records,
        output_file=args.output_file,
        max_concurrent=args.max_concurrent,
        delay=args.delay
    )
    
    end_time = time.time()
    duration = end_time - start_time
    
    logger.info(f"总用时: {duration:.1f}秒")
    logger.info(f"平均每条记录: {duration/args.num_records:.2f}秒")
    logger.info(f"成功率: {successful/(successful+failed)*100:.1f}%")

if __name__ == "__main__":
    asyncio.run(main())
