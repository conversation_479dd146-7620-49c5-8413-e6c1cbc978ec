#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

"""
语音评估数据集生成运行脚本
提供简单的命令行界面和配置管理
"""

import os
import sys
import asyncio
import argparse
from datetime import datetime
from speech_assessment_llm_generator import SpeechAssessmentGenerator

def check_dependencies():
    """检查依赖包"""
    required_packages = ['aiohttp', 'aiofiles', 'pandas', 'openpyxl']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print(f"请运行: pip install {' '.join(missing_packages)}")
        return False
    
    return True

def get_api_key():
    """获取API密钥"""
    # 优先从环境变量获取
    api_key = os.getenv("QWEN_API_KEY")
    
    if not api_key:
        # 从命令行输入获取
        api_key = input("请输入Qwen API密钥: ").strip()
    
    if not api_key or len(api_key) < 20:
        print("❌ API密钥无效")
        return None
    
    return api_key

async def generate_test_sample():
    """生成测试样本"""
    print("🔧 生成测试样本...")
    
    api_key = get_api_key()
    if not api_key:
        return False
    
    generator = SpeechAssessmentGenerator(api_key)
    
    try:
        # 生成单条记录作为测试
        record = await generator.generate_single_record(30)  # 30个月的孩子
        
        if record:
            print("✅ 测试样本生成成功！")
            print("\n📋 样本内容预览:")
            print(f"- 记录ID: {record['record_id']}")
            print(f"- 孩子年龄: {record['child_info']['age_description']}")
            print(f"- 目标词汇: {record['assessment_case']['target_word']}")
            print(f"- 家长描述: {record['assessment_case']['parent_description']}")
            print(f"- 专业评估: {record['professional_assessment'][:200]}...")
            
            # 保存测试样本
            import json
            with open("test_sample.json", "w", encoding="utf-8") as f:
                json.dump(record, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 测试样本已保存到: test_sample.json")
            return True
        else:
            print("❌ 测试样本生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        if generator.session:
            await generator.session.close()

async def generate_full_dataset(num_records: int, output_file: str = None):
    """生成完整数据集"""
    
    if not output_file:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"speech_assessment_dataset_{timestamp}.jsonl"
    
    print(f"🚀 开始生成 {num_records} 条语音评估数据...")
    print(f"📁 输出文件: {output_file}")
    
    api_key = get_api_key()
    if not api_key:
        return False
    
    generator = SpeechAssessmentGenerator(api_key)
    
    try:
        successful, failed = await generator.generate_batch_records(
            num_records=num_records,
            output_file=output_file,
            max_concurrent=8,  # 适中的并发数
            delay=0.6  # 稍微保守的延迟
        )
        
        print(f"\n✅ 数据集生成完成！")
        print(f"📊 统计信息:")
        print(f"  - 成功生成: {successful} 条")
        print(f"  - 失败记录: {failed} 条")
        print(f"  - 成功率: {successful/(successful+failed)*100:.1f}%")
        print(f"  - 输出文件: {output_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")
        return False

def analyze_dataset(dataset_file: str):
    """分析生成的数据集"""
    try:
        import json
        
        print(f"📊 分析数据集: {dataset_file}")
        
        records = []
        with open(dataset_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    records.append(json.loads(line))
        
        if not records:
            print("❌ 数据集为空")
            return
        
        print(f"\n📈 数据集统计:")
        print(f"- 总记录数: {len(records)}")
        
        # 年龄分布
        ages = [r['child_info']['age_months'] for r in records]
        age_groups = {
            "12-18个月": len([age for age in ages if 12 <= age < 18]),
            "18-24个月": len([age for age in ages if 18 <= age < 24]),
            "24-36个月": len([age for age in ages if 24 <= age < 36]),
            "36-48个月": len([age for age in ages if 36 <= age <= 48])
        }
        
        print(f"\n👶 年龄分布:")
        for age_group, count in age_groups.items():
            percentage = count / len(records) * 100
            print(f"  {age_group}: {count} 条 ({percentage:.1f}%)")
        
        # 性别分布
        genders = {}
        for record in records:
            gender = record['child_info']['gender']
            genders[gender] = genders.get(gender, 0) + 1
        
        print(f"\n👫 性别分布:")
        for gender, count in genders.items():
            percentage = count / len(records) * 100
            print(f"  {gender}: {count} 条 ({percentage:.1f}%)")
        
        # 词汇分布
        words = {}
        for record in records:
            word = record['assessment_case']['target_word']
            words[word] = words.get(word, 0) + 1
        
        print(f"\n🔤 最常见的测试词汇 (前10个):")
        sorted_words = sorted(words.items(), key=lambda x: x[1], reverse=True)
        for word, count in sorted_words[:10]:
            print(f"  {word}: {count} 次")
        
        # 错误类型分布
        error_types = {}
        for record in records:
            error_type = record['assessment_case']['suspected_error_type']
            error_types[error_type] = error_types.get(error_type, 0) + 1
        
        print(f"\n⚠️ 错误类型分布:")
        for error_type, count in error_types.items():
            percentage = count / len(records) * 100
            print(f"  {error_type}: {count} 条 ({percentage:.1f}%)")
        
        print(f"\n✅ 数据集分析完成")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="语音评估数据集生成工具")
    
    parser.add_argument("--mode", choices=["test", "generate", "analyze"], 
                       default="test", help="运行模式")
    
    parser.add_argument("--records", type=int, default=1000,
                       help="生成记录数量 (默认: 1000)")
    
    parser.add_argument("--output", type=str, default=None,
                       help="输出文件名 (默认: 自动生成)")
    
    parser.add_argument("--dataset", type=str, default=None,
                       help="要分析的数据集文件")
    
    args = parser.parse_args()
    
    print("🗣️ 语音评估数据集生成工具")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    if args.mode == "test":
        print("🔧 测试模式 - 生成单条样本记录")
        success = asyncio.run(generate_test_sample())
        if not success:
            sys.exit(1)
            
    elif args.mode == "generate":
        print(f"🚀 生成模式 - 生成 {args.records} 条记录")
        success = asyncio.run(generate_full_dataset(args.records, args.output))
        if not success:
            sys.exit(1)
            
    elif args.mode == "analyze":
        if not args.dataset:
            print("❌ 分析模式需要指定 --dataset 参数")
            sys.exit(1)
        
        if not os.path.exists(args.dataset):
            print(f"❌ 数据集文件不存在: {args.dataset}")
            sys.exit(1)
        
        analyze_dataset(args.dataset)
    
    print("\n🎉 操作完成！")

if __name__ == "__main__":
    main()
