#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

"""
增强版语音评估数据生成器
基于构音评估表+Qwen LLM，参考refgpt的高效并发处理方法
生成1000条父母描述→LLM判断→专业指导的数据集
"""

import json
import asyncio
import aiohttp
import random
import time
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from queue import PriorityQueue
import numpy as np
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 设置随机种子
random.seed(42)
np.random.seed(42)

class APIRequest:
    """API请求类，参考refgpt的设计"""
    
    def __init__(self, task_id: int, request_data: Dict[str, Any], attempts_left: int = 3):
        self.task_id = task_id
        self.request_data = request_data
        self.attempts_left = attempts_left
        self.result = []
    
    async def call_api(self, session: aiohttp.ClientSession, api_key: str, api_url: str, 
                      retry_queue: asyncio.Queue, status_tracker: 'StatusTracker'):
        """异步调用API"""
        logger.info(f"开始处理请求 #{self.task_id}")
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        try:
            async with session.post(api_url, headers=headers, json=self.request_data, timeout=30) as response:
                response.raise_for_status()
                result = await response.json()
                
                if "choices" in result and len(result["choices"]) > 0:
                    content = result["choices"][0]["message"]["content"].strip()
                    self.result = content
                    status_tracker.num_tasks_succeeded += 1
                    logger.info(f"请求 #{self.task_id} 成功完成")
                    return True
                else:
                    logger.error(f"请求 #{self.task_id} 响应格式异常: {result}")
                    raise Exception("响应格式异常")
                    
        except Exception as e:
            logger.warning(f"请求 #{self.task_id} 失败: {e}")
            status_tracker.num_other_errors += 1
            
            if self.attempts_left > 0:
                self.attempts_left -= 1
                await retry_queue.put(self)
                logger.info(f"请求 #{self.task_id} 将重试，剩余尝试次数: {self.attempts_left}")
            else:
                logger.error(f"请求 #{self.task_id} 所有重试均失败")
                status_tracker.num_tasks_failed += 1
            
            return False

class StatusTracker:
    """状态跟踪器，参考refgpt的设计"""
    
    def __init__(self):
        self.num_tasks_started = 0
        self.num_tasks_in_progress = 0
        self.num_tasks_succeeded = 0
        self.num_tasks_failed = 0
        self.num_rate_limit_errors = 0
        self.num_api_errors = 0
        self.num_other_errors = 0
        self.time_of_last_rate_limit_error = 0

class EnhancedSpeechAssessmentGenerator:
    """增强版语音评估数据生成器"""
    
    def __init__(self, api_config_file: str = "api_config.jsonl"):
        # 加载API配置
        self.api_config = self.load_api_config(api_config_file)
        self.api_key = self.api_config["api-key"]
        self.api_url = self.api_config["request_url"]
        
        # 加载52个词汇和音素
        self.word_phoneme_pairs = self.load_word_list()
        
        # 构音问题类型和描述模板
        self.error_types = {
            "替代": {
                "description": "用其他音替代目标音",
                "parent_templates": [
                    "孩子说'{word}'的时候，总是发成别的音，听起来不太对",
                    "'{word}'这个词孩子发音不准，好像是用其他音代替了",
                    "孩子说'{word}'时发音有问题，感觉是发成了其他的音"
                ]
            },
            "省略": {
                "description": "省略某个音素或音节",
                "parent_templates": [
                    "孩子说'{word}'的时候，好像少了一个音，听起来不完整",
                    "'{word}'这个词孩子说得不完整，感觉漏了什么",
                    "孩子说'{word}'时总是省略一些音，听起来很短"
                ]
            },
            "歪曲": {
                "description": "发音不准确但可识别",
                "parent_templates": [
                    "孩子说'{word}'的发音听起来怪怪的，但还能听懂",
                    "'{word}'这个词孩子能说出来，但发音不太标准",
                    "孩子说'{word}'时发音有点模糊，不够清楚"
                ]
            },
            "增加": {
                "description": "添加额外的音素",
                "parent_templates": [
                    "孩子说'{word}'的时候，好像多加了一些音",
                    "'{word}'这个词孩子说得比较长，感觉多了什么",
                    "孩子说'{word}'时会加一些额外的音进去"
                ]
            }
        }
        
        # 年龄段特征
        self.age_characteristics = {
            "12-18": {
                "common_errors": ["省略", "替代"], 
                "concern_level": "观察",
                "development_stage": "早期词汇期"
            },
            "18-24": {
                "common_errors": ["替代", "歪曲"], 
                "concern_level": "轻度关注",
                "development_stage": "词汇爆发期"
            },
            "24-36": {
                "common_errors": ["歪曲", "替代"], 
                "concern_level": "需要注意",
                "development_stage": "语法发展期"
            },
            "36-48": {
                "common_errors": ["歪曲", "增加"], 
                "concern_level": "建议评估",
                "development_stage": "语音完善期"
            }
        }
    
    def load_api_config(self, config_file: str) -> Dict[str, str]:
        """加载API配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.loads(f.readline().strip())
        except Exception as e:
            logger.error(f"加载API配置失败: {e}")
            raise
    
    def load_word_list(self) -> List[Dict[str, str]]:
        """加载52个标准化测试词汇"""
        # 基于构音评估表的52个词汇
        words = [
            {"word": "桌", "phoneme": "zh"}, {"word": "象", "phoneme": "iang"},
            {"word": "包", "phoneme": "b"}, {"word": "抛", "phoneme": "p"},
            {"word": "猫", "phoneme": "m"}, {"word": "刀", "phoneme": "d"},
            {"word": "跳", "phoneme": "t"}, {"word": "闹", "phoneme": "n"},
            {"word": "高", "phoneme": "g"}, {"word": "考", "phoneme": "k"},
            {"word": "好", "phoneme": "h"}, {"word": "老", "phoneme": "l"},
            {"word": "找", "phoneme": "zh"}, {"word": "草", "phoneme": "c"},
            {"word": "少", "phoneme": "sh"}, {"word": "扫", "phoneme": "s"},
            {"word": "饶", "phoneme": "r"}, {"word": "早", "phoneme": "z"},
            {"word": "鸟", "phoneme": "n"}, {"word": "要", "phoneme": "y"},
            {"word": "挖", "phoneme": "w"}, {"word": "花", "phoneme": "h"},
            {"word": "瓜", "phoneme": "g"}, {"word": "鸭", "phoneme": "y"},
            {"word": "茶", "phoneme": "ch"}, {"word": "蛇", "phoneme": "sh"},
            {"word": "菇", "phoneme": "g"}, {"word": "哭", "phoneme": "k"},
            {"word": "壳", "phoneme": "k"}, {"word": "纸", "phoneme": "zh"},
            {"word": "字", "phoneme": "z"}, {"word": "刺", "phoneme": "c"},
            {"word": "丝", "phoneme": "s"}, {"word": "日", "phoneme": "r"},
            {"word": "鱼", "phoneme": "y"}, {"word": "猪", "phoneme": "zh"},
            {"word": "书", "phoneme": "sh"}, {"word": "树", "phoneme": "sh"},
            {"word": "出", "phoneme": "ch"}, {"word": "醋", "phoneme": "c"},
            {"word": "苏", "phoneme": "s"}, {"word": "如", "phoneme": "r"},
            {"word": "女", "phoneme": "n"}, {"word": "绿", "phoneme": "l"},
            {"word": "驴", "phoneme": "l"}, {"word": "鹿", "phoneme": "l"},
            {"word": "路", "phoneme": "l"}, {"word": "炉", "phoneme": "l"},
            {"word": "布", "phoneme": "b"}, {"word": "肚", "phoneme": "d"},
            {"word": "兔", "phoneme": "t"}, {"word": "虎", "phoneme": "h"},
            {"word": "鼓", "phoneme": "g"}, {"word": "裤", "phoneme": "k"}
        ]
        return words
    
    def generate_parent_case(self, age_months: int) -> Dict[str, Any]:
        """生成父母描述的案例"""
        # 确定年龄段
        if age_months < 18:
            age_key = "12-18"
        elif age_months < 24:
            age_key = "18-24"
        elif age_months < 36:
            age_key = "24-36"
        else:
            age_key = "36-48"
        
        age_char = self.age_characteristics[age_key]
        
        # 随机选择词汇和错误类型
        word_info = random.choice(self.word_phoneme_pairs)
        error_type = random.choice(age_char["common_errors"])
        
        # 生成父母描述
        template = random.choice(self.error_types[error_type]["parent_templates"])
        description = template.format(word=word_info["word"])
        
        # 添加年龄和背景信息
        age_desc = f"{age_months // 12}岁{age_months % 12}个月"
        gender = random.choice(["男孩", "女孩"])
        
        context_info = [
            f"我家{gender}{age_desc}了",
            f"孩子现在{age_desc}",
            f"我的{gender}孩子{age_desc}"
        ]
        
        context = random.choice(context_info)
        
        # 添加父母的担心程度
        concern_levels = {
            "观察": "我想了解一下这是否正常",
            "轻度关注": "我有点担心，想咨询一下",
            "需要注意": "我比较担心，这需要干预吗",
            "建议评估": "我很担心，是否需要专业评估"
        }
        
        concern = concern_levels[age_char["concern_level"]]
        full_description = f"{context}，{description}。{concern}？"
        
        return {
            "age_months": age_months,
            "age_description": age_desc,
            "gender": gender,
            "target_word": word_info["word"],
            "target_phoneme": word_info["phoneme"],
            "error_type": error_type,
            "parent_description": full_description,
            "concern_level": age_char["concern_level"],
            "development_stage": age_char["development_stage"]
        }
    
    def create_llm_request(self, parent_case: Dict[str, Any]) -> Dict[str, Any]:
        """创建LLM请求数据"""
        system_prompt = """你是一位专业的语音病理学家和儿童发展专家，具有丰富的婴幼儿语音构音评估和治疗经验。你需要根据家长的描述，判断孩子是否存在构音问题，并给出专业的指导建议。

请按照以下格式回复：

1. **问题判断**：[正常发展/轻度问题/中度问题/需要专业评估]
2. **问题分析**：[详细分析发音问题的类型和可能原因，150字以内]
3. **年龄适宜性**：[评估该问题在此年龄段是否常见，100字以内]
4. **指导建议**：[具体的家庭练习方法和注意事项，200字以内]
5. **随访建议**：[何时复查或寻求专业帮助，100字以内]

请确保建议专业、实用、易于家长理解和执行。总字数控制在600字以内。"""

        user_prompt = f"""
家长描述：{parent_case['parent_description']}

请根据以上描述进行专业评估和指导。

补充信息：
- 孩子年龄：{parent_case['age_description']}
- 涉及词汇："{parent_case['target_word']}"
- 目标音素：{parent_case['target_phoneme']}
- 发展阶段：{parent_case['development_stage']}
"""
        
        return {
            "model": "qwen-turbo",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            "temperature": 0.7,
            "max_tokens": 800
        }

    def create_dialogue_from_assessment(self, parent_case: Dict[str, Any], assessment: str) -> str:
        """基于评估结果创建对话"""
        dialogue_parts = [
            "<开始对话>",
            f"<家长 1>: {parent_case['parent_description']}",
            f"<专家 1>: 我来为您分析一下孩子的情况。{assessment}",
            "<家长 2>: 谢谢您的专业建议，我会按照您说的方法来帮助孩子练习。",
            "<专家 2>: 不客气。记住要保持耐心，语音发展是一个渐进的过程。如果有任何问题，随时可以咨询。",
            "<结束对话>"
        ]
        return "\n".join(dialogue_parts)

    async def process_requests_concurrently(self,
                                          requests: List[APIRequest],
                                          max_requests_per_minute: float = 60,
                                          max_concurrent: int = 10) -> List[Dict[str, Any]]:
        """并发处理API请求，参考refgpt的方法"""

        # 初始化状态跟踪器
        status_tracker = StatusTracker()

        # 创建重试队列
        retry_queue = asyncio.Queue()

        # 创建结果列表
        results = []

        # 计算请求间隔
        seconds_to_pause_after_rate_limit_error = 15
        seconds_to_sleep_each_loop = 0.01

        # 创建HTTP会话
        async with aiohttp.ClientSession() as session:

            # 创建任务队列
            available_request_capacity = max_concurrent
            next_request = None
            request_iterator = iter(requests)

            logger.info(f"开始处理 {len(requests)} 个请求，最大并发数: {max_concurrent}")

            while True:
                # 获取下一个请求
                if next_request is None:
                    if not retry_queue.empty():
                        next_request = await retry_queue.get()
                        logger.debug(f"重试请求 {next_request.task_id}")
                    else:
                        try:
                            next_request = next(request_iterator)
                        except StopIteration:
                            if status_tracker.num_tasks_in_progress == 0:
                                break
                            else:
                                await asyncio.sleep(seconds_to_sleep_each_loop)
                                continue

                # 检查是否有可用容量
                if available_request_capacity > 0 and next_request:
                    # 保存当前请求的引用
                    current_request = next_request

                    # 启动请求任务
                    task = asyncio.create_task(
                        current_request.call_api(session, self.api_key, self.api_url, retry_queue, status_tracker)
                    )

                    # 更新状态
                    available_request_capacity -= 1
                    status_tracker.num_tasks_in_progress += 1
                    status_tracker.num_tasks_started += 1
                    next_request = None

                    # 添加任务完成回调
                    def task_done_callback(task_obj, request_obj):
                        nonlocal available_request_capacity
                        available_request_capacity += 1
                        status_tracker.num_tasks_in_progress -= 1

                        # 保存结果
                        if hasattr(request_obj, 'result') and request_obj.result:
                            results.append({
                                'task_id': request_obj.task_id,
                                'parent_case': request_obj.parent_case,
                                'assessment': request_obj.result
                            })

                    task.add_done_callback(lambda t, req=current_request: task_done_callback(t, req))

                # 短暂休眠以避免过度占用CPU
                await asyncio.sleep(seconds_to_sleep_each_loop)

                # 定期打印进度
                if status_tracker.num_tasks_started % 50 == 0:
                    logger.info(f"进度: {status_tracker.num_tasks_succeeded}/{len(requests)} 成功, "
                              f"{status_tracker.num_tasks_failed} 失败, "
                              f"{status_tracker.num_tasks_in_progress} 进行中")

        logger.info(f"所有请求处理完成。成功: {status_tracker.num_tasks_succeeded}, "
                   f"失败: {status_tracker.num_tasks_failed}")

        return results

    async def generate_dataset(self, num_records: int = 1000,
                             output_file: str = "speech_assessment_dataset.jsonl",
                             max_concurrent: int = 10) -> None:
        """生成完整的数据集"""

        logger.info(f"开始生成 {num_records} 条语音评估数据")

        # 1. 生成所有父母案例
        logger.info("生成父母描述案例...")
        parent_cases = []
        for i in range(num_records):
            age_months = random.randint(12, 48)
            case = self.generate_parent_case(age_months)
            parent_cases.append(case)

        # 2. 创建API请求
        logger.info("创建API请求...")
        api_requests = []
        for i, case in enumerate(parent_cases):
            llm_request_data = self.create_llm_request(case)
            api_request = APIRequest(
                task_id=i,
                request_data={
                    'parent_case': case,
                    'llm_request': llm_request_data
                }
            )
            # 将LLM请求数据设置为API请求的数据
            api_request.request_data = llm_request_data
            # 保存父母案例信息
            api_request.parent_case = case
            api_requests.append(api_request)

        # 3. 并发处理请求
        logger.info("开始并发处理API请求...")
        start_time = time.time()

        results = await self.process_requests_concurrently(
            api_requests,
            max_concurrent=max_concurrent
        )

        end_time = time.time()
        duration = end_time - start_time

        # 4. 构建完整记录并保存
        logger.info("构建完整记录...")
        complete_records = []

        for result in results:
            parent_case = result['parent_case']
            assessment = result['assessment']

            # 创建对话
            dialogue = self.create_dialogue_from_assessment(parent_case, assessment)

            # 构建完整记录
            record = {
                "record_id": f"ESA_{datetime.now().strftime('%Y%m%d')}_{result['task_id']:04d}",
                "source": "enhanced_speech_assessment_llm",
                "created_at": datetime.now().isoformat(),
                "child_info": {
                    "age_months": parent_case["age_months"],
                    "age_description": parent_case["age_description"],
                    "gender": parent_case["gender"],
                    "development_stage": parent_case["development_stage"]
                },
                "assessment_case": {
                    "target_word": parent_case["target_word"],
                    "target_phoneme": parent_case["target_phoneme"],
                    "suspected_error_type": parent_case["error_type"],
                    "parent_description": parent_case["parent_description"],
                    "concern_level": parent_case["concern_level"]
                },
                "professional_assessment": assessment,
                "dialogue": dialogue,
                "metadata": {
                    "generation_method": "llm_assisted_concurrent",
                    "api_model": "qwen-turbo",
                    "assessment_type": "parent_reported"
                }
            }

            complete_records.append(record)

        # 5. 保存到文件
        logger.info(f"保存 {len(complete_records)} 条记录到 {output_file}")
        with open(output_file, 'w', encoding='utf-8') as f:
            for record in complete_records:
                f.write(json.dumps(record, ensure_ascii=False) + '\n')

        # 6. 输出统计信息
        logger.info(f"数据集生成完成！")
        logger.info(f"总用时: {duration:.1f}秒")
        if len(complete_records) > 0:
            logger.info(f"平均每条记录: {duration/len(complete_records):.2f}秒")
            logger.info(f"成功率: {len(complete_records)/num_records*100:.1f}%")
        else:
            logger.warning("没有成功生成任何记录")
        logger.info(f"成功生成: {len(complete_records)} 条记录")

        return complete_records

async def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="增强版语音评估数据集生成器")
    parser.add_argument("--num_records", type=int, default=1000,
                       help="生成记录数量 (默认: 1000)")
    parser.add_argument("--output_file", type=str, default="enhanced_speech_assessment_dataset.jsonl",
                       help="输出文件名 (默认: enhanced_speech_assessment_dataset.jsonl)")
    parser.add_argument("--max_concurrent", type=int, default=10,
                       help="最大并发请求数 (默认: 10)")
    parser.add_argument("--api_config", type=str, default="api_config.jsonl",
                       help="API配置文件 (默认: api_config.jsonl)")
    parser.add_argument("--test_mode", action="store_true",
                       help="测试模式，只生成5条记录")

    args = parser.parse_args()

    # 测试模式
    if args.test_mode:
        args.num_records = 5
        args.output_file = "test_enhanced_speech_assessment.jsonl"
        logger.info("运行在测试模式")

    try:
        # 创建生成器
        generator = EnhancedSpeechAssessmentGenerator(args.api_config)

        # 生成数据集
        records = await generator.generate_dataset(
            num_records=args.num_records,
            output_file=args.output_file,
            max_concurrent=args.max_concurrent
        )

        # 显示样例
        if records:
            logger.info("\n" + "="*50)
            logger.info("样例记录:")
            logger.info("="*50)
            sample_record = records[0]

            print(f"\n记录ID: {sample_record['record_id']}")
            print(f"儿童信息: {sample_record['child_info']['age_description']} {sample_record['child_info']['gender']}")
            print(f"目标词汇: {sample_record['assessment_case']['target_word']} (音素: {sample_record['assessment_case']['target_phoneme']})")
            print(f"错误类型: {sample_record['assessment_case']['suspected_error_type']}")
            print(f"\n父母描述:")
            print(sample_record['assessment_case']['parent_description'])
            print(f"\n专业评估:")
            print(sample_record['professional_assessment'][:200] + "..." if len(sample_record['professional_assessment']) > 200 else sample_record['professional_assessment'])
            print(f"\n对话片段:")
            dialogue_lines = sample_record['dialogue'].split('\n')[:4]
            for line in dialogue_lines:
                print(line)
            print("...")

        logger.info(f"\n✅ 数据集生成完成！文件保存为: {args.output_file}")

    except Exception as e:
        logger.error(f"生成数据集时发生错误: {e}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
