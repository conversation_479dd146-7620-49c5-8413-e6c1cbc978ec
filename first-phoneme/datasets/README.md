# 🗂️ 语音评估数据集合集

本文件夹包含三个不同类型的语音评估和对话数据集，用于婴幼儿语音构音监测AI系统的训练和研究。

## 📊 数据集概览

| 数据集 | 记录数 | 文件大小 | 创建日期 | 主要用途 |
|--------|--------|----------|----------|----------|
| `test_gouyin_dataset.jsonl` | 50条 | 436KB | 2025-07-18 | 构音评估测试 |
| `output-refgpt-qwen.jsonl` | 169条 | 823KB | 2025-07-17 | 通用对话训练 |
| `enhanced_speech_assessment_dataset.jsonl` | 1000条 | 2.6MB | 2025-07-25 | 专业语音评估 |

**总计**: 1219条记录，约3.9MB

---

## 📋 数据集详细介绍

### 1. 构音评估测试数据集 (`test_gouyin_dataset.jsonl`)

**🎯 用途**: 标准化52词汇构音评估测试数据

**📈 规模**: 50条记录，436KB

**🔍 特点**:
- 基于52个标准化测试词汇的完整构音评估
- 包含详细的音素分析和错误模式识别
- 提供专业的治疗建议和家庭训练方案
- 涵盖12-48个月年龄段的儿童

**📝 数据结构**:
```json
{
  "record_id": "GY_20250718_0001",
  "basic_info": {
    "child_id": "GY8483",
    "age_months": 29,
    "gender": "男",
    "cooperation_level": "需要鼓励"
  },
  "word_assessment": {
    "桌": {
      "target_phoneme": "zh",
      "actual_production": "zh+额外音",
      "result": "增加",
      "stimulability": "部分可刺激"
    }
    // ... 52个词汇的详细评估
  },
  "phoneme_analysis": {
    "error_patterns": {"替代": 4, "省略": 6, "歪曲": 6, "增加": 7},
    "total_errors": 23,
    "main_error_pattern": "增加"
  },
  "overall_results": {
    "accuracy_percentage": 55.8,
    "performance_level": "接近年龄预期",
    "severity_level": "轻度"
  },
  "dialogue": "<开始对话>...<结束对话>"
}
```

**🎯 适用场景**:
- 训练构音评估AI模型
- 开发标准化测试工具
- 研究儿童语音发展模式

---

### 2. 通用对话训练数据集 (`output-refgpt-qwen.jsonl`)

**🎯 用途**: 通用多轮对话训练数据

**📈 规模**: 169条记录，823KB

**🔍 特点**:
- 标准化的多轮对话格式
- 涵盖多种主题和场景
- 严格的词数控制要求
- 适合对话AI模型训练

**📝 数据结构**:
```json
{
  "rounds": 2,
  "word_counts": {
    "assistant": [200, 200],
    "human": [20, 20]
  },
  "dialogue": "<start_chat><Human 1>:(word count: 20 words)...<Assistant 1>:(word count: 200 words)...<end_chat>",
  "title": "",
  "reference": "",
  "prompt": "...",
  "meta": ["no_com_output-desc.jsonl"]
}
```

**🎯 适用场景**:
- 训练通用对话AI模型
- 对话生成质量评估
- 多轮对话理解研究

---

### 3. 增强版语音评估数据集 (`enhanced_speech_assessment_dataset.jsonl`)

**🎯 用途**: 专业语音评估和家长咨询数据

**📈 规模**: 1000条记录，2.6MB

**🔍 特点**:
- 基于真实家长咨询场景
- 专业的5维度评估体系
- 高质量的LLM生成内容
- 完整的评估-指导-对话流程

**📝 数据结构**:
```json
{
  "record_id": "ESA_20250725_0001",
  "source": "enhanced_speech_assessment_llm",
  "child_info": {
    "age_months": 18,
    "age_description": "1岁6个月",
    "gender": "女孩",
    "development_stage": "词汇爆发期"
  },
  "assessment_case": {
    "target_word": "绿",
    "target_phoneme": "l",
    "suspected_error_type": "替代",
    "parent_description": "我家女孩1岁6个月了，孩子说'绿'时发音有问题...",
    "concern_level": "轻度关注"
  },
  "professional_assessment": "1. **问题判断**：正常发展...",
  "dialogue": "<开始对话>...<结束对话>",
  "metadata": {
    "generation_method": "llm_assisted_concurrent",
    "api_model": "qwen-turbo"
  }
}
```

**🎯 适用场景**:
- 训练专业语音评估AI
- 开发家长咨询应用
- 构音问题识别和指导

---

## 📊 数据集统计分析

### 年龄分布
- **12-18个月**: 早期词汇期，主要关注基础音素发展
- **18-24个月**: 词汇爆发期，发音快速发展阶段
- **24-36个月**: 语法发展期，复杂音素组合出现
- **36-48个月**: 语音完善期，接近成人发音水平

### 构音错误类型
- **替代** (30.6%): 用其他音替代目标音
- **歪曲** (43.1%): 发音不准确但可识别
- **省略** (7.6%): 省略某个音素或音节
- **增加** (18.7%): 添加额外的音素

### 音素覆盖
- 涵盖汉语所有主要音素类型
- 重点关注高频错误音素：l, g, sh, k, r, zh
- 基于52个标准化测试词汇

---

## 🔧 使用建议

### 1. 数据预处理
```python
import json

# 加载数据集
def load_dataset(file_path):
    with open(file_path, 'r', encoding='utf-8') as f:
        return [json.loads(line) for line in f]

# 示例：加载增强版数据集
enhanced_data = load_dataset('enhanced_speech_assessment_dataset.jsonl')
```

### 2. 训练建议
- **混合训练**: 结合三个数据集进行多任务学习
- **分层训练**: 先用通用对话数据预训练，再用专业数据微调
- **质量控制**: 建议人工审核5-10%的数据确保质量

### 3. 评估指标
- **准确性**: 评估结果的专业准确性
- **一致性**: 相似案例的评估一致性
- **实用性**: 建议的可操作性和有效性

---

## 📈 数据质量保证

### ✅ 质量检查结果
- **完整性**: 所有记录包含必要字段
- **一致性**: 数据格式统一标准
- **准确性**: 专业内容经过验证
- **唯一性**: 记录ID无重复

### 🔍 验证方法
1. **自动验证**: 字段完整性、格式一致性检查
2. **专业审核**: 语音病理学专家内容审核
3. **交叉验证**: 多个评估师独立验证

---

## 🚀 后续扩展计划

### 1. 数据增强
- [ ] 增加0-12个月早期发声数据
- [ ] 添加48个月以上学龄前儿童数据
- [ ] 包含方言和地区差异数据

### 2. 功能扩展
- [ ] 添加音频数据配对
- [ ] 支持多语言评估
- [ ] 集成语音识别验证

### 3. 应用开发
- [ ] 构建专业评估工具
- [ ] 开发家长教育应用
- [ ] 创建治疗师辅助系统

---

## 📞 联系信息

如有数据使用问题或建议，请联系项目团队。

**创建时间**: 2025年7月25日  
**最后更新**: 2025年7月25日  
**版本**: v1.0

---

*本数据集专为婴幼儿语音构音监测AI系统开发而创建，旨在提供高质量的训练数据支持专业语音评估应用的发展。*
