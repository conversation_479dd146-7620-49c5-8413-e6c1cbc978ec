#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

"""
测试 Qwen API 调用的简单脚本
"""

import json
import aiohttp
import asyncio

async def test_qwen_api():
    """测试 Qwen API 调用"""
    
    # 从配置文件读取 API 信息
    with open("api_config.jsonl", "r", encoding="utf8") as f:
        api_config = json.loads(f.readline().strip())
    
    # 设置请求头
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_config['api-key']}"
    }
    
    # 设置请求数据
    data = {
        "model": "qwen-turbo",
        "messages": [
            {
                "role": "system",
                "content": "你是一个有用的助手。"
            },
            {
                "role": "user", 
                "content": "你好，请简单介绍一下自己。"
            }
        ],
        "temperature": 1.0,
        "max_tokens": 100,
        "top_p": 1.0
    }
    
    # 发送请求
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                url=api_config['request_url'],
                headers=headers,
                json=data
            ) as response:
                result = await response.json()
                
                if "error" in result:
                    print(f"API 错误: {result['error']}")
                    return False
                else:
                    print("API 调用成功!")
                    print(f"响应: {result['choices'][0]['message']['content']}")
                    return True
                    
    except Exception as e:
        print(f"请求异常: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_qwen_api())
    if success:
        print("\n✅ Qwen API 配置正确，可以正常使用！")
    else:
        print("\n❌ Qwen API 配置有问题，请检查配置文件和网络连接。")
