#!/usr/bin/env python3
# -*- encoding:utf-8 -*-

import json
import re

def translate_speech_therapy_dialogue(text):
    """专业翻译语音治疗对话"""
    
    # 完整句子翻译映射
    complete_sentences = {
        # 问题
        "How can speech disorders be treated?": "语音障碍如何治疗？",
        "How do I say the /f/ sound?": "我如何发/f/音？",
        "How can I help my child with lip movement?": "我如何帮助我的孩子练习唇部运动？",
        "Why is the K sound hard for kids to say?": "为什么K音对孩子来说很难发？",
        "How can I help my child practice the /j/ sound?": "我如何帮助我的孩子练习/j/音？",
        "Can you guide me through a speech therapy exercise for a child?": "你能指导我为孩子做语音治疗练习吗？",
        "I need help understanding how to teach the /r/ sound to a child.": "我需要帮助了解如何教孩子发/r/音。",
        "What if the cereal falls off?": "如果谷物圈掉了怎么办？",
        "What if I feel air for /b/?": "如果我发/b/音时感觉到气流怎么办？",
        "Can you give an example word?": "你能给个例子单词吗？",
        "What if I still can't tell them apart?": "如果我仍然无法区分它们怎么办？",
        "Can you explain how mirroring helps in therapy?": "你能解释镜像在治疗中如何帮助吗？",
        "What if I want to try some exercises at home?": "如果我想在家尝试一些练习怎么办？",
        "How can we help a child practice the K sound?": "我们如何帮助孩子练习K音？",
        "Can you give an example of a game?": "你能给个游戏的例子吗？",
        "How can I improve my public speaking skills?": "我如何提高我的公共演讲技能？",
        "What are some common mistakes to avoid?": "有哪些常见错误需要避免？",
        "How do I choose the right sounds for the child to practice?": "我如何为孩子选择合适的练习音素？",
        "What if the child gets frustrated?": "如果孩子感到沮丧怎么办？",
        "How can this help someone learning English?": "这如何帮助学习英语的人？",
        "Can you teach me a fun way to say them?": "你能教我一个有趣的说法吗？",
        "What if we don't have a sensory bin?": "如果我们没有感官箱怎么办？",
        "Can you suggest more animals with the /j/ sound?": "你能推荐更多带有/j/音的动物吗？",
        "How do I make this activity educational?": "我如何让这个活动具有教育意义？",
        "How do I check if I'm doing it right?": "我如何检查自己做得对不对？",
        "Can you walk me through it step by step?": "你能一步步指导我吗？",
        "Can you recommend a screening tool?": "你能推荐一个筛查工具吗？",
        "What are effective techniques for real-life applications?": "现实应用中有哪些有效技巧？",
        "How do you handle resistance or frustration?": "如何处理抗拒或沮丧情绪？",
        
        # 回答开头
        "To help your child with lip movement": "为了帮助你的孩子练习唇部运动",
        "To make the /f/ sound": "要发/f/音",
        "Speech disorders can often be treated through therapy with a licensed speech-language pathologist.": "语音障碍通常可以通过持证语音语言病理学家的治疗来改善。",
        "The /k/ sound is made in the back of the throat": "/k/音是在喉咙后部发出的",
        "Mirroring is a helpful technique in speech therapy": "镜像是语音治疗中的一个有用技巧",
        "That's a great idea!": "这是个好主意！",
        "Sure! Let's use the word": "当然！我们用这个词",
        "If you feel air when making the /b/ sound": "如果你在发/b/音时感觉到气流",
        "You're not alone if you find it hard to tell the difference!": "如果你觉得很难区分，你并不孤单！",
        "Of course! To help with the /v/ sound": "当然！为了帮助练习/v/音",
        "To help a child practice the /j/ sound": "为了帮助孩子练习/j/音",
        "If you don't have a sensory bin": "如果你没有感官箱",
        "Of course! Here are some animals that start with the /j/ sound": "当然！这里有一些以/j/音开头的动物",
        "To make the activity educational": "为了让活动具有教育意义",
        "You can check by placing your hand in front of your mouth.": "你可以把手放在嘴前来检查。",
        "To guide you through the /v/ sound step by step": "一步步指导你发/v/音",
        "A recommended screening tool for assessing /r/ stimulability": "推荐用于评估/r/音刺激性的筛查工具",
        "In real-life applications": "在现实应用中",
        "When a child shows resistance or frustration": "当孩子表现出抗拒或沮丧时",
        
        # 专业术语
        "speech-language pathologist": "语音语言病理学家",
        "licensed speech language pathologist": "持证语音语言病理学家",
        "articulation therapy": "构音治疗",
        "speech therapy": "语音治疗",
        "visual feedback": "视觉反馈",
        "mouth movements": "口部运动",
        "tongue position": "舌位",
        "vocal cords": "声带",
        "breath control": "呼吸控制",
        "pronunciation": "发音",
        "articulation": "构音",
        "speech sounds": "语音",
        "phoneme": "音素",
        "speech development": "语音发展",
        "communication skills": "沟通技能",
        "speech intelligibility": "语音清晰度",
        "speech exercises": "语音练习",
        "gestural cueing": "手势提示",
        "interactive games": "互动游戏",
        "positive reinforcement": "正面强化",
        "consistent practice": "持续练习",
        "sensory bin": "感官箱",
        "tactile cueing": "触觉提示",
        "stimulability": "刺激性",
        "screening tool": "筛查工具",
        "articulation disorder": "构音障碍",
        "speech sound disorders": "语音障碍",
        
        # 常用短语
        "word count": "字数要求",
        "further asks": "进一步询问",
        "gives specific instructions": "给出具体指示",
        "expresses his/her needs": "表达他/她的需求",
        "asks with curiosity": "好奇地询问",
        "asks in a childlike tone": "用孩子般的语气询问",
        "answers in a way that a child can understand": "用孩子能理解的方式回答",
        "detailed explanation": "详细解释",
        "from the perspective of real life": "从现实生活的角度",
        "makes a request": "提出请求",
        "asks a question": "提出问题",
        "asks in an expert's tone": "以专家的语气询问",
        "asks in an old person's tone": "以老年人的语气询问",
        "asks in a young person's tone": "以年轻人的语气询问",
        
        # 其他常见表达
        "That's normal": "这很正常",
        "Keep trying": "继续尝试",
        "You're doing great": "你做得很好",
        "Don't worry": "别担心",
        "Be patient": "要有耐心",
        "Practice regularly": "定期练习",
        "Small daily efforts": "每天的小努力",
        "With practice": "通过练习",
        "Over time": "随着时间推移",
        "It takes time": "这需要时间",
        "Remember": "记住",
        "For example": "例如",
        "Another option": "另一个选择",
        "You can also": "你也可以",
        "Additionally": "此外",
        "Furthermore": "而且",
        "However": "然而",
        "Therefore": "因此",
        "In conclusion": "总之",
    }
    
    # 应用翻译
    result = text
    for english, chinese in complete_sentences.items():
        result = re.sub(re.escape(english), chinese, result, flags=re.IGNORECASE)
    
    # 处理特殊格式
    result = re.sub(r'<start_chat>', '<开始对话>', result)
    result = re.sub(r'<end_chat>', '<结束对话>', result)
    result = re.sub(r'<Human (\d+)>', r'<人类 \1>', result)
    result = re.sub(r'<Assistant (\d+)>', r'<助手 \1>', result)
    result = re.sub(r'\(word count: (\d+) words?\)', r'(字数要求：\1字)', result)
    
    return result

def create_chinese_only_file(input_file, output_file):
    """创建纯中文翻译文件"""
    
    translated_dialogues = []
    
    with open(input_file, "r", encoding="utf8") as f:
        for line_num, line in enumerate(f, 1):
            if not line.strip():
                continue
                
            try:
                data = json.loads(line.strip())
                
                # 创建中文版本
                chinese_data = {
                    "id": line_num,
                    "rounds": data.get("rounds", 0),
                    "word_counts": data.get("word_counts", {}),
                }
                
                # 翻译对话内容
                if "dialogue" in data and data["dialogue"]:
                    chinese_data["dialogue"] = translate_speech_therapy_dialogue(data["dialogue"])
                
                # 翻译标题
                if "title" in data and data["title"]:
                    chinese_data["title"] = translate_speech_therapy_dialogue(data["title"])
                
                # 翻译参考内容
                if "reference" in data and data["reference"]:
                    chinese_data["reference"] = translate_speech_therapy_dialogue(data["reference"])
                
                # 保留原始字段作为参考
                chinese_data["original"] = {
                    "dialogue": data.get("dialogue", ""),
                    "title": data.get("title", ""),
                    "reference": data.get("reference", "")
                }
                
                # 保留其他字段
                for key in ["prompt", "meta"]:
                    if key in data:
                        chinese_data[key] = data[key]
                
                translated_dialogues.append(chinese_data)
                
                if line_num % 25 == 0:
                    print(f"已翻译 {line_num} 个对话...")
                    
            except json.JSONDecodeError as e:
                print(f"第 {line_num} 行JSON解析错误: {e}")
                continue
    
    # 保存翻译结果
    with open(output_file, "w", encoding="utf8") as f:
        for dialogue in translated_dialogues:
            f.write(json.dumps(dialogue, ensure_ascii=False) + "\n")
    
    return len(translated_dialogues)

if __name__ == "__main__":
    print("🌐 开始专业翻译语音治疗对话...")
    
    # 显示翻译示例
    sample = "How do I say the /f/ sound? To make the /f/ sound, you don't need to turn your voice on."
    translated = translate_speech_therapy_dialogue(sample)
    print(f"翻译示例:")
    print(f"原文: {sample}")
    print(f"译文: {translated}")
    
    print("\n" + "="*50)
    
    # 执行翻译
    count = create_chinese_only_file(
        "output-refgpt-qwen.jsonl",
        "output-refgpt-qwen-chinese.jsonl"
    )
    
    print(f"\n✅ 专业翻译完成！")
    print(f"成功翻译 {count} 个对话")
    print(f"中文文件: output-refgpt-qwen-chinese.jsonl")
    print(f"\n📖 文件特点:")
    print("- 专业的语音治疗术语翻译")
    print("- 保留原文作为参考")
    print("- 适合中文语音治疗师使用")
