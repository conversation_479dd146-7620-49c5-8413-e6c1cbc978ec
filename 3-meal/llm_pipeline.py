#!/usr/bin/env python3
"""
LLM数据生成管道
实现基于大语言模型的自动化营养指导数据集生成系统
"""

import json
import pandas as pd
import asyncio
import aiohttp
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import logging
from concurrent.futures import ThreadPoolExecutor
import random

from prompt_templates import NutritionPromptLibrary

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class GenerationConfig:
    """数据生成配置"""
    model: str = "gpt-3.5-turbo"
    temperature: float = 0.7
    max_tokens: int = 800
    batch_size: int = 10
    max_retries: int = 3
    delay_between_requests: float = 1.0
    quality_threshold: float = 70.0

@dataclass
class GenerationResult:
    """生成结果"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    quality_score: Optional[float] = None
    retry_count: int = 0

class LLMDataPipeline:
    """LLM数据生成管道"""
    
    def __init__(self, config: GenerationConfig, api_key: str = None):
        self.config = config
        self.api_key = api_key
        self.prompt_library = NutritionPromptLibrary()
        self.session = None
        
        # 统计信息
        self.stats = {
            "total_requests": 0,
            "successful_generations": 0,
            "failed_generations": 0,
            "quality_passed": 0,
            "quality_failed": 0,
            "total_tokens_used": 0
        }
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def call_llm_async(self, system_prompt: str, user_prompt: str) -> GenerationResult:
        """异步调用LLM"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.config.model,
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            "temperature": self.config.temperature,
            "max_tokens": self.config.max_tokens
        }
        
        for attempt in range(self.config.max_retries):
            try:
                async with self.session.post(
                    "https://api.openai.com/v1/chat/completions",
                    headers=headers,
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        content = result['choices'][0]['message']['content']
                        tokens_used = result.get('usage', {}).get('total_tokens', 0)
                        
                        self.stats["total_tokens_used"] += tokens_used
                        
                        return GenerationResult(
                            success=True,
                            data={"content": content, "tokens": tokens_used},
                            retry_count=attempt
                        )
                    else:
                        error_text = await response.text()
                        logger.warning(f"API请求失败 (状态码: {response.status}): {error_text}")
                        
            except Exception as e:
                logger.warning(f"请求异常 (尝试 {attempt + 1}/{self.config.max_retries}): {str(e)}")
                
            # 等待后重试
            if attempt < self.config.max_retries - 1:
                await asyncio.sleep(2 ** attempt)  # 指数退避
        
        return GenerationResult(
            success=False,
            error="达到最大重试次数",
            retry_count=self.config.max_retries
        )
    
    def prepare_generation_tasks(self, nutribench_data: pd.DataFrame, 
                               sample_size: int = 100) -> List[Dict[str, Any]]:
        """准备生成任务"""
        tasks = []
        
        # 随机采样
        sampled_data = nutribench_data.sample(n=min(sample_size, len(nutribench_data)))
        
        for _, row in sampled_data.iterrows():
            meal_data = row.to_dict()
            
            # 为每个餐食生成多种类型的对话任务
            for template_name in self.prompt_library.list_templates():
                for age_group in ["6-12m", "1-3y", "3-6y"]:
                    task = {
                        "meal_data": meal_data,
                        "template_name": template_name,
                        "age_group": age_group,
                        "task_id": f"{len(tasks):06d}"
                    }
                    tasks.append(task)
        
        # 随机打乱任务顺序
        random.shuffle(tasks)
        return tasks
    
    def generate_prompts(self, task: Dict[str, Any]) -> tuple[str, str]:
        """生成系统和用户提示"""
        template = self.prompt_library.get_template(task["template_name"])
        if not template:
            raise ValueError(f"未找到模板: {task['template_name']}")
        
        # 准备模板变量
        meal_data = task["meal_data"]
        age_group = task["age_group"]
        
        # 年龄组信息
        age_info = {
            "6-12m": {"name": "6-12个月", "stage": "辅食添加期"},
            "1-3y": {"name": "1-3岁", "stage": "幼儿期"},
            "3-6y": {"name": "3-6岁", "stage": "学龄前期"}
        }
        
        # 构建用户提示的变量
        template_vars = {
            "age_group": age_info[age_group]["name"],
            "development_stage": age_info[age_group]["stage"],
            "meal_description": meal_data.get("meal_description", ""),
            "carb": meal_data.get("carb", 0),
            "protein": meal_data.get("protein", 0),
            "fat": meal_data.get("fat", 0),
            "energy": meal_data.get("energy", 0),
            "country": meal_data.get("country", "Unknown"),
            "weight": round(random.uniform(8, 20), 1),
            "height": round(random.uniform(70, 120), 1),
            "special_conditions": "无特殊情况",
            "parent_concerns": "希望确保营养充足",
            "nutrition_info": f"能量{meal_data.get('energy', 0)}千卡，蛋白质{meal_data.get('protein', 0)}克"
        }
        
        try:
            user_prompt = template.user_template.format(**template_vars)
        except KeyError as e:
            # 如果模板变量不足，使用简化版本
            user_prompt = f"""我的宝宝{age_info[age_group]['name']}，今天吃了{meal_data.get('meal_description', '')}，请给我专业的营养指导。

营养成分：
- 碳水化合物：{meal_data.get('carb', 0)}克
- 蛋白质：{meal_data.get('protein', 0)}克
- 脂肪：{meal_data.get('fat', 0)}克
- 总能量：{meal_data.get('energy', 0)}千卡"""
        
        return template.system_prompt, user_prompt
    
    async def process_batch(self, tasks: List[Dict[str, Any]]) -> List[GenerationResult]:
        """批量处理任务"""
        results = []
        
        # 创建异步任务
        async_tasks = []
        for task in tasks:
            try:
                system_prompt, user_prompt = self.generate_prompts(task)
                async_task = self.call_llm_async(system_prompt, user_prompt)
                async_tasks.append((task, async_task))
            except Exception as e:
                logger.error(f"准备任务失败: {str(e)}")
                results.append(GenerationResult(success=False, error=str(e)))
        
        # 执行异步任务
        for task, async_task in async_tasks:
            result = await async_task
            
            if result.success:
                # 构建完整的对话数据
                conversation = {
                    "instruction": self.generate_prompts(task)[1],
                    "output": result.data["content"],
                    "metadata": {
                        "type": task["template_name"],
                        "age_group": task["age_group"],
                        "country": task["meal_data"].get("country", "Unknown"),
                        "generation_method": "llm_pipeline",
                        "model": self.config.model,
                        "tokens_used": result.data["tokens"],
                        "task_id": task["task_id"]
                    }
                }
                
                # 质量检查
                quality_result = self.prompt_library.validate_output(
                    task["template_name"], 
                    result.data["content"]
                )
                
                conversation["metadata"]["quality_score"] = quality_result["score"]
                conversation["metadata"]["quality_passed"] = quality_result["valid"]
                
                result.data = conversation
                result.quality_score = quality_result["score"]
                
                # 更新统计
                if quality_result["valid"]:
                    self.stats["quality_passed"] += 1
                else:
                    self.stats["quality_failed"] += 1
                
                self.stats["successful_generations"] += 1
            else:
                self.stats["failed_generations"] += 1
            
            results.append(result)
            self.stats["total_requests"] += 1
            
            # 添加延迟
            await asyncio.sleep(self.config.delay_between_requests)
        
        return results
    
    async def generate_dataset(self, nutribench_data: pd.DataFrame, 
                             sample_size: int = 100,
                             output_path: str = "llm_generated_dataset.json") -> Dict[str, Any]:
        """生成完整数据集"""
        logger.info(f"开始生成数据集，样本数: {sample_size}")
        
        # 准备任务
        tasks = self.prepare_generation_tasks(nutribench_data, sample_size)
        logger.info(f"准备了 {len(tasks)} 个生成任务")
        
        # 分批处理
        all_results = []
        successful_conversations = []
        
        for i in range(0, len(tasks), self.config.batch_size):
            batch = tasks[i:i + self.config.batch_size]
            logger.info(f"处理批次 {i//self.config.batch_size + 1}/{(len(tasks)-1)//self.config.batch_size + 1}")
            
            batch_results = await self.process_batch(batch)
            all_results.extend(batch_results)
            
            # 收集成功的对话
            for result in batch_results:
                if result.success and result.quality_score >= self.config.quality_threshold:
                    successful_conversations.append(result.data)
        
        # 保存数据集
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(successful_conversations, f, ensure_ascii=False, indent=2)
        
        # 生成报告
        report = self.generate_report()
        report_path = output_path.replace('.json', '_report.json')
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"数据集生成完成: {len(successful_conversations)} 条高质量对话")
        logger.info(f"数据集保存到: {output_path}")
        logger.info(f"报告保存到: {report_path}")
        
        return {
            "dataset_path": output_path,
            "report_path": report_path,
            "total_conversations": len(successful_conversations),
            "stats": self.stats
        }
    
    def generate_report(self) -> Dict[str, Any]:
        """生成详细报告"""
        total_requests = self.stats["total_requests"]
        success_rate = (self.stats["successful_generations"] / total_requests * 100) if total_requests > 0 else 0
        quality_rate = (self.stats["quality_passed"] / self.stats["successful_generations"] * 100) if self.stats["successful_generations"] > 0 else 0
        
        return {
            "generation_summary": {
                "total_requests": total_requests,
                "successful_generations": self.stats["successful_generations"],
                "failed_generations": self.stats["failed_generations"],
                "success_rate": f"{success_rate:.1f}%"
            },
            "quality_summary": {
                "quality_passed": self.stats["quality_passed"],
                "quality_failed": self.stats["quality_failed"],
                "quality_pass_rate": f"{quality_rate:.1f}%"
            },
            "resource_usage": {
                "total_tokens_used": self.stats["total_tokens_used"],
                "estimated_cost_usd": self.stats["total_tokens_used"] * 0.002 / 1000  # GPT-3.5估算
            },
            "configuration": asdict(self.config)
        }

async def main():
    """主函数示例"""
    # 配置
    config = GenerationConfig(
        batch_size=5,
        max_retries=2,
        delay_between_requests=0.5
    )
    
    print("=== LLM数据生成管道 ===")
    print("注意：需要配置OpenAI API密钥才能使用")
    
    # 示例数据
    sample_data = pd.DataFrame([
        {
            "meal_description": "小米粥配蒸蛋羹",
            "carb": 22.5,
            "protein": 6.8,
            "fat": 3.2,
            "energy": 145.0,
            "country": "CHN"
        }
    ])
    
    # 如果有API密钥，可以运行完整流程
    api_key = None  # 在这里设置您的API密钥
    
    if api_key:
        async with LLMDataPipeline(config, api_key) as pipeline:
            result = await pipeline.generate_dataset(sample_data, sample_size=1)
            print(f"生成结果: {result}")
    else:
        print("请设置API密钥以运行完整的生成流程")

if __name__ == "__main__":
    asyncio.run(main())
