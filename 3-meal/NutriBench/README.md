---
license: cc-by-nc-sa-4.0
dataset_info:
- config_name: v1
  features:
  - name: meal_description
    dtype: string
  - name: carb
    dtype: float64
  - name: protein
    dtype: float64
  - name: fat
    dtype: float64
  - name: energy
    dtype: float64
  splits:
  - name: wweia_meal_metric
    num_bytes: 851657
    num_examples: 5532
  - name: who_meal_natural
    num_bytes: 37246
    num_examples: 266
  - name: wweia_meal_natural
    num_bytes: 911941
    num_examples: 5532
  - name: who_meal_metric
    num_bytes: 73802
    num_examples: 527
  download_size: 920875
  dataset_size: 1874646
- config_name: v2
  features:
  - name: meal_description
    dtype: string
  - name: carb
    dtype: float64
  - name: fat
    dtype: float64
  - name: energy
    dtype: float64
  - name: protein
    dtype: float64
  - name: country
    dtype: string
  - name: serving_type
    dtype: string
  splits:
  - name: train
    num_bytes: 2719739
    num_examples: 15617
  download_size: 1160801
  dataset_size: 2719739
configs:
- config_name: v1
  data_files:
  - split: wweia_meal_metric
    path: v1/wweia_meal_metric-*
  - split: who_meal_natural
    path: v1/who_meal_natural-*
  - split: wweia_meal_natural
    path: v1/wweia_meal_natural-*
  - split: who_meal_metric
    path: v1/who_meal_metric-*
- config_name: v2
  default: true
  data_files:
  - split: train
    path: v2/train-*
---

# **NutriBench**

*A Dataset for Evaluating Large Language Models on Nutrition Estimation from Meal Descriptions*

[🌐 Project Page](https://mehak126.github.io/nutribench.html) | [📝 Paper (ICLR 2025)](https://arxiv.org/abs/2407.12843) | [📊 Dataset](https://huggingface.co/datasets/dongx1997/NutriBench) | [🔗 Github](https://github.com/DongXzz/NutriBench)

---

## News

- [2025/04/08] **NutriBench v2** is released! Now supports **24 countries** with improved **diversity** in meal descriptions.

- [2025/03/16] We’ve launched LLM-Based Carb Estimation via Text Message!  
  - For US phone numbers, text your meal description to **+******************.  
  - For WhatsApp, send a message to **+******************.
  
- [2025/02/11] 🎉 Our **NutriBench** paper has been **accepted at ICLR 2025**!

- [2024/10/16] Released **NutriBench v1**, the **First** benchmark for evaluating nutrition estimation from meal descriptions.  

---

## Dataset

| Version | #Samples | #Countries |
|--------|----------|------------|
| v1     | 11,857   | 11         |
| v2     | 15,617   | 24         |

> **Note:** To reproduce the results in our paper, please use **NutriBench v1**.

Each sample in Nutribench v2 includes:

```json
{
  "meal_description": "I'm enjoying a chocolate-coated ice cream bar, vanilla flavor, as a quick snack.",
  "carb": 18.38,
  "fat": 18.08,
  "energy": 248.25,
  "protein": 3.08,
  "country": "USA"
}
```
The dataset contains the following columns:
- `meal_description`: A natural language description of the meal.
- `carb`: The carbohydrate content of the meal (in grams).
- `fat`: The fat content of the meal (in grams).
- `energy`: The energy content of the meal (in kilocalories).
- `protein`: The protein content of the meal (in grams).
- `country`: The country associated with the meal. Country information follows the [ISO 3166-1 alpha-3](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-3) standard. Please refer to the linked resource for full country names.
- `serving_type`: Indicates whether the measurements are in metric serving (`metric`) or natural serving (`natural`).

All the meal descriptions in NutriBench are sourced from real-world data 
- [What We Eat in America (WWEIA)](https://www.ars.usda.gov/northeast-area/beltsville-md-bhnrc/beltsville-human-nutrition-research-center/food-surveys-research-group/docs/wweianhanes-overview/#)
- [FAO/WHO Gift](https://www.fao.org/gift-individual-food-consumption/data/en)

---


## Inference/Evaluation
Please refer to our [🔗 Github](https://github.com/DongXzz/NutriBench)

---

## Acknowledgements
Laya Pullela and Sophia Mirrashidi processed and manually verified these data to compile NutriBench v2.

---

## 📜 Citation

If you find **NutriBench** helpful, please consider citing:

```bibtex
@article{hua2024nutribench,
  title={NutriBench: A Dataset for Evaluating Large Language Models on Nutrition Estimation from Meal Descriptions},
  author={Hua, Andong and Dhaliwal, Mehak Preet and Burke, Ryan and Pullela, Laya and Qin, Yao},
  journal={arXiv preprint arXiv:2407.12843},
  year={2024}
}
```

---