#!/usr/bin/env python3
"""
基于LLM的NutriBench数据集构建器
使用大语言模型生成高质量的营养指导对话数据
"""

import json
import pandas as pd
import random
import time
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import openai
from pathlib import Path

@dataclass
class ConversationConfig:
    """对话生成配置"""
    age_group: str
    conversation_type: str
    meal_data: Dict[str, Any]
    context: Optional[Dict[str, Any]] = None

class LLMDatasetBuilder:
    def __init__(self, api_key: str = None, model: str = "gpt-3.5-turbo"):
        """初始化LLM数据集构建器"""
        self.model = model
        if api_key:
            openai.api_key = api_key
        
        # 加载营养指南
        self.guidelines = self._load_guidelines()
        
        # 对话类型配置
        self.conversation_types = {
            "nutrition_analysis": "营养成分分析",
            "diversity_monitoring": "饮食多样性监测", 
            "health_guidance": "健康指导建议",
            "feeding_advice": "喂养方法指导",
            "safety_warning": "食品安全提醒",
            "growth_monitoring": "生长发育监测"
        }
        
        # 年龄组配置
        self.age_groups = {
            "6-12m": {"name": "6-12个月", "stage": "辅食添加期"},
            "1-3y": {"name": "1-3岁", "stage": "幼儿期"},
            "3-6y": {"name": "3-6岁", "stage": "学龄前期"}
        }
    
    def _load_guidelines(self) -> Dict[str, Any]:
        """加载营养指南"""
        try:
            with open('nutrition_guidelines.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print("警告: 未找到营养指南文件，使用默认配置")
            return {}
    
    def generate_system_prompt(self, config: ConversationConfig) -> str:
        """生成系统提示"""
        age_info = self.age_groups.get(config.age_group, {})
        conv_type = self.conversation_types.get(config.conversation_type, "营养指导")
        
        system_prompt = f"""你是一位专业的儿童营养师，专门为{age_info.get('name', '婴幼儿')}({age_info.get('stage', '发育期')})提供营养指导。

专业背景：
- 熟悉中国居民膳食指南(2022)
- 了解国家卫健委婴幼儿喂养指南
- 掌握各年龄段营养需求标准
- 具备食品安全和过敏预防知识

任务要求：
1. 提供科学、准确的营养建议
2. 考虑{age_info.get('name', '婴幼儿')}的发育特点
3. 给出实用、可操作的指导
4. 包含必要的安全提醒
5. 语言亲切、专业，易于家长理解

对话类型：{conv_type}

注意事项：
- 不替代医疗诊断，严重问题建议就医
- 考虑个体差异，给出个性化建议
- 强调食品安全和卫生
- 关注营养均衡和多样性"""

        return system_prompt
    
    def generate_user_prompt(self, config: ConversationConfig) -> str:
        """生成用户提示"""
        meal_desc = config.meal_data.get('meal_description', '')
        carb = config.meal_data.get('carb', 0)
        protein = config.meal_data.get('protein', 0)
        fat = config.meal_data.get('fat', 0)
        energy = config.meal_data.get('energy', 0)
        
        # 根据对话类型生成不同的用户问题
        if config.conversation_type == "nutrition_analysis":
            user_prompt = f"""请帮我分析一下这个餐食的营养价值：

餐食描述：{meal_desc}
营养成分：
- 碳水化合物：{carb}克
- 蛋白质：{protein}克  
- 脂肪：{fat}克
- 总能量：{energy}千卡

我的宝宝{self.age_groups[config.age_group]['name']}，这个餐食适合吗？营养够吗？"""

        elif config.conversation_type == "diversity_monitoring":
            user_prompt = f"""我想了解这个餐食对宝宝饮食多样性的贡献：

餐食：{meal_desc}
宝宝年龄：{self.age_groups[config.age_group]['name']}

这个餐食在营养多样性方面怎么样？还需要搭配什么食物？"""

        elif config.conversation_type == "health_guidance":
            weight = round(random.uniform(8, 20), 1)  # 随机生成合理体重
            user_prompt = f"""宝宝{self.age_groups[config.age_group]['name']}，体重{weight}kg，今天吃了：

{meal_desc}

请帮我评估一下营养是否充足，还需要补充什么？"""

        elif config.conversation_type == "feeding_advice":
            user_prompt = f"""我的宝宝{self.age_groups[config.age_group]['name']}，想给他/她准备这样的餐食：

{meal_desc}

请问应该怎么制作和喂养？有什么注意事项？"""

        elif config.conversation_type == "safety_warning":
            user_prompt = f"""宝宝{self.age_groups[config.age_group]['name']}，我准备给他/她吃：

{meal_desc}

这个食物安全吗？有什么需要注意的地方？"""

        else:  # growth_monitoring
            user_prompt = f"""我的宝宝{self.age_groups[config.age_group]['name']}，最近在吃：

{meal_desc}

这样的饮食对宝宝的生长发育有帮助吗？"""

        return user_prompt
    
    def call_llm(self, system_prompt: str, user_prompt: str) -> str:
        """调用LLM生成回答"""
        try:
            response = openai.ChatCompletion.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                temperature=0.7,
                max_tokens=800,
                top_p=0.9
            )
            return response.choices[0].message.content.strip()
        except Exception as e:
            print(f"LLM调用失败: {e}")
            return self._generate_fallback_response(user_prompt)
    
    def _generate_fallback_response(self, user_prompt: str) -> str:
        """生成备用回答（当LLM调用失败时）"""
        return """感谢您的咨询。作为专业的营养师，我建议：

1. 确保食物新鲜、卫生
2. 注意营养均衡，多样化饮食
3. 根据宝宝年龄选择合适的食物质地
4. 观察宝宝的反应，及时调整

如有特殊情况，建议咨询儿科医生。"""
    
    def generate_conversation(self, config: ConversationConfig) -> Dict[str, Any]:
        """生成单个对话"""
        system_prompt = self.generate_system_prompt(config)
        user_prompt = self.generate_user_prompt(config)
        
        # 调用LLM生成回答
        assistant_response = self.call_llm(system_prompt, user_prompt)
        
        # 构建对话数据
        conversation = {
            "instruction": user_prompt,
            "output": assistant_response,
            "metadata": {
                "type": config.conversation_type,
                "age_group": config.age_group,
                "country": config.meal_data.get('country', 'Unknown'),
                "generation_method": "llm_assisted",
                "model": self.model,
                "meal_energy": config.meal_data.get('energy', 0)
            }
        }
        
        return conversation
    
    def batch_generate(self, nutribench_data: pd.DataFrame, 
                      sample_size: int = 100,
                      conversations_per_meal: int = 2) -> List[Dict[str, Any]]:
        """批量生成对话数据"""
        dataset = []
        
        # 随机采样餐食数据
        sampled_data = nutribench_data.sample(n=min(sample_size, len(nutribench_data)))
        
        print(f"开始生成 {len(sampled_data)} 个餐食的对话数据...")
        
        for idx, (_, row) in enumerate(sampled_data.iterrows()):
            meal_data = row.to_dict()
            
            print(f"处理餐食 {idx+1}/{len(sampled_data)}: {meal_data['meal_description'][:30]}...")
            
            # 为每个餐食生成多个对话
            for _ in range(conversations_per_meal):
                # 随机选择年龄组和对话类型
                age_group = random.choice(list(self.age_groups.keys()))
                conv_type = random.choice(list(self.conversation_types.keys()))
                
                config = ConversationConfig(
                    age_group=age_group,
                    conversation_type=conv_type,
                    meal_data=meal_data
                )
                
                try:
                    conversation = self.generate_conversation(config)
                    dataset.append(conversation)
                    
                    # 添加延迟避免API限制
                    time.sleep(0.5)
                    
                except Exception as e:
                    print(f"生成对话失败: {e}")
                    continue
        
        print(f"成功生成 {len(dataset)} 条对话数据")
        return dataset
    
    def enhance_existing_dataset(self, existing_dataset_path: str) -> List[Dict[str, Any]]:
        """增强现有数据集"""
        with open(existing_dataset_path, 'r', encoding='utf-8') as f:
            existing_data = json.load(f)
        
        enhanced_dataset = []
        
        print(f"开始增强现有数据集 ({len(existing_data)} 条)...")
        
        for idx, item in enumerate(existing_data):
            # 保留原始数据
            enhanced_dataset.append(item)
            
            # 生成变体对话
            if idx % 3 == 0:  # 每3条生成1个变体
                try:
                    # 从原始指令中提取信息
                    original_instruction = item['instruction']
                    
                    # 使用LLM生成变体
                    variant_prompt = f"""基于以下营养咨询问题，生成一个类似但表达方式不同的问题：

原问题：{original_instruction}

要求：
1. 保持核心意思不变
2. 改变表达方式和用词
3. 可以调整问题角度
4. 保持自然、口语化的风格"""

                    system_prompt = "你是一个专业的文本改写助手，擅长生成自然、多样化的表达方式。"
                    
                    variant_instruction = self.call_llm(system_prompt, variant_prompt)
                    
                    # 创建变体对话
                    variant_conversation = {
                        "instruction": variant_instruction,
                        "output": item['output'],  # 使用相同的回答
                        "metadata": {
                            **item['metadata'],
                            "generation_method": "llm_variant",
                            "original_id": idx
                        }
                    }
                    
                    enhanced_dataset.append(variant_conversation)
                    time.sleep(0.3)
                    
                except Exception as e:
                    print(f"生成变体失败: {e}")
                    continue
        
        print(f"数据集增强完成，从 {len(existing_data)} 条增加到 {len(enhanced_dataset)} 条")
        return enhanced_dataset

def load_nutribench_data(data_path: str = "NutriBench") -> pd.DataFrame:
    """加载NutriBench数据集"""
    print("🔄 加载NutriBench数据集...")

    try:
        # 尝试加载v2版本（推荐）
        v2_path = f"{data_path}/v2/train-00000-of-00001.parquet"
        if os.path.exists(v2_path):
            df = pd.read_parquet(v2_path)
            print(f"✅ 成功加载v2数据: {len(df)} 条记录")
            print(f"📊 覆盖国家: {df['country'].nunique()} 个")
            return df
    except Exception as e:
        print(f"❌ v2数据加载失败: {e}")

    # 使用示例数据
    print("⚠️ 使用示例数据...")
    return pd.DataFrame([
        {
            "meal_description": "For breakfast, I ate a plain bun weighing 126 grams and sprinkled on 27 grams of raw sugar.",
            "carb": 90.8,
            "protein": 9.6,
            "fat": 4.2,
            "energy": 439.0,
            "country": "ZMB"
        },
        {
            "meal_description": "For lunch, I had 171 grams of boiled fresh groundnuts in their shells and 230 grams of boiled orange sweet potato without skin.",
            "carb": 97.8,
            "protein": 27.9,
            "fat": 37.8,
            "energy": 806.0,
            "country": "ZMB"
        }
    ])

def main():
    """主函数示例"""
    print("🤖 LLM辅助数据集构建器")
    print("注意：需要配置OpenAI API密钥才能使用LLM功能")

    # 加载NutriBench数据
    nutribench_data = load_nutribench_data()

    # 显示数据统计
    print(f"\n📊 数据统计:")
    print(f"  总样本数: {len(nutribench_data)}")
    if 'country' in nutribench_data.columns:
        print(f"  国家数量: {nutribench_data['country'].nunique()}")
        country_counts = nutribench_data['country'].value_counts().head(5)
        print(f"  主要国家: {dict(country_counts)}")

    # 显示营养成分范围
    if all(col in nutribench_data.columns for col in ['carb', 'protein', 'fat', 'energy']):
        print(f"\n🥗 营养成分范围:")
        for nutrient in ['carb', 'protein', 'fat', 'energy']:
            values = nutribench_data[nutrient]
            print(f"  {nutrient}: {values.min():.1f} - {values.max():.1f} (平均: {values.mean():.1f})")

    # 示例：如果有API密钥，可以运行LLM增强
    api_key = 'sk-5eba46fbcff649d5bf28313bc865de10'  # 在这里设置您的API密钥

    if api_key:
        print(f"\n🚀 开始LLM增强数据生成...")
        builder = LLMDatasetBuilder(api_key=api_key)

        # 使用小样本测试
        sample_data = nutribench_data.head(5)
        dataset = builder.batch_generate(sample_data, conversations_per_meal=2)

        # 保存结果
        output_file = 'llm_enhanced_nutribench_dataset.json'
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, ensure_ascii=False, indent=2)

        print(f"✅ 生成了 {len(dataset)} 条LLM增强对话")
        print(f"💾 保存到: {output_file}")
    else:
        print(f"\n💡 要使用LLM增强功能，请:")
        print(f"  1. 设置OpenAI API密钥")
        print(f"  2. 取消注释相关代码")
        print(f"  3. 运行: builder.batch_generate(nutribench_data, conversations_per_meal=3)")

    # 显示数据样本
    print(f"\n📖 数据样本:")
    for i in range(min(3, len(nutribench_data))):
        row = nutribench_data.iloc[i]
        print(f"  {i+1}. {row['meal_description'][:60]}...")
        print(f"     营养: 碳水{row['carb']}g, 蛋白质{row['protein']}g, 脂肪{row['fat']}g, 能量{row['energy']}kcal")

if __name__ == "__main__":
    main()
