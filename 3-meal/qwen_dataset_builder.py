#!/usr/bin/env python3
"""
基于Qwen LLM的0-3岁婴幼儿营养数据集构建器
专门针对0-3岁年龄段，使用通义千问API生成高质量营养指导对话
"""

import json
import pandas as pd
import random
import time
import os
import requests
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class QwenConfig:
    """Qwen配置"""
    api_key: str
    model: str = "qwen-plus"  # 或 qwen-turbo, qwen-max
    base_url: str = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    temperature: float = 0.7
    max_tokens: int = 800

class QwenDatasetBuilder:
    """基于Qwen的0-3岁婴幼儿营养数据集构建器"""
    
    def __init__(self, config: QwenConfig):
        self.config = config
        self.guidelines = self._load_guidelines()
        
        # 0-3岁年龄组配置
        self.age_groups = {
            "0-6m": {
                "name": "0-6个月",
                "stage": "纯母乳喂养期",
                "energy_need": 550,
                "main_nutrition": ["母乳", "配方奶", "维生素D"]
            },
            "6-12m": {
                "name": "6-12个月", 
                "stage": "辅食添加期",
                "energy_need": 750,
                "main_nutrition": ["母乳", "辅食", "铁", "锌"]
            },
            "12-24m": {
                "name": "12-24个月",
                "stage": "幼儿期过渡",
                "energy_need": 1000,
                "main_nutrition": ["多样化食物", "钙", "优质蛋白"]
            },
            "24-36m": {
                "name": "24-36个月",
                "stage": "幼儿期",
                "energy_need": 1200,
                "main_nutrition": ["均衡膳食", "全面营养"]
            }
        }
        
        # 对话类型
        self.conversation_types = {
            "nutrition_analysis": "营养成分分析",
            "feeding_guidance": "喂养指导",
            "safety_advice": "安全建议",
            "development_support": "发育支持",
            "problem_solving": "喂养问题解决"
        }
    
    def _load_guidelines(self) -> Dict[str, Any]:
        """加载0-3岁喂养指南"""
        try:
            with open('infant_feeding_guidelines.json', 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print("警告: 未找到婴幼儿喂养指南文件")
            return {}
    
    def call_qwen_api(self, system_prompt: str, user_prompt: str) -> Optional[str]:
        """调用通义千问API"""
        headers = {
            "Authorization": f"Bearer {self.config.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": self.config.model,
            "input": {
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
            },
            "parameters": {
                "temperature": self.config.temperature,
                "max_tokens": self.config.max_tokens,
                "top_p": 0.9
            }
        }
        
        try:
            response = requests.post(
                self.config.base_url,
                headers=headers,
                json=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if "output" in result and "text" in result["output"]:
                    return result["output"]["text"]
                else:
                    print(f"API响应格式异常: {result}")
                    return None
            else:
                print(f"API调用失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"API调用异常: {str(e)}")
            return None
    
    def generate_system_prompt(self, age_group: str, conversation_type: str) -> str:
        """生成系统提示"""
        age_info = self.age_groups.get(age_group, {})
        conv_type_name = self.conversation_types.get(conversation_type, "营养指导")
        
        # 获取年龄段特定指南
        guidelines_key = age_group.replace('m', 'm').replace('y', 'm')  # 统一格式
        age_guidelines = self.guidelines.get("0_3_years_feeding_guidelines", {}).get(age_group, {})
        
        system_prompt = f"""你是一位专业的婴幼儿营养师和儿科医生，专门为{age_info.get('name', '婴幼儿')}({age_info.get('stage', '发育期')})提供营养指导。

专业背景：
- 儿科营养学博士，15年临床经验
- 熟悉《3岁以下婴幼儿健康养育照护指南》
- 掌握中国居民膳食指南(2022)婴幼儿部分
- 了解WHO婴幼儿喂养建议和最新研究

年龄段特点({age_info.get('name', '婴幼儿')}):
- 发育阶段: {age_info.get('stage', '快速生长期')}
- 每日能量需求: 约{age_info.get('energy_need', 800)}千卡
- 营养重点: {', '.join(age_info.get('main_nutrition', ['均衡营养']))}

任务要求：
1. 提供科学、准确的{conv_type_name}建议
2. 严格遵循年龄段喂养原则和安全要求
3. 给出实用、可操作的具体指导
4. 包含必要的安全提醒和注意事项
5. 语言温和、专业，易于家长理解

重要原则：
- 0-6个月: 纯母乳喂养，不添加任何辅食
- 6个月起: 及时添加辅食，继续母乳喂养
- 循序渐进: 从泥状到块状，从单一到多样
- 安全第一: 预防窒息、过敏等风险
- 不替代医疗诊断，严重问题建议就医"""

        return system_prompt
    
    def generate_user_prompt(self, meal_data: Dict, age_group: str, conversation_type: str) -> str:
        """生成用户提示"""
        age_name = self.age_groups[age_group]['name']
        meal_desc = meal_data.get('meal_description', '')
        
        # 根据年龄段和对话类型生成不同的问题
        if age_group == "0-6m":
            if conversation_type == "nutrition_analysis":
                return f"我的宝宝{age_name}，我在考虑给他/她吃{meal_desc}，这样合适吗？需要注意什么？"
            elif conversation_type == "feeding_guidance":
                return f"我的{age_name}宝宝，关于喂养方面我想了解一些指导，特别是关于{meal_desc}这类食物。"
            else:
                return f"我的宝宝{age_name}，请给我一些专业的喂养建议。"
        
        elif age_group == "6-12m":
            if conversation_type == "nutrition_analysis":
                return f"我的宝宝{age_name}，今天吃了{meal_desc}，请帮我分析一下营养价值和适宜性。"
            elif conversation_type == "feeding_guidance":
                return f"我的{age_name}宝宝刚开始添加辅食，{meal_desc}这样的食物应该怎么准备和喂养？"
            elif conversation_type == "safety_advice":
                return f"给{age_name}的宝宝准备{meal_desc}时，有什么安全注意事项？"
            else:
                return f"关于{age_name}宝宝的辅食添加，{meal_desc}这类食物有什么建议？"
        
        else:  # 12-24m, 24-36m
            if conversation_type == "nutrition_analysis":
                return f"我的宝宝{age_name}，今天的餐食是{meal_desc}，请评估一下营养是否充足。"
            elif conversation_type == "feeding_guidance":
                return f"我的{age_name}宝宝，关于{meal_desc}这样的食物，应该如何安排在日常饮食中？"
            elif conversation_type == "problem_solving":
                return f"我的{age_name}宝宝对{meal_desc}这类食物有些抗拒，该怎么办？"
            else:
                return f"请给我一些关于{age_name}宝宝营养搭配的建议，特别是{meal_desc}这类食物。"
    
    def generate_conversation(self, meal_data: Dict, age_group: str, conversation_type: str) -> Optional[Dict[str, Any]]:
        """生成单个对话"""
        system_prompt = self.generate_system_prompt(age_group, conversation_type)
        user_prompt = self.generate_user_prompt(meal_data, age_group, conversation_type)
        
        # 调用Qwen API
        assistant_response = self.call_qwen_api(system_prompt, user_prompt)
        
        if assistant_response is None:
            return None
        
        # 构建对话数据
        conversation = {
            "instruction": user_prompt,
            "output": assistant_response,
            "metadata": {
                "type": conversation_type,
                "age_group": age_group,
                "country": meal_data.get("country", "Unknown"),
                "generation_method": "qwen_api",
                "model": self.config.model,
                "meal_energy": meal_data.get("energy", 0),
                "stage": self.age_groups[age_group]["stage"]
            }
        }
        
        return conversation
    
    def batch_generate(self, nutribench_data: pd.DataFrame, 
                      sample_size: int = 100,
                      conversations_per_meal: int = 2) -> List[Dict[str, Any]]:
        """批量生成对话数据"""
        print(f"🚀 开始使用Qwen生成0-3岁婴幼儿营养数据集...")
        print(f"📊 目标: {sample_size}个餐食 × {conversations_per_meal}个对话 = {sample_size * conversations_per_meal}条数据")
        
        # 随机采样
        sampled_data = nutribench_data.sample(n=min(sample_size, len(nutribench_data)))
        
        all_conversations = []
        total_requests = 0
        successful_requests = 0
        
        for idx, (_, meal_row) in enumerate(sampled_data.iterrows()):
            meal_data = meal_row.to_dict()
            print(f"\n📝 处理餐食 {idx+1}/{len(sampled_data)}: {meal_data['meal_description'][:50]}...")
            
            for _ in range(conversations_per_meal):
                # 随机选择年龄组和对话类型
                age_group = random.choice(list(self.age_groups.keys()))
                conv_type = random.choice(list(self.conversation_types.keys()))
                
                try:
                    conversation = self.generate_conversation(meal_data, age_group, conv_type)
                    total_requests += 1
                    
                    if conversation:
                        all_conversations.append(conversation)
                        successful_requests += 1
                        print(f"  ✅ 生成 {conv_type} ({age_group}) - 成功率: {successful_requests/total_requests*100:.1f}%")
                    else:
                        print(f"  ❌ 生成失败: {conv_type} ({age_group})")
                    
                    # 添加延迟避免API限制
                    time.sleep(1.0)
                    
                except Exception as e:
                    print(f"  ❌ 生成异常: {str(e)}")
                    continue
        
        print(f"\n🎉 生成完成！")
        print(f"📊 总请求: {total_requests}, 成功: {successful_requests}, 成功率: {successful_requests/total_requests*100:.1f}%")
        return all_conversations
    
    def save_dataset(self, conversations: List[Dict[str, Any]], 
                    filename: str = "qwen_0_3_years_nutrition_dataset.json"):
        """保存数据集"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(conversations, f, ensure_ascii=False, indent=2)
        print(f"💾 数据集已保存到: {filename}")
        
        # 生成统计报告
        self._generate_statistics(conversations, filename.replace('.json', '_stats.json'))
    
    def _generate_statistics(self, conversations: List[Dict[str, Any]], stats_filename: str):
        """生成统计报告"""
        if not conversations:
            return
        
        # 统计分析
        type_counts = {}
        age_counts = {}
        stage_counts = {}
        
        for conv in conversations:
            metadata = conv['metadata']
            
            # 统计类型
            conv_type = metadata['type']
            type_counts[conv_type] = type_counts.get(conv_type, 0) + 1
            
            # 统计年龄组
            age_group = metadata['age_group']
            age_counts[age_group] = age_counts.get(age_group, 0) + 1
            
            # 统计发育阶段
            stage = metadata['stage']
            stage_counts[stage] = stage_counts.get(stage, 0) + 1
        
        stats = {
            "total_conversations": len(conversations),
            "type_distribution": type_counts,
            "age_distribution": age_counts,
            "stage_distribution": stage_counts,
            "generation_info": {
                "model": self.config.model,
                "generation_method": "qwen_api",
                "target_age_range": "0-3岁"
            }
        }
        
        with open(stats_filename, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"📈 统计报告已保存到: {stats_filename}")

def load_nutribench_data(data_path: str = "NutriBench") -> pd.DataFrame:
    """加载NutriBench数据集"""
    print("🔄 加载NutriBench数据集...")
    
    try:
        v2_path = f"{data_path}/v2/train-00000-of-00001.parquet"
        if os.path.exists(v2_path):
            df = pd.read_parquet(v2_path)
            print(f"✅ 成功加载v2数据: {len(df)} 条记录")
            return df
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
    
    return pd.DataFrame()

def main():
    """主函数"""
    print("=" * 60)
    print("🤖 基于Qwen的0-3岁婴幼儿营养数据集构建器")
    print("=" * 60)
    
    # 配置Qwen API
    api_key = input("请输入您的通义千问API密钥 (或按Enter跳过): ").strip()
    
    if not api_key:
        print("⚠️ 未提供API密钥，将使用模拟模式演示")
        print("💡 要使用真实API，请:")
        print("  1. 在阿里云控制台获取API密钥")
        print("  2. 重新运行程序并输入密钥")
        return
    
    # 创建配置
    config = QwenConfig(
        api_key=api_key,
        model="qwen-plus",  # 可选: qwen-turbo, qwen-max
        temperature=0.7
    )
    
    # 创建构建器
    builder = QwenDatasetBuilder(config)
    
    # 加载数据
    nutribench_data = load_nutribench_data()
    if nutribench_data.empty:
        print("❌ 无法加载NutriBench数据，程序退出")
        return
    
    # 生成数据集
    print(f"\n🎯 开始生成0-3岁专用营养数据集...")
    conversations = builder.batch_generate(
        nutribench_data,
        sample_size=20,  # 从小样本开始测试
        conversations_per_meal=2
    )
    
    if conversations:
        # 保存数据集
        builder.save_dataset(conversations)
        
        # 显示样本
        print(f"\n📖 生成的对话样本:")
        for i, conv in enumerate(conversations[:2]):
            print(f"\n--- 样本 {i+1} ---")
            print(f"年龄组: {conv['metadata']['age_group']} ({conv['metadata']['stage']})")
            print(f"类型: {conv['metadata']['type']}")
            print(f"问题: {conv['instruction'][:100]}...")
            print(f"回答: {conv['output'][:200]}...")
    else:
        print("❌ 未能生成任何对话数据")
    
    print("\n" + "=" * 60)

if __name__ == "__main__":
    main()
