#!/usr/bin/env python3
"""
智能数据生成策略
基于数据质量和多样性的高效采样方法，从15,617条数据中智能选择最有价值的数据进行生成
"""

import pandas as pd
import numpy as np
import json
import random
from typing import Dict, List, Any, Tuple
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

class SmartDataGenerationStrategy:
    """智能数据生成策略"""
    
    def __init__(self, nutribench_data: pd.DataFrame):
        self.data = nutribench_data
        self.selected_samples = []
        
        # 定义营养价值等级
        self.nutrition_categories = {
            "high_protein": {"protein": (20, float('inf'))},
            "balanced": {"protein": (10, 20), "carb": (30, 60), "fat": (5, 15)},
            "high_energy": {"energy": (400, float('inf'))},
            "low_energy": {"energy": (0, 200)},
            "high_carb": {"carb": (60, float('inf'))},
            "high_fat": {"fat": (20, float('inf'))},
            "moderate": {"energy": (200, 400), "protein": (5, 15)}
        }
        
        # 年龄段对应的理想营养特征
        self.age_nutrition_preferences = {
            "6m_start": ["low_energy", "moderate", "balanced"],
            "6-9m": ["moderate", "balanced", "high_protein"],
            "9-12m": ["balanced", "high_protein", "moderate"],
            "12-24m": ["balanced", "high_protein", "high_energy"]
        }
    
    def analyze_data_distribution(self) -> Dict[str, Any]:
        """分析数据分布特征"""
        print("📊 分析NutriBench数据分布...")
        
        analysis = {
            "total_samples": len(self.data),
            "countries": self.data['country'].nunique(),
            "country_distribution": self.data['country'].value_counts().to_dict(),
            "nutrition_stats": {
                "energy": {
                    "mean": self.data['energy'].mean(),
                    "std": self.data['energy'].std(),
                    "min": self.data['energy'].min(),
                    "max": self.data['energy'].max(),
                    "quartiles": self.data['energy'].quantile([0.25, 0.5, 0.75]).to_dict()
                },
                "protein": {
                    "mean": self.data['protein'].mean(),
                    "std": self.data['protein'].std(),
                    "quartiles": self.data['protein'].quantile([0.25, 0.5, 0.75]).to_dict()
                }
            }
        }
        
        print(f"✅ 总数据量: {analysis['total_samples']:,} 条")
        print(f"✅ 覆盖国家: {analysis['countries']} 个")
        print(f"✅ 能量范围: {analysis['nutrition_stats']['energy']['min']:.1f} - {analysis['nutrition_stats']['energy']['max']:.1f} kcal")
        
        return analysis
    
    def categorize_by_nutrition(self) -> Dict[str, pd.DataFrame]:
        """按营养特征分类数据"""
        print("\n🔍 按营养特征分类数据...")
        
        categorized_data = {}
        
        for category, criteria in self.nutrition_categories.items():
            mask = pd.Series([True] * len(self.data))
            
            for nutrient, (min_val, max_val) in criteria.items():
                if max_val == float('inf'):
                    mask &= (self.data[nutrient] >= min_val)
                else:
                    mask &= (self.data[nutrient] >= min_val) & (self.data[nutrient] <= max_val)
            
            categorized_data[category] = self.data[mask].copy()
            print(f"  {category}: {len(categorized_data[category])} 条数据")
        
        return categorized_data
    
    def cluster_based_sampling(self, n_clusters: int = 20, samples_per_cluster: int = 5) -> pd.DataFrame:
        """基于聚类的智能采样"""
        print(f"\n🎯 基于聚类的智能采样 (K={n_clusters}, 每簇{samples_per_cluster}样本)...")
        
        # 准备特征数据
        features = ['energy', 'protein', 'carb', 'fat']
        X = self.data[features].fillna(0)
        
        # 标准化
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # K-means聚类
        kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        clusters = kmeans.fit_predict(X_scaled)
        
        # 从每个簇中采样
        selected_samples = []
        
        for cluster_id in range(n_clusters):
            cluster_mask = clusters == cluster_id
            cluster_data = self.data[cluster_mask]
            
            if len(cluster_data) > 0:
                # 从每个簇中随机采样
                n_samples = min(samples_per_cluster, len(cluster_data))
                sampled = cluster_data.sample(n=n_samples, random_state=42)
                selected_samples.append(sampled)
                
                print(f"  簇 {cluster_id}: {len(cluster_data)} 条数据 → 采样 {n_samples} 条")
        
        result = pd.concat(selected_samples, ignore_index=True)
        print(f"✅ 聚类采样完成: {len(result)} 条数据")
        
        return result
    
    def diversity_based_sampling(self, target_size: int = 200) -> pd.DataFrame:
        """基于多样性的采样策略"""
        print(f"\n🌈 基于多样性的采样策略 (目标: {target_size} 条)...")
        
        selected_samples = []
        
        # 1. 按国家分层采样
        country_samples = {}
        countries = self.data['country'].unique()
        samples_per_country = max(1, target_size // len(countries))
        
        for country in countries:
            country_data = self.data[self.data['country'] == country]
            n_samples = min(samples_per_country, len(country_data))
            sampled = country_data.sample(n=n_samples, random_state=42)
            country_samples[country] = sampled
            selected_samples.append(sampled)
            print(f"  {country}: {len(country_data)} 条 → 采样 {n_samples} 条")
        
        # 2. 补充营养特征多样性
        current_total = sum(len(df) for df in selected_samples)
        remaining = target_size - current_total
        
        if remaining > 0:
            print(f"  补充营养多样性采样: {remaining} 条")
            
            # 按营养特征分类采样
            categorized_data = self.categorize_by_nutrition()
            samples_per_category = max(1, remaining // len(categorized_data))
            
            for category, data in categorized_data.items():
                if len(data) > 0 and remaining > 0:
                    n_samples = min(samples_per_category, len(data), remaining)
                    # 避免重复采样
                    existing_indices = set()
                    for df in selected_samples:
                        existing_indices.update(df.index)
                    
                    available_data = data[~data.index.isin(existing_indices)]
                    if len(available_data) > 0:
                        sampled = available_data.sample(n=min(n_samples, len(available_data)), random_state=42)
                        selected_samples.append(sampled)
                        remaining -= len(sampled)
        
        result = pd.concat(selected_samples, ignore_index=True).drop_duplicates()
        print(f"✅ 多样性采样完成: {len(result)} 条数据")
        
        return result
    
    def quality_based_filtering(self, data: pd.DataFrame) -> pd.DataFrame:
        """基于质量的数据过滤"""
        print(f"\n🔍 基于质量的数据过滤...")
        
        original_size = len(data)
        
        # 1. 移除异常值
        # 能量异常值 (< 10 或 > 3000 kcal)
        data = data[(data['energy'] >= 10) & (data['energy'] <= 3000)]
        
        # 蛋白质异常值 (< 0 或 > 100g)
        data = data[(data['protein'] >= 0) & (data['protein'] <= 100)]
        
        # 2. 移除营养成分全为0的数据
        nutrition_cols = ['energy', 'protein', 'carb', 'fat']
        data = data[data[nutrition_cols].sum(axis=1) > 0]
        
        # 3. 移除描述过短的数据
        data = data[data['meal_description'].str.len() >= 20]
        
        # 4. 优先选择营养均衡的数据
        data['nutrition_score'] = self._calculate_nutrition_score(data)
        data = data.sort_values('nutrition_score', ascending=False)
        
        filtered_size = len(data)
        print(f"✅ 质量过滤完成: {original_size} → {filtered_size} 条数据")
        
        return data
    
    def _calculate_nutrition_score(self, data: pd.DataFrame) -> pd.Series:
        """计算营养评分"""
        scores = pd.Series(0.0, index=data.index)
        
        # 营养均衡性评分
        protein_ratio = data['protein'] * 4 / data['energy']  # 蛋白质能量比
        carb_ratio = data['carb'] * 4 / data['energy']        # 碳水能量比
        fat_ratio = data['fat'] * 9 / data['energy']          # 脂肪能量比
        
        # 理想比例: 蛋白质10-20%, 碳水50-65%, 脂肪20-35%
        protein_score = 1 - abs(protein_ratio - 0.15) / 0.15
        carb_score = 1 - abs(carb_ratio - 0.575) / 0.575
        fat_score = 1 - abs(fat_ratio - 0.275) / 0.275
        
        scores = (protein_score + carb_score + fat_score) / 3
        scores = scores.clip(0, 1)  # 限制在0-1范围
        
        return scores
    
    def generate_smart_sample(self, strategy: str = "hybrid", target_size: int = 300) -> pd.DataFrame:
        """生成智能样本"""
        print(f"🚀 开始智能采样策略: {strategy} (目标: {target_size} 条)")
        print("=" * 60)
        
        if strategy == "cluster":
            # 纯聚类采样
            n_clusters = max(10, target_size // 15)
            samples_per_cluster = max(1, target_size // n_clusters)
            selected_data = self.cluster_based_sampling(n_clusters, samples_per_cluster)
            
        elif strategy == "diversity":
            # 纯多样性采样
            selected_data = self.diversity_based_sampling(target_size)
            
        elif strategy == "hybrid":
            # 混合策略
            cluster_size = target_size // 2
            diversity_size = target_size - cluster_size
            
            print("📊 混合策略:")
            print(f"  - 聚类采样: {cluster_size} 条")
            print(f"  - 多样性采样: {diversity_size} 条")
            
            # 聚类采样
            n_clusters = max(8, cluster_size // 12)
            samples_per_cluster = max(1, cluster_size // n_clusters)
            cluster_samples = self.cluster_based_sampling(n_clusters, samples_per_cluster)
            
            # 多样性采样 (排除已选择的)
            remaining_data = self.data[~self.data.index.isin(cluster_samples.index)]
            diversity_samples = self.diversity_based_sampling_from_data(remaining_data, diversity_size)
            
            selected_data = pd.concat([cluster_samples, diversity_samples], ignore_index=True)
        
        else:
            raise ValueError(f"不支持的策略: {strategy}")
        
        # 质量过滤
        selected_data = self.quality_based_filtering(selected_data)
        
        # 最终截取到目标大小
        if len(selected_data) > target_size:
            selected_data = selected_data.head(target_size)
        
        self.selected_samples = selected_data
        
        print(f"\n🎉 智能采样完成!")
        print(f"📊 最终样本: {len(selected_data)} 条")
        print(f"📈 覆盖国家: {selected_data['country'].nunique()} 个")
        print(f"⚡ 采样效率: {len(selected_data)/len(self.data)*100:.2f}%")
        
        return selected_data
    
    def diversity_based_sampling_from_data(self, data: pd.DataFrame, target_size: int) -> pd.DataFrame:
        """从指定数据中进行多样性采样"""
        if len(data) <= target_size:
            return data

        # 按营养特征分层采样
        categorized_data = {}
        for category, criteria in self.nutrition_categories.items():
            mask = pd.Series([True] * len(data), index=data.index)  # 修复索引对齐问题
            for nutrient, (min_val, max_val) in criteria.items():
                if max_val == float('inf'):
                    mask &= (data[nutrient] >= min_val)
                else:
                    mask &= (data[nutrient] >= min_val) & (data[nutrient] <= max_val)
            categorized_data[category] = data[mask]
        
        # 从每个类别采样
        samples_per_category = max(1, target_size // len(categorized_data))
        selected_samples = []
        
        for category, cat_data in categorized_data.items():
            if len(cat_data) > 0:
                n_samples = min(samples_per_category, len(cat_data))
                sampled = cat_data.sample(n=n_samples, random_state=42)
                selected_samples.append(sampled)
        
        result = pd.concat(selected_samples, ignore_index=True).drop_duplicates()
        
        # 如果还不够，随机补充
        if len(result) < target_size:
            remaining = target_size - len(result)
            unused_data = data[~data.index.isin(result.index)]
            if len(unused_data) > 0:
                additional = unused_data.sample(n=min(remaining, len(unused_data)), random_state=42)
                result = pd.concat([result, additional], ignore_index=True)
        
        return result.head(target_size)
    
    def save_sampling_report(self, filename: str = "smart_sampling_report.json"):
        """保存采样报告"""
        if len(self.selected_samples) == 0:
            print("⚠️ 没有采样数据，无法生成报告")
            return
        
        report = {
            "sampling_summary": {
                "total_available": len(self.data),
                "selected_samples": len(self.selected_samples),
                "sampling_ratio": len(self.selected_samples) / len(self.data),
                "efficiency_score": len(self.selected_samples) / len(self.data) * 100
            },
            "diversity_analysis": {
                "countries_covered": self.selected_samples['country'].nunique(),
                "country_distribution": self.selected_samples['country'].value_counts().to_dict(),
                "nutrition_ranges": {
                    "energy": {
                        "min": float(self.selected_samples['energy'].min()),
                        "max": float(self.selected_samples['energy'].max()),
                        "mean": float(self.selected_samples['energy'].mean())
                    },
                    "protein": {
                        "min": float(self.selected_samples['protein'].min()),
                        "max": float(self.selected_samples['protein'].max()),
                        "mean": float(self.selected_samples['protein'].mean())
                    }
                }
            },
            "quality_metrics": {
                "avg_nutrition_score": float(self.selected_samples['nutrition_score'].mean()),
                "description_length_avg": float(self.selected_samples['meal_description'].str.len().mean())
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📋 采样报告已保存: {filename}")

def main():
    """主演示函数"""
    print("🧠 智能数据生成策略演示")
    print("=" * 60)
    
    # 加载数据
    try:
        df = pd.read_parquet('NutriBench/v2/train-00000-of-00001.parquet')
        print(f"✅ 成功加载NutriBench数据: {len(df):,} 条")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    # 创建智能采样器
    sampler = SmartDataGenerationStrategy(df)
    
    # 分析数据分布
    analysis = sampler.analyze_data_distribution()
    
    # 演示不同采样策略
    strategies = [
        ("cluster", 200, "基于聚类的采样"),
        ("diversity", 200, "基于多样性的采样"), 
        ("hybrid", 300, "混合策略采样")
    ]
    
    for strategy, size, description in strategies:
        print(f"\n{'='*60}")
        print(f"🎯 策略: {description}")
        print(f"{'='*60}")
        
        selected_data = sampler.generate_smart_sample(strategy=strategy, target_size=size)
        
        # 保存采样结果
        output_file = f"smart_sample_{strategy}_{size}.parquet"
        selected_data.to_parquet(output_file)
        print(f"💾 采样结果已保存: {output_file}")
        
        # 保存报告
        report_file = f"smart_sampling_report_{strategy}.json"
        sampler.save_sampling_report(report_file)
    
    print(f"\n🎉 智能采样策略演示完成!")
    print("💡 建议使用混合策略(hybrid)获得最佳的数据质量和多样性平衡")

if __name__ == "__main__":
    main()
