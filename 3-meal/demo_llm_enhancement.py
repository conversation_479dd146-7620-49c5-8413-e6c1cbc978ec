#!/usr/bin/env python3
"""
LLM增强数据集构建演示脚本
展示如何使用LLM来增强NutriBench数据集的构建过程
"""

import json
import pandas as pd
import asyncio
import time
import os
from typing import Dict, List, Any
import random

# 模拟LLM调用（实际使用时需要真实的API密钥）
class MockLLM:
    """模拟LLM类，用于演示"""
    
    def __init__(self):
        self.response_templates = {
            "nutrition_analysis": """根据您提供的餐食信息，我来为您详细分析：

📊 **营养成分评估**
这个餐食含有碳水化合物{carb}克、蛋白质{protein}克、脂肪{fat}克，总能量{energy}千卡。营养构成较为均衡，能够为{age_group}的宝宝提供必要的营养支持。

📈 **年龄适宜性分析**
对于{age_group}的宝宝来说，这个餐食的营养密度适中，食物质地和营养配比符合该年龄段的发育需求。

💡 **营养建议**
建议搭配新鲜蔬菜和水果，确保维生素和矿物质的充足摄入。同时注意食物的新鲜度和卫生安全。

⚠️ **安全注意事项**
请确保食物充分加热，避免生冷食物。注意观察宝宝的消化反应，如有不适请及时调整。""",

            "diversity_monitoring": """让我为您分析这个餐食的多样性价值：

🌈 **食物多样性分析**
这个餐食涵盖了{food_groups}个主要食物类别，在营养多样性方面表现{diversity_level}。

📊 **营养类别覆盖**
主要提供了碳水化合物和蛋白质，建议增加蔬菜和水果的摄入以提高维生素含量。

⚖️ **营养平衡评估**
当前餐食的宏量营养素比例基本合理，但微量营养素可能需要通过其他食物补充。

🎯 **改进建议**
下一餐可以考虑添加绿叶蔬菜、胡萝卜等富含维生素的食物，以及适量的健康脂肪来源。""",

            "health_guidance": """基于您提供的信息，我来为宝宝制定个性化指导：

👶 **宝宝营养状况评估**
{age_group}的宝宝正处于快速生长期，当前的餐食能够提供{energy}千卡能量，基本满足部分营养需求。

📊 **当前饮食分析**
这餐的蛋白质含量为{protein}克，有助于肌肉和器官发育。碳水化合物{carb}克能提供充足的能量支持。

🎯 **营养缺口识别**
建议增加钙质丰富的食物，如奶制品，以及富含铁质的食物来预防营养缺乏。

💡 **个性化建议**
根据宝宝的年龄特点，建议采用少量多餐的方式，每日安排5-6次进食，确保营养的充分吸收。"""
        }
    
    async def generate_response(self, template_type: str, **kwargs) -> str:
        """生成模拟回答"""
        # 模拟API延迟
        await asyncio.sleep(0.1)
        
        template = self.response_templates.get(template_type, "抱歉，我无法提供相关建议。")
        
        try:
            return template.format(**kwargs)
        except KeyError:
            return template

class LLMEnhancedDemo:
    """LLM增强演示类"""
    
    def __init__(self):
        self.mock_llm = MockLLM()
        self.conversation_types = ["nutrition_analysis", "diversity_monitoring", "health_guidance"]
        self.age_groups = {
            "6-12m": "6-12个月",
            "1-3y": "1-3岁", 
            "3-6y": "3-6岁"
        }
    
    def load_nutribench_data(self, data_path: str = "NutriBench") -> pd.DataFrame:
        """加载NutriBench数据集"""
        print("🔄 加载NutriBench数据集...")

        try:
            # 尝试加载v2版本（推荐）
            v2_path = f"{data_path}/v2/train-00000-of-00001.parquet"
            if os.path.exists(v2_path):
                df = pd.read_parquet(v2_path)
                print(f"✅ 成功加载v2数据: {len(df)} 条记录")
                print(f"📊 覆盖国家: {df['country'].nunique()} 个")

                # 随机采样一些数据用于演示
                sample_size = min(10, len(df))
                df_sample = df.sample(n=sample_size, random_state=42)
                print(f"🎲 随机选择 {sample_size} 条数据用于演示")
                return df_sample
        except Exception as e:
            print(f"❌ v2数据加载失败: {e}")

        # 如果加载失败，使用示例数据
        print("⚠️ 使用示例数据...")
        return self.create_sample_data()

    def create_sample_data(self) -> pd.DataFrame:
        """创建示例数据（备用）"""
        sample_meals = [
            {
                "meal_description": "For breakfast, I ate a plain bun weighing 126 grams and sprinkled on 27 grams of raw sugar.",
                "carb": 90.8,
                "protein": 9.6,
                "fat": 4.2,
                "energy": 439.0,
                "country": "ZMB"
            },
            {
                "meal_description": "For lunch, I had 171 grams of boiled fresh groundnuts in their shells and 230 grams of boiled orange sweet potato without skin.",
                "carb": 97.8,
                "protein": 27.9,
                "fat": 37.8,
                "energy": 806.0,
                "country": "ZMB"
            },
            {
                "meal_description": "I've got 9 grams of boiled kasepa fish, 34 grams of raw maize flour, 3 grams of onion, 9 grams of tomato, and 48 grams of fritters for breakfast.",
                "carb": 51.2,
                "protein": 8.7,
                "fat": 14.2,
                "energy": 363.0,
                "country": "ZMB"
            },
            {
                "meal_description": "For a snack, I'm drinking 360 grams of bottled water.",
                "carb": 0.0,
                "protein": 0.0,
                "fat": 0.0,
                "energy": 0.0,
                "country": "USA"
            },
            {
                "meal_description": "For a snack, I have a chocolate-coated vanilla ice cream bar weighing 75 grams.",
                "carb": 18.38,
                "protein": 3.08,
                "fat": 18.08,
                "energy": 248.25,
                "country": "USA"
            }
        ]

        return pd.DataFrame(sample_meals)
    
    def generate_user_question(self, meal_data: Dict, conv_type: str, age_group: str) -> str:
        """生成用户问题"""
        meal_desc = meal_data['meal_description']
        age_name = self.age_groups[age_group]
        
        questions = {
            "nutrition_analysis": [
                f"我的宝宝{age_name}，今天吃了{meal_desc}，请帮我分析一下营养成分。",
                f"请评估一下{meal_desc}对{age_name}宝宝的营养价值如何？",
                f"{age_name}的宝宝吃{meal_desc}合适吗？营养够吗？"
            ],
            "diversity_monitoring": [
                f"我想了解{meal_desc}对{age_name}宝宝的饮食多样性有什么帮助？",
                f"这个餐食{meal_desc}在营养多样性方面表现如何？",
                f"{age_name}宝宝的饮食多样性，{meal_desc}能贡献什么？"
            ],
            "health_guidance": [
                f"宝宝{age_name}，今天吃了{meal_desc}，还需要补充什么营养？",
                f"我的{age_name}宝宝吃了{meal_desc}，请给我健康指导建议。",
                f"关于{age_name}宝宝的营养，{meal_desc}够吗？怎么改善？"
            ]
        }
        
        return random.choice(questions[conv_type])
    
    async def generate_conversation(self, meal_data: Dict, conv_type: str, age_group: str) -> Dict[str, Any]:
        """生成单个对话"""
        # 生成用户问题
        user_question = self.generate_user_question(meal_data, conv_type, age_group)
        
        # 准备LLM参数
        llm_params = {
            **meal_data,
            "age_group": self.age_groups[age_group],
            "food_groups": random.randint(2, 4),
            "diversity_level": random.choice(["良好", "一般", "需要改善"])
        }
        
        # 调用LLM生成回答
        assistant_response = await self.mock_llm.generate_response(conv_type, **llm_params)
        
        # 构建对话数据
        conversation = {
            "instruction": user_question,
            "output": assistant_response,
            "metadata": {
                "type": conv_type,
                "age_group": age_group,
                "country": meal_data.get("country", "Unknown"),
                "generation_method": "llm_enhanced",
                "meal_energy": meal_data.get("energy", 0),
                "quality_score": random.uniform(75, 95)  # 模拟质量分数
            }
        }
        
        return conversation
    
    async def batch_generate(self, sample_data: pd.DataFrame, conversations_per_meal: int = 2) -> List[Dict[str, Any]]:
        """批量生成对话"""
        print("🚀 开始LLM增强数据生成...")
        
        all_conversations = []
        total_tasks = len(sample_data) * len(self.age_groups) * conversations_per_meal
        completed_tasks = 0
        
        for idx, (_, meal_row) in enumerate(sample_data.iterrows()):
            meal_data = meal_row.to_dict()
            print(f"\n📝 处理餐食 {idx+1}/{len(sample_data)}: {meal_data['meal_description'][:30]}...")
            
            for age_group in self.age_groups.keys():
                for _ in range(conversations_per_meal):
                    # 随机选择对话类型
                    conv_type = random.choice(self.conversation_types)
                    
                    try:
                        conversation = await self.generate_conversation(meal_data, conv_type, age_group)
                        all_conversations.append(conversation)
                        
                        completed_tasks += 1
                        progress = completed_tasks / total_tasks * 100
                        print(f"  ✅ 生成 {conv_type} ({age_group}) - 进度: {progress:.1f}%")
                        
                    except Exception as e:
                        print(f"  ❌ 生成失败: {str(e)}")
                        continue
        
        print(f"\n🎉 生成完成！总共生成了 {len(all_conversations)} 条对话")
        return all_conversations
    
    def analyze_generated_data(self, conversations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析生成的数据"""
        print("\n📊 数据分析结果:")
        
        # 统计对话类型分布
        type_counts = {}
        age_counts = {}
        quality_scores = []
        
        for conv in conversations:
            metadata = conv['metadata']
            
            # 统计类型
            conv_type = metadata['type']
            type_counts[conv_type] = type_counts.get(conv_type, 0) + 1
            
            # 统计年龄组
            age_group = metadata['age_group']
            age_counts[age_group] = age_counts.get(age_group, 0) + 1
            
            # 收集质量分数
            quality_scores.append(metadata.get('quality_score', 0))
        
        # 计算统计信息
        avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
        
        analysis = {
            "total_conversations": len(conversations),
            "type_distribution": type_counts,
            "age_distribution": age_counts,
            "average_quality_score": avg_quality,
            "high_quality_count": sum(1 for score in quality_scores if score >= 80)
        }
        
        # 打印分析结果
        print(f"  📈 总对话数: {analysis['total_conversations']}")
        print(f"  🎯 平均质量分: {analysis['average_quality_score']:.1f}")
        print(f"  ⭐ 高质量对话: {analysis['high_quality_count']} ({analysis['high_quality_count']/len(conversations)*100:.1f}%)")
        
        print(f"\n  📋 对话类型分布:")
        for conv_type, count in analysis['type_distribution'].items():
            percentage = count / len(conversations) * 100
            print(f"    {conv_type}: {count} ({percentage:.1f}%)")
        
        print(f"\n  👶 年龄组分布:")
        for age_group, count in analysis['age_distribution'].items():
            percentage = count / len(conversations) * 100
            print(f"    {age_group}: {count} ({percentage:.1f}%)")
        
        return analysis
    
    def save_dataset(self, conversations: List[Dict[str, Any]], filename: str = "llm_enhanced_demo_dataset.json"):
        """保存数据集"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(conversations, f, ensure_ascii=False, indent=2)
        print(f"\n💾 数据集已保存到: {filename}")
    
    def show_sample_conversations(self, conversations: List[Dict[str, Any]], num_samples: int = 3):
        """展示样本对话"""
        print(f"\n📖 样本对话展示 (随机选择{num_samples}条):")
        
        samples = random.sample(conversations, min(num_samples, len(conversations)))
        
        for i, conv in enumerate(samples, 1):
            print(f"\n--- 样本 {i} ---")
            print(f"类型: {conv['metadata']['type']}")
            print(f"年龄组: {conv['metadata']['age_group']}")
            print(f"质量分: {conv['metadata']['quality_score']:.1f}")
            print(f"\n用户问题:")
            print(conv['instruction'])
            print(f"\n助手回答:")
            print(conv['output'][:200] + "..." if len(conv['output']) > 200 else conv['output'])

async def main():
    """主演示函数"""
    print("=" * 60)
    print("🤖 LLM增强的NutriBench数据集构建演示")
    print("=" * 60)
    
    # 创建演示实例
    demo = LLMEnhancedDemo()
    
    # 加载NutriBench数据
    print("\n📋 加载NutriBench数据...")
    sample_data = demo.load_nutribench_data()
    print(f"准备了 {len(sample_data)} 个餐食样本")
    
    # 生成对话数据
    conversations = await demo.batch_generate(sample_data, conversations_per_meal=2)
    
    # 分析生成的数据
    analysis = demo.analyze_generated_data(conversations)
    
    # 展示样本对话
    demo.show_sample_conversations(conversations, num_samples=2)
    
    # 保存数据集
    demo.save_dataset(conversations)
    
    print("\n" + "=" * 60)
    print("✨ 演示完成！")
    print("\n💡 实际使用时的建议:")
    print("1. 配置真实的OpenAI API密钥")
    print("2. 使用完整的NutriBench数据集")
    print("3. 调整生成参数以优化质量和成本")
    print("4. 实施人工审核流程")
    print("5. 持续监控和优化数据质量")
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
