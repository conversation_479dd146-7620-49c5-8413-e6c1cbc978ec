#!/usr/bin/env python3
"""
简化版智能采样演示
避免复杂的机器学习依赖，使用简单有效的采样策略
"""

import pandas as pd
import numpy as np
import json
import random
from typing import Dict, List, Any

class SimpleSmartSampler:
    """简化版智能采样器"""
    
    def __init__(self, data: pd.DataFrame):
        self.data = data
        print(f"📊 加载数据: {len(data):,} 条记录")
        print(f"🌍 覆盖国家: {data['country'].nunique()} 个")
        print(f"⚡ 能量范围: {data['energy'].min():.1f} - {data['energy'].max():.1f} kcal")
    
    def stratified_sampling(self, target_size: int = 300) -> pd.DataFrame:
        """分层采样策略"""
        print(f"\n🎯 分层采样策略 (目标: {target_size} 条)")
        print("-" * 50)
        
        selected_samples = []
        
        # 1. 按国家分层采样 (确保地域多样性)
        countries = self.data['country'].unique()
        base_samples_per_country = max(1, target_size // (len(countries) * 2))  # 预留一半给其他策略
        
        print(f"📍 按国家分层采样 (每国 {base_samples_per_country} 条):")
        country_samples = []
        for country in countries:
            country_data = self.data[self.data['country'] == country]
            n_samples = min(base_samples_per_country, len(country_data))
            sampled = country_data.sample(n=n_samples, random_state=42)
            country_samples.append(sampled)
            print(f"  {country}: {len(country_data)} → {n_samples} 条")
        
        country_combined = pd.concat(country_samples, ignore_index=True)
        selected_samples.append(country_combined)
        
        # 2. 按营养特征采样 (确保营养多样性)
        remaining_target = target_size - len(country_combined)
        if remaining_target > 0:
            print(f"\n🍎 按营养特征采样 (剩余目标: {remaining_target} 条):")
            
            # 定义营养区间
            nutrition_ranges = {
                "低能量": (0, 200),
                "中等能量": (200, 400), 
                "高能量": (400, float('inf')),
                "高蛋白": (15, float('inf')),
                "低蛋白": (0, 5),
                "高碳水": (50, float('inf')),
                "高脂肪": (15, float('inf'))
            }
            
            samples_per_range = max(1, remaining_target // len(nutrition_ranges))
            nutrition_samples = []
            
            for range_name, (min_energy, max_energy) in nutrition_ranges.items():
                if "能量" in range_name:
                    if max_energy == float('inf'):
                        range_data = self.data[self.data['energy'] >= min_energy]
                    else:
                        range_data = self.data[(self.data['energy'] >= min_energy) & 
                                             (self.data['energy'] < max_energy)]
                elif "蛋白" in range_name:
                    if max_energy == float('inf'):
                        range_data = self.data[self.data['protein'] >= min_energy]
                    else:
                        range_data = self.data[self.data['protein'] < max_energy]
                elif "碳水" in range_name:
                    range_data = self.data[self.data['carb'] >= min_energy]
                elif "脂肪" in range_name:
                    range_data = self.data[self.data['fat'] >= min_energy]
                
                # 排除已选择的样本
                existing_indices = set()
                for df in selected_samples:
                    existing_indices.update(df.index)
                range_data = range_data[~range_data.index.isin(existing_indices)]
                
                if len(range_data) > 0:
                    n_samples = min(samples_per_range, len(range_data))
                    sampled = range_data.sample(n=n_samples, random_state=42)
                    nutrition_samples.append(sampled)
                    print(f"  {range_name}: {len(range_data)} → {n_samples} 条")
            
            if nutrition_samples:
                nutrition_combined = pd.concat(nutrition_samples, ignore_index=True)
                selected_samples.append(nutrition_combined)
        
        # 3. 合并所有样本
        final_samples = pd.concat(selected_samples, ignore_index=True).drop_duplicates()
        
        # 4. 如果超过目标，随机选择
        if len(final_samples) > target_size:
            final_samples = final_samples.sample(n=target_size, random_state=42)
        
        print(f"\n✅ 分层采样完成: {len(final_samples)} 条")
        return final_samples
    
    def quality_focused_sampling(self, target_size: int = 200) -> pd.DataFrame:
        """质量优先采样"""
        print(f"\n🏆 质量优先采样 (目标: {target_size} 条)")
        print("-" * 50)
        
        # 1. 数据清洗
        clean_data = self.data.copy()
        
        # 移除异常值
        original_size = len(clean_data)
        clean_data = clean_data[
            (clean_data['energy'] >= 10) & (clean_data['energy'] <= 2000) &
            (clean_data['protein'] >= 0) & (clean_data['protein'] <= 80) &
            (clean_data['carb'] >= 0) & (clean_data['carb'] <= 200) &
            (clean_data['fat'] >= 0) & (clean_data['fat'] <= 100)
        ]
        
        # 移除描述过短的数据
        clean_data = clean_data[clean_data['meal_description'].str.len() >= 20]
        
        print(f"🧹 数据清洗: {original_size} → {len(clean_data)} 条")
        
        # 2. 计算质量评分
        clean_data = clean_data.copy()
        clean_data['quality_score'] = self._calculate_quality_score(clean_data)
        
        # 3. 按质量评分排序并采样
        clean_data = clean_data.sort_values('quality_score', ascending=False)
        
        # 4. 分层采样高质量数据
        high_quality = clean_data.head(target_size * 2)  # 取前2倍数量
        
        # 在高质量数据中进行多样性采样
        countries = high_quality['country'].unique()
        samples_per_country = max(1, target_size // len(countries))
        
        selected_samples = []
        for country in countries:
            country_data = high_quality[high_quality['country'] == country]
            n_samples = min(samples_per_country, len(country_data))
            sampled = country_data.head(n_samples)  # 取质量最高的
            selected_samples.append(sampled)
            print(f"  {country}: 质量评分 {sampled['quality_score'].mean():.3f}, 采样 {n_samples} 条")
        
        final_samples = pd.concat(selected_samples, ignore_index=True)
        
        # 如果不够，补充高质量样本
        if len(final_samples) < target_size:
            remaining = target_size - len(final_samples)
            unused_high_quality = high_quality[~high_quality.index.isin(final_samples.index)]
            additional = unused_high_quality.head(remaining)
            final_samples = pd.concat([final_samples, additional], ignore_index=True)
        
        print(f"✅ 质量采样完成: {len(final_samples)} 条")
        print(f"📊 平均质量评分: {final_samples['quality_score'].mean():.3f}")
        
        return final_samples
    
    def _calculate_quality_score(self, data: pd.DataFrame) -> pd.Series:
        """计算质量评分"""
        scores = pd.Series(0.0, index=data.index)
        
        # 1. 营养均衡性评分 (40%)
        total_energy = data['energy']
        protein_energy = data['protein'] * 4
        carb_energy = data['carb'] * 4
        fat_energy = data['fat'] * 9
        
        # 避免除零错误
        total_energy = total_energy.replace(0, 1)
        
        protein_ratio = protein_energy / total_energy
        carb_ratio = carb_energy / total_energy
        fat_ratio = fat_energy / total_energy
        
        # 理想比例: 蛋白质10-20%, 碳水50-65%, 脂肪20-35%
        protein_score = 1 - np.abs(protein_ratio - 0.15) / 0.15
        carb_score = 1 - np.abs(carb_ratio - 0.575) / 0.575
        fat_score = 1 - np.abs(fat_ratio - 0.275) / 0.275
        
        nutrition_score = (protein_score + carb_score + fat_score) / 3
        nutrition_score = nutrition_score.clip(0, 1)
        
        # 2. 描述质量评分 (30%)
        desc_length = data['meal_description'].str.len()
        desc_score = np.minimum(desc_length / 100, 1.0)  # 100字符为满分
        
        # 3. 营养密度评分 (30%)
        # 蛋白质密度 (g/100kcal)
        protein_density = (data['protein'] / data['energy'] * 100).clip(0, 20) / 20
        
        # 综合评分
        scores = (nutrition_score * 0.4 + desc_score * 0.3 + protein_density * 0.3)
        
        return scores.clip(0, 1)
    
    def balanced_sampling(self, target_size: int = 300) -> pd.DataFrame:
        """均衡采样策略"""
        print(f"\n⚖️ 均衡采样策略 (目标: {target_size} 条)")
        print("-" * 50)
        
        # 分配采样比例
        stratified_size = target_size // 2
        quality_size = target_size - stratified_size
        
        print(f"📊 采样分配:")
        print(f"  - 分层采样: {stratified_size} 条")
        print(f"  - 质量采样: {quality_size} 条")
        
        # 1. 分层采样
        stratified_samples = self.stratified_sampling(stratified_size)
        
        # 2. 质量采样 (排除已选择的)
        remaining_data = self.data[~self.data.index.isin(stratified_samples.index)]
        quality_sampler = SimpleSmartSampler(remaining_data)
        quality_samples = quality_sampler.quality_focused_sampling(quality_size)
        
        # 3. 合并
        final_samples = pd.concat([stratified_samples, quality_samples], ignore_index=True)
        
        print(f"\n✅ 均衡采样完成: {len(final_samples)} 条")
        print(f"🌍 覆盖国家: {final_samples['country'].nunique()} 个")
        print(f"📊 能量范围: {final_samples['energy'].min():.1f} - {final_samples['energy'].max():.1f} kcal")
        
        return final_samples

def demonstrate_sampling_strategies():
    """演示采样策略"""
    print("🧠 简化版智能采样策略演示")
    print("=" * 60)
    
    # 加载数据
    try:
        df = pd.read_parquet('NutriBench/v2/train-00000-of-00001.parquet')
        print(f"✅ 成功加载NutriBench数据: {len(df):,} 条")
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        return
    
    sampler = SimpleSmartSampler(df)
    
    # 演示不同策略
    strategies = [
        ("stratified", 200, "分层采样"),
        ("quality", 150, "质量优先采样"),
        ("balanced", 300, "均衡采样")
    ]
    
    results = {}
    
    for strategy_name, size, description in strategies:
        print(f"\n{'='*60}")
        print(f"🎯 策略: {description}")
        print(f"{'='*60}")
        
        if strategy_name == "stratified":
            selected_data = sampler.stratified_sampling(size)
        elif strategy_name == "quality":
            selected_data = sampler.quality_focused_sampling(size)
        elif strategy_name == "balanced":
            selected_data = sampler.balanced_sampling(size)
        
        # 保存结果
        output_file = f"simple_smart_sample_{strategy_name}_{size}.parquet"
        selected_data.to_parquet(output_file)
        print(f"💾 采样结果已保存: {output_file}")
        
        # 生成统计报告
        stats = {
            "strategy": description,
            "target_size": size,
            "actual_size": len(selected_data),
            "countries_covered": int(selected_data['country'].nunique()),
            "sampling_efficiency": len(selected_data) / len(df) * 100,
            "nutrition_stats": {
                "energy_mean": float(selected_data['energy'].mean()),
                "energy_std": float(selected_data['energy'].std()),
                "protein_mean": float(selected_data['protein'].mean()),
                "protein_std": float(selected_data['protein'].std())
            },
            "country_distribution": selected_data['country'].value_counts().to_dict()
        }
        
        stats_file = f"simple_sampling_stats_{strategy_name}.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"📊 统计报告已保存: {stats_file}")
        results[strategy_name] = selected_data
    
    # 对比分析
    print(f"\n{'='*60}")
    print("📊 策略对比分析")
    print(f"{'='*60}")
    
    for strategy_name, data in results.items():
        print(f"\n{strategies[[s[0] for s in strategies].index(strategy_name)][2]}:")
        print(f"  样本数量: {len(data)} 条")
        print(f"  国家覆盖: {data['country'].nunique()} 个")
        print(f"  采样效率: {len(data)/len(df)*100:.3f}%")
        print(f"  平均能量: {data['energy'].mean():.1f} kcal")
        print(f"  平均蛋白质: {data['protein'].mean():.1f} g")
    
    print(f"\n🎉 演示完成!")
    print("💡 建议:")
    print("  - 数据量优先: 使用分层采样")
    print("  - 质量优先: 使用质量采样")
    print("  - 平衡考虑: 使用均衡采样")

if __name__ == "__main__":
    demonstrate_sampling_strategies()
