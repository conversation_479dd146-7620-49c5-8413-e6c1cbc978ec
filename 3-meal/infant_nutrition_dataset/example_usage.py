#!/usr/bin/env python3
"""
婴幼儿营养指导数据集使用示例
Example Usage of Infant Nutrition Guidance Dataset

展示如何加载和使用数据集进行各种任务
"""

import json
import pandas as pd
from load_dataset import InfantNutritionDataset
from collections import Counter

def main():
    """主示例函数"""
    print("🍼 婴幼儿营养指导数据集使用示例")
    print("=" * 60)
    
    # 1. 初始化数据集
    print("\n1️⃣ 初始化数据集...")
    dataset = InfantNutritionDataset()
    
    # 2. 加载主数据集
    print("\n2️⃣ 加载主数据集...")
    conversations = dataset.load_main_dataset()
    print(f"✅ 成功加载 {len(conversations)} 条对话")
    
    # 3. 获取数据集统计信息
    print("\n3️⃣ 数据集统计信息...")
    stats = dataset.get_statistics()
    print(f"📊 总对话数: {stats['total_conversations']}")
    print(f"📊 对话类型数: {stats['unique_types']}")
    print(f"📊 年龄段数: {stats['unique_age_groups']}")
    print(f"📊 国家数: {stats['unique_countries']}")
    
    # 4. 展示对话类型分布
    print("\n4️⃣ 对话类型分布:")
    for conv_type, count in stats['conversation_types'].items():
        percentage = (count / stats['total_conversations']) * 100
        print(f"   {conv_type}: {count} 条 ({percentage:.1f}%)")
    
    # 5. 展示年龄段分布
    print("\n5️⃣ 年龄段分布:")
    for age_group, count in stats['age_groups'].items():
        percentage = (count / stats['total_conversations']) * 100
        print(f"   {age_group}: {count} 条 ({percentage:.1f}%)")
    
    # 6. 打印样本对话
    print("\n6️⃣ 样本对话展示:")
    dataset.print_sample(0)
    
    # 7. 按类型筛选数据
    print("\n7️⃣ 按类型筛选示例...")
    nutrition_analysis = dataset.filter_by_type('nutrition_analysis')
    feeding_guidance = dataset.filter_by_type('feeding_guidance')
    safety_advice = dataset.filter_by_type('safety_advice')
    
    print(f"   营养分析对话: {len(nutrition_analysis)} 条")
    print(f"   喂养指导对话: {len(feeding_guidance)} 条")
    print(f"   安全建议对话: {len(safety_advice)} 条")
    
    # 8. 按年龄段筛选数据
    print("\n8️⃣ 按年龄段筛选示例...")
    toddler_conversations = dataset.filter_by_age_group('12-24m')
    infant_conversations = dataset.filter_by_age_group('9-12m')
    
    print(f"   12-24月龄对话: {len(toddler_conversations)} 条")
    print(f"   9-12月龄对话: {len(infant_conversations)} 条")
    
    # 9. 转换为DataFrame进行分析
    print("\n9️⃣ 转换为DataFrame...")
    df = dataset.to_dataframe()
    print(f"   DataFrame形状: {df.shape}")
    print(f"   列名: {list(df.columns)}")
    
    # 10. 数据质量分析
    print("\n🔟 数据质量分析...")
    analyze_data_quality(df)
    
    # 11. 导出不同格式的训练数据
    print("\n1️⃣1️⃣ 导出训练数据...")
    dataset.export_for_training('training_data.jsonl', 'jsonl')
    dataset.export_for_training('training_data.csv', 'csv')
    print("   ✅ 已导出 JSONL 和 CSV 格式")
    
    # 12. 加载其他数据集
    print("\n1️⃣2️⃣ 加载其他数据集...")
    try:
        demo_data = dataset.load_demo_dataset()
        print(f"   演示数据集: {len(demo_data)} 条")
        
        enhanced_data = dataset.load_enhanced_dataset()
        print(f"   增强数据集: {len(enhanced_data)} 条")
    except FileNotFoundError as e:
        print(f"   ⚠️ 文件未找到: {e}")
    
    # 13. 加载知识库
    print("\n1️⃣3️⃣ 加载知识库...")
    try:
        guidelines = dataset.load_knowledge_base('infant_feeding_guidelines.json')
        nutrition_guide = dataset.load_knowledge_base('nutrition_guidelines.json')
        print(f"   ✅ 成功加载喂养指南和营养指导")
    except FileNotFoundError as e:
        print(f"   ⚠️ 知识库文件未找到: {e}")
    
    print("\n🎉 示例演示完成！")
    print("=" * 60)

def analyze_data_quality(df):
    """分析数据质量"""
    print("   数据质量指标:")
    
    # 检查空值
    null_counts = df.isnull().sum()
    if null_counts.sum() > 0:
        print(f"   ⚠️ 发现空值: {null_counts[null_counts > 0].to_dict()}")
    else:
        print("   ✅ 无空值")
    
    # 检查文本长度
    if 'instruction' in df.columns:
        avg_instruction_length = df['instruction'].str.len().mean()
        print(f"   📏 平均问题长度: {avg_instruction_length:.1f} 字符")
    
    if 'output' in df.columns:
        avg_output_length = df['output'].str.len().mean()
        print(f"   📏 平均回答长度: {avg_output_length:.1f} 字符")
    
    # 检查数据分布
    if 'type' in df.columns:
        type_balance = df['type'].value_counts()
        min_count = type_balance.min()
        max_count = type_balance.max()
        balance_ratio = min_count / max_count
        print(f"   ⚖️ 类型平衡度: {balance_ratio:.3f} (1.0为完全平衡)")
    
    if 'age_group' in df.columns:
        age_balance = df['age_group'].value_counts()
        print(f"   👶 年龄段覆盖: {len(age_balance)} 个年龄段")

def create_visualizations(df):
    """创建数据可视化（需要matplotlib和seaborn）"""
    try:
        import matplotlib.pyplot as plt
        import seaborn as sns
        
        plt.style.use('default')
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 对话类型分布
        if 'type' in df.columns:
            type_counts = df['type'].value_counts()
            axes[0, 0].pie(type_counts.values, labels=type_counts.index, autopct='%1.1f%%')
            axes[0, 0].set_title('对话类型分布')
        
        # 年龄段分布
        if 'age_group' in df.columns:
            age_counts = df['age_group'].value_counts()
            axes[0, 1].bar(age_counts.index, age_counts.values)
            axes[0, 1].set_title('年龄段分布')
            axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 文本长度分布
        if 'output' in df.columns:
            output_lengths = df['output'].str.len()
            axes[1, 0].hist(output_lengths, bins=30, alpha=0.7)
            axes[1, 0].set_title('回答长度分布')
            axes[1, 0].set_xlabel('字符数')
        
        # 国家分布（前10个）
        if 'country' in df.columns:
            country_counts = df['country'].value_counts().head(10)
            axes[1, 1].bar(country_counts.index, country_counts.values)
            axes[1, 1].set_title('国家分布 (前10)')
            axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig('dataset_visualization.png', dpi=300, bbox_inches='tight')
        print("   📊 可视化图表已保存为 dataset_visualization.png")
        
    except ImportError:
        print("   ⚠️ 需要安装 matplotlib 和 seaborn 来生成可视化")

def demonstrate_training_preparation():
    """演示训练数据准备"""
    print("\n🚀 训练数据准备示例:")
    
    dataset = InfantNutritionDataset()
    conversations = dataset.load_main_dataset()
    
    # 准备训练格式
    training_samples = []
    for conv in conversations:
        sample = {
            "messages": [
                {"role": "user", "content": conv["instruction"]},
                {"role": "assistant", "content": conv["output"]}
            ],
            "metadata": conv["metadata"]
        }
        training_samples.append(sample)
    
    print(f"   ✅ 准备了 {len(training_samples)} 个训练样本")
    
    # 按类型分割数据
    type_splits = {}
    for sample in training_samples:
        conv_type = sample["metadata"]["type"]
        if conv_type not in type_splits:
            type_splits[conv_type] = []
        type_splits[conv_type].append(sample)
    
    print("   📊 按类型分割:")
    for conv_type, samples in type_splits.items():
        print(f"      {conv_type}: {len(samples)} 样本")
    
    # 保存训练格式
    with open('training_format.jsonl', 'w', encoding='utf-8') as f:
        for sample in training_samples[:5]:  # 只保存前5个作为示例
            f.write(json.dumps(sample, ensure_ascii=False) + '\n')
    
    print("   💾 训练格式示例已保存为 training_format.jsonl")

if __name__ == "__main__":
    main()
    demonstrate_training_preparation()
