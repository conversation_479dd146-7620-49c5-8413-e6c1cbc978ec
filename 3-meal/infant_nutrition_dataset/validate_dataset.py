#!/usr/bin/env python3
"""
数据集验证脚本
Dataset Validation Script

验证数据集的完整性、格式正确性和质量指标
"""

import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DatasetValidator:
    """数据集验证器"""
    
    def __init__(self, dataset_path: str = "."):
        self.dataset_path = Path(dataset_path)
        self.validation_results = {}
    
    def validate_all(self) -> Dict:
        """执行所有验证检查"""
        logger.info("开始数据集验证...")
        
        # 验证文件存在性
        self.validation_results['file_existence'] = self._validate_file_existence()
        
        # 验证JSON格式
        self.validation_results['json_format'] = self._validate_json_format()
        
        # 验证数据结构
        self.validation_results['data_structure'] = self._validate_data_structure()
        
        # 验证数据质量
        self.validation_results['data_quality'] = self._validate_data_quality()
        
        # 验证统计一致性
        self.validation_results['statistics_consistency'] = self._validate_statistics()
        
        # 生成验证报告
        self._generate_validation_report()
        
        return self.validation_results
    
    def _validate_file_existence(self) -> Dict:
        """验证必要文件是否存在"""
        logger.info("验证文件存在性...")
        
        required_files = [
            "efficient_dataset_quality_focused_1753096578.json",
            "enhanced_qwen_0_3_years_dataset.json", 
            "qwen_0_3_years_demo_dataset.json",
            "infant_feeding_guidelines.json",
            "辅食添加要点.json",
            "nutrition_guidelines.json",
            "README.md",
            "dataset_info.json"
        ]
        
        results = {}
        for filename in required_files:
            file_path = self.dataset_path / filename
            results[filename] = {
                'exists': file_path.exists(),
                'size_bytes': file_path.stat().st_size if file_path.exists() else 0
            }
        
        missing_files = [f for f, r in results.items() if not r['exists']]
        if missing_files:
            logger.warning(f"缺失文件: {missing_files}")
        else:
            logger.info("所有必要文件都存在")
        
        return {
            'files': results,
            'missing_count': len(missing_files),
            'total_files': len(required_files),
            'all_present': len(missing_files) == 0
        }
    
    def _validate_json_format(self) -> Dict:
        """验证JSON文件格式"""
        logger.info("验证JSON格式...")
        
        json_files = [
            "efficient_dataset_quality_focused_1753096578.json",
            "enhanced_qwen_0_3_years_dataset.json",
            "qwen_0_3_years_demo_dataset.json",
            "infant_feeding_guidelines.json",
            "辅食添加要点.json",
            "nutrition_guidelines.json",
            "dataset_info.json"
        ]
        
        results = {}
        for filename in json_files:
            file_path = self.dataset_path / filename
            if not file_path.exists():
                results[filename] = {'valid': False, 'error': 'File not found'}
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                results[filename] = {
                    'valid': True,
                    'type': type(data).__name__,
                    'length': len(data) if isinstance(data, (list, dict)) else None
                }
            except json.JSONDecodeError as e:
                results[filename] = {'valid': False, 'error': str(e)}
            except Exception as e:
                results[filename] = {'valid': False, 'error': f"Unexpected error: {str(e)}"}
        
        invalid_files = [f for f, r in results.items() if not r.get('valid', False)]
        if invalid_files:
            logger.error(f"JSON格式错误的文件: {invalid_files}")
        else:
            logger.info("所有JSON文件格式正确")
        
        return {
            'files': results,
            'invalid_count': len(invalid_files),
            'all_valid': len(invalid_files) == 0
        }
    
    def _validate_data_structure(self) -> Dict:
        """验证数据结构"""
        logger.info("验证数据结构...")
        
        # 验证主数据集结构
        main_file = self.dataset_path / "efficient_dataset_quality_focused_1753096578.json"
        if not main_file.exists():
            return {'error': 'Main dataset file not found'}
        
        try:
            with open(main_file, 'r', encoding='utf-8') as f:
                conversations = json.load(f)
        except Exception as e:
            return {'error': f'Failed to load main dataset: {str(e)}'}
        
        if not isinstance(conversations, list):
            return {'error': 'Main dataset should be a list'}
        
        # 验证对话结构
        required_fields = ['instruction', 'output', 'metadata']
        required_metadata_fields = [
            'type', 'age_group', 'stage', 'country', 'generation_method',
            'model', 'based_on_official_guidelines'
        ]
        
        structure_errors = []
        valid_conversations = 0
        
        for i, conv in enumerate(conversations[:10]):  # 检查前10个样本
            if not isinstance(conv, dict):
                structure_errors.append(f"Conversation {i}: Not a dictionary")
                continue
            
            # 检查必需字段
            missing_fields = [field for field in required_fields if field not in conv]
            if missing_fields:
                structure_errors.append(f"Conversation {i}: Missing fields {missing_fields}")
                continue
            
            # 检查元数据字段
            metadata = conv.get('metadata', {})
            missing_metadata = [field for field in required_metadata_fields if field not in metadata]
            if missing_metadata:
                structure_errors.append(f"Conversation {i}: Missing metadata fields {missing_metadata}")
                continue
            
            valid_conversations += 1
        
        return {
            'total_conversations': len(conversations),
            'sample_checked': min(10, len(conversations)),
            'valid_conversations': valid_conversations,
            'structure_errors': structure_errors,
            'structure_valid': len(structure_errors) == 0
        }
    
    def _validate_data_quality(self) -> Dict:
        """验证数据质量"""
        logger.info("验证数据质量...")
        
        main_file = self.dataset_path / "efficient_dataset_quality_focused_1753096578.json"
        if not main_file.exists():
            return {'error': 'Main dataset file not found'}
        
        try:
            with open(main_file, 'r', encoding='utf-8') as f:
                conversations = json.load(f)
        except Exception as e:
            return {'error': f'Failed to load main dataset: {str(e)}'}
        
        quality_metrics = {
            'total_conversations': len(conversations),
            'empty_instructions': 0,
            'empty_outputs': 0,
            'short_outputs': 0,  # 少于100字符
            'long_outputs': 0,   # 超过5000字符
            'missing_metadata': 0,
            'invalid_age_groups': 0,
            'invalid_types': 0
        }
        
        valid_age_groups = ['0-6m', '6m_start', '6-9m', '9-12m', '12-24m']
        valid_types = [
            'nutrition_analysis', 'feeding_guidance', 'texture_advice',
            'portion_guidance', 'safety_advice', 'problem_solving'
        ]
        
        for conv in conversations:
            # 检查指令
            instruction = conv.get('instruction', '')
            if not instruction or instruction.strip() == '':
                quality_metrics['empty_instructions'] += 1
            
            # 检查输出
            output = conv.get('output', '')
            if not output or output.strip() == '':
                quality_metrics['empty_outputs'] += 1
            elif len(output) < 100:
                quality_metrics['short_outputs'] += 1
            elif len(output) > 5000:
                quality_metrics['long_outputs'] += 1
            
            # 检查元数据
            metadata = conv.get('metadata', {})
            if not metadata:
                quality_metrics['missing_metadata'] += 1
                continue
            
            # 检查年龄段
            age_group = metadata.get('age_group')
            if age_group not in valid_age_groups:
                quality_metrics['invalid_age_groups'] += 1
            
            # 检查对话类型
            conv_type = metadata.get('type')
            if conv_type not in valid_types:
                quality_metrics['invalid_types'] += 1
        
        # 计算质量分数
        total = quality_metrics['total_conversations']
        if total > 0:
            quality_score = 1.0 - (
                quality_metrics['empty_instructions'] +
                quality_metrics['empty_outputs'] +
                quality_metrics['missing_metadata'] +
                quality_metrics['invalid_age_groups'] +
                quality_metrics['invalid_types']
            ) / total
        else:
            quality_score = 0.0
        
        quality_metrics['quality_score'] = quality_score
        quality_metrics['quality_grade'] = self._get_quality_grade(quality_score)
        
        return quality_metrics
    
    def _validate_statistics(self) -> Dict:
        """验证统计一致性"""
        logger.info("验证统计一致性...")
        
        # 加载主数据集
        main_file = self.dataset_path / "efficient_dataset_quality_focused_1753096578.json"
        if not main_file.exists():
            return {'error': 'Main dataset file not found'}
        
        try:
            with open(main_file, 'r', encoding='utf-8') as f:
                conversations = json.load(f)
        except Exception as e:
            return {'error': f'Failed to load main dataset: {str(e)}'}
        
        # 计算实际统计
        actual_stats = {
            'total_conversations': len(conversations),
            'type_distribution': {},
            'age_distribution': {},
            'country_distribution': {}
        }
        
        for conv in conversations:
            metadata = conv.get('metadata', {})
            
            # 类型分布
            conv_type = metadata.get('type', 'unknown')
            actual_stats['type_distribution'][conv_type] = \
                actual_stats['type_distribution'].get(conv_type, 0) + 1
            
            # 年龄分布
            age_group = metadata.get('age_group', 'unknown')
            actual_stats['age_distribution'][age_group] = \
                actual_stats['age_distribution'].get(age_group, 0) + 1
            
            # 国家分布
            country = metadata.get('country', 'unknown')
            actual_stats['country_distribution'][country] = \
                actual_stats['country_distribution'].get(country, 0) + 1
        
        # 加载预期统计（如果存在）
        stats_file = self.dataset_path / "generation_stats_quality_focused_1753096578.json"
        expected_stats = None
        if stats_file.exists():
            try:
                with open(stats_file, 'r', encoding='utf-8') as f:
                    expected_stats = json.load(f)
            except Exception as e:
                logger.warning(f"无法加载统计文件: {str(e)}")
        
        return {
            'actual_stats': actual_stats,
            'expected_stats': expected_stats,
            'stats_file_exists': stats_file.exists(),
            'consistency_check': expected_stats is not None
        }
    
    def _get_quality_grade(self, score: float) -> str:
        """根据质量分数获取等级"""
        if score >= 0.95:
            return "优秀 (Excellent)"
        elif score >= 0.90:
            return "良好 (Good)"
        elif score >= 0.80:
            return "一般 (Fair)"
        else:
            return "需要改进 (Needs Improvement)"
    
    def _generate_validation_report(self):
        """生成验证报告"""
        logger.info("生成验证报告...")
        
        report = {
            "validation_summary": {
                "timestamp": pd.Timestamp.now().isoformat(),
                "overall_status": "PASS",
                "critical_issues": [],
                "warnings": []
            },
            "detailed_results": self.validation_results
        }
        
        # 检查关键问题
        if not self.validation_results.get('file_existence', {}).get('all_present', False):
            report["validation_summary"]["critical_issues"].append("Missing required files")
            report["validation_summary"]["overall_status"] = "FAIL"
        
        if not self.validation_results.get('json_format', {}).get('all_valid', False):
            report["validation_summary"]["critical_issues"].append("Invalid JSON format")
            report["validation_summary"]["overall_status"] = "FAIL"
        
        if not self.validation_results.get('data_structure', {}).get('structure_valid', False):
            report["validation_summary"]["critical_issues"].append("Invalid data structure")
            report["validation_summary"]["overall_status"] = "FAIL"
        
        # 检查警告
        quality_score = self.validation_results.get('data_quality', {}).get('quality_score', 0)
        if quality_score < 0.90:
            report["validation_summary"]["warnings"].append(f"Low quality score: {quality_score:.3f}")
        
        # 保存报告
        report_file = self.dataset_path / "validation_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"验证报告已保存: {report_file}")
        
        # 打印摘要
        print("\n" + "="*60)
        print("数据集验证报告摘要")
        print("="*60)
        print(f"整体状态: {report['validation_summary']['overall_status']}")
        
        if report['validation_summary']['critical_issues']:
            print(f"关键问题: {', '.join(report['validation_summary']['critical_issues'])}")
        
        if report['validation_summary']['warnings']:
            print(f"警告: {', '.join(report['validation_summary']['warnings'])}")
        
        # 打印详细结果
        if 'data_quality' in self.validation_results:
            quality = self.validation_results['data_quality']
            print(f"\n数据质量:")
            print(f"  总对话数: {quality.get('total_conversations', 0)}")
            print(f"  质量分数: {quality.get('quality_score', 0):.3f}")
            print(f"  质量等级: {quality.get('quality_grade', 'Unknown')}")
        
        print("="*60)

def main():
    """主函数"""
    validator = DatasetValidator()
    results = validator.validate_all()
    
    print("\n验证完成！详细报告请查看 validation_report.json")

if __name__ == "__main__":
    main()
