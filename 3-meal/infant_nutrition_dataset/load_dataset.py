#!/usr/bin/env python3
"""
婴幼儿营养指导数据集加载器
Infant Nutrition Guidance Dataset Loader

使用示例 (Usage Example):
    from load_dataset import InfantNutritionDataset
    
    # 加载数据集
    dataset = InfantNutritionDataset()
    
    # 获取主数据集
    conversations = dataset.load_main_dataset()
    
    # 获取统计信息
    stats = dataset.get_statistics()
    
    # 按类型筛选对话
    nutrition_analysis = dataset.filter_by_type('nutrition_analysis')
"""

import json
import pandas as pd
from pathlib import Path
from typing import Dict, List, Optional, Union
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class InfantNutritionDataset:
    """婴幼儿营养指导数据集加载器"""
    
    def __init__(self, dataset_path: str = "."):
        """
        初始化数据集加载器
        
        Args:
            dataset_path: 数据集文件夹路径
        """
        self.dataset_path = Path(dataset_path)
        self.conversations = None
        self.dataset_info = None
        
        # 加载数据集信息
        self._load_dataset_info()
    
    def _load_dataset_info(self):
        """加载数据集信息"""
        info_file = self.dataset_path / "dataset_info.json"
        if info_file.exists():
            with open(info_file, 'r', encoding='utf-8') as f:
                self.dataset_info = json.load(f)
        else:
            logger.warning("dataset_info.json not found")
    
    def load_main_dataset(self) -> List[Dict]:
        """
        加载主数据集
        
        Returns:
            对话列表
        """
        main_file = self.dataset_path / "efficient_dataset_quality_focused_1753096578.json"
        
        if not main_file.exists():
            raise FileNotFoundError(f"主数据集文件不存在: {main_file}")
        
        logger.info(f"加载主数据集: {main_file}")
        with open(main_file, 'r', encoding='utf-8') as f:
            self.conversations = json.load(f)
        
        logger.info(f"成功加载 {len(self.conversations)} 条对话")
        return self.conversations
    
    def load_enhanced_dataset(self) -> List[Dict]:
        """
        加载增强版数据集
        
        Returns:
            对话列表
        """
        enhanced_file = self.dataset_path / "enhanced_qwen_0_3_years_dataset.json"
        
        if not enhanced_file.exists():
            raise FileNotFoundError(f"增强版数据集文件不存在: {enhanced_file}")
        
        logger.info(f"加载增强版数据集: {enhanced_file}")
        with open(enhanced_file, 'r', encoding='utf-8') as f:
            conversations = json.load(f)
        
        logger.info(f"成功加载 {len(conversations)} 条对话")
        return conversations
    
    def load_demo_dataset(self) -> List[Dict]:
        """
        加载演示数据集
        
        Returns:
            对话列表
        """
        demo_file = self.dataset_path / "qwen_0_3_years_demo_dataset.json"
        
        if not demo_file.exists():
            raise FileNotFoundError(f"演示数据集文件不存在: {demo_file}")
        
        logger.info(f"加载演示数据集: {demo_file}")
        with open(demo_file, 'r', encoding='utf-8') as f:
            conversations = json.load(f)
        
        logger.info(f"成功加载 {len(conversations)} 条对话")
        return conversations
    
    def load_knowledge_base(self, filename: str) -> Dict:
        """
        加载知识库文件
        
        Args:
            filename: 知识库文件名
            
        Returns:
            知识库内容
        """
        kb_file = self.dataset_path / filename
        
        if not kb_file.exists():
            raise FileNotFoundError(f"知识库文件不存在: {kb_file}")
        
        logger.info(f"加载知识库: {kb_file}")
        with open(kb_file, 'r', encoding='utf-8') as f:
            knowledge = json.load(f)
        
        return knowledge
    
    def get_statistics(self) -> Dict:
        """
        获取数据集统计信息
        
        Returns:
            统计信息字典
        """
        if self.conversations is None:
            self.load_main_dataset()
        
        # 基本统计
        total_conversations = len(self.conversations)
        
        # 按类型统计
        type_counts = {}
        age_counts = {}
        country_counts = {}
        
        for conv in self.conversations:
            metadata = conv['metadata']
            
            # 对话类型统计
            conv_type = metadata.get('type', 'unknown')
            type_counts[conv_type] = type_counts.get(conv_type, 0) + 1
            
            # 年龄段统计
            age_group = metadata.get('age_group', 'unknown')
            age_counts[age_group] = age_counts.get(age_group, 0) + 1
            
            # 国家统计
            country = metadata.get('country', 'unknown')
            country_counts[country] = country_counts.get(country, 0) + 1
        
        return {
            'total_conversations': total_conversations,
            'conversation_types': type_counts,
            'age_groups': age_counts,
            'countries': country_counts,
            'unique_types': len(type_counts),
            'unique_age_groups': len(age_counts),
            'unique_countries': len(country_counts)
        }
    
    def filter_by_type(self, conversation_type: str) -> List[Dict]:
        """
        按对话类型筛选
        
        Args:
            conversation_type: 对话类型
            
        Returns:
            筛选后的对话列表
        """
        if self.conversations is None:
            self.load_main_dataset()
        
        filtered = [
            conv for conv in self.conversations 
            if conv['metadata'].get('type') == conversation_type
        ]
        
        logger.info(f"筛选类型 '{conversation_type}': {len(filtered)} 条对话")
        return filtered
    
    def filter_by_age_group(self, age_group: str) -> List[Dict]:
        """
        按年龄段筛选
        
        Args:
            age_group: 年龄段
            
        Returns:
            筛选后的对话列表
        """
        if self.conversations is None:
            self.load_main_dataset()
        
        filtered = [
            conv for conv in self.conversations 
            if conv['metadata'].get('age_group') == age_group
        ]
        
        logger.info(f"筛选年龄段 '{age_group}': {len(filtered)} 条对话")
        return filtered
    
    def filter_by_country(self, country: str) -> List[Dict]:
        """
        按国家筛选
        
        Args:
            country: 国家代码
            
        Returns:
            筛选后的对话列表
        """
        if self.conversations is None:
            self.load_main_dataset()
        
        filtered = [
            conv for conv in self.conversations 
            if conv['metadata'].get('country') == country
        ]
        
        logger.info(f"筛选国家 '{country}': {len(filtered)} 条对话")
        return filtered
    
    def to_dataframe(self, include_metadata: bool = True) -> pd.DataFrame:
        """
        转换为pandas DataFrame
        
        Args:
            include_metadata: 是否包含元数据列
            
        Returns:
            DataFrame
        """
        if self.conversations is None:
            self.load_main_dataset()
        
        data = []
        for conv in self.conversations:
            row = {
                'instruction': conv['instruction'],
                'output': conv['output']
            }
            
            if include_metadata:
                metadata = conv['metadata']
                row.update({
                    'type': metadata.get('type'),
                    'age_group': metadata.get('age_group'),
                    'stage': metadata.get('stage'),
                    'country': metadata.get('country'),
                    'original_meal_energy': metadata.get('original_meal_energy'),
                    'converted_meal_energy': metadata.get('converted_meal_energy'),
                    'conversion_ratio': metadata.get('conversion_ratio')
                })
            
            data.append(row)
        
        return pd.DataFrame(data)
    
    def export_for_training(self, output_file: str, format: str = 'jsonl'):
        """
        导出训练格式数据
        
        Args:
            output_file: 输出文件路径
            format: 输出格式 ('jsonl', 'csv', 'json')
        """
        if self.conversations is None:
            self.load_main_dataset()
        
        if format == 'jsonl':
            with open(output_file, 'w', encoding='utf-8') as f:
                for conv in self.conversations:
                    training_sample = {
                        'input': conv['instruction'],
                        'output': conv['output'],
                        'metadata': conv['metadata']
                    }
                    f.write(json.dumps(training_sample, ensure_ascii=False) + '\n')
        
        elif format == 'csv':
            df = self.to_dataframe()
            df.to_csv(output_file, index=False, encoding='utf-8')
        
        elif format == 'json':
            training_data = [
                {
                    'input': conv['instruction'],
                    'output': conv['output'],
                    'metadata': conv['metadata']
                }
                for conv in self.conversations
            ]
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(training_data, f, ensure_ascii=False, indent=2)
        
        else:
            raise ValueError(f"不支持的格式: {format}")
        
        logger.info(f"导出训练数据到: {output_file}")
    
    def print_sample(self, index: int = 0):
        """
        打印样本对话
        
        Args:
            index: 对话索引
        """
        if self.conversations is None:
            self.load_main_dataset()
        
        if index >= len(self.conversations):
            raise IndexError(f"索引超出范围: {index}")
        
        conv = self.conversations[index]
        print(f"=== 样本对话 {index} ===")
        print(f"问题: {conv['instruction']}")
        print(f"回答: {conv['output'][:200]}...")
        print(f"元数据: {conv['metadata']}")
        print("=" * 50)

def main():
    """示例使用"""
    # 初始化数据集
    dataset = InfantNutritionDataset()
    
    # 加载主数据集
    conversations = dataset.load_main_dataset()
    
    # 获取统计信息
    stats = dataset.get_statistics()
    print("数据集统计信息:")
    print(f"总对话数: {stats['total_conversations']}")
    print(f"对话类型: {stats['conversation_types']}")
    print(f"年龄段分布: {stats['age_groups']}")
    
    # 打印样本
    dataset.print_sample(0)
    
    # 按类型筛选
    nutrition_analysis = dataset.filter_by_type('nutrition_analysis')
    print(f"营养分析对话数: {len(nutrition_analysis)}")
    
    # 转换为DataFrame
    df = dataset.to_dataframe()
    print(f"DataFrame形状: {df.shape}")
    
    # 导出训练数据
    dataset.export_for_training('training_data.jsonl', 'jsonl')

if __name__ == "__main__":
    main()
