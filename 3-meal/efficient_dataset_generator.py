#!/usr/bin/env python3
"""
高效数据集生成器
结合智能采样策略和分量转换系统，高效生成高质量的婴幼儿营养数据集
"""

import pandas as pd
import json
import time
import random
from typing import Dict, List, Any, Optional
from smart_data_generation_strategy import SmartDataGenerationStrategy
from portion_conversion_system import InfantPortionConverter
from enhanced_qwen_builder import QwenConfig, EnhancedQwenBuilder

class EfficientDatasetGenerator:
    """高效数据集生成器"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.portion_converter = InfantPortionConverter()
        
        # 配置Qwen
        self.qwen_config = QwenConfig(
            api_key=api_key,
            model="qwen-plus-latest",
            temperature=0.7,
            max_tokens=1000
        )
        
        # 生成策略配置
        self.generation_strategies = {
            "balanced": {
                "description": "均衡策略 - 质量与数量平衡",
                "sample_size": 300,
                "conversations_per_meal": 3,
                "expected_output": 900
            },
            "quality_focused": {
                "description": "质量优先 - 少而精",
                "sample_size": 150,
                "conversations_per_meal": 4,
                "expected_output": 600
            },
            "quantity_focused": {
                "description": "数量优先 - 大规模生成",
                "sample_size": 500,
                "conversations_per_meal": 2,
                "expected_output": 1000
            },
            "custom": {
                "description": "自定义策略",
                "sample_size": 200,
                "conversations_per_meal": 3,
                "expected_output": 600
            }
        }
    
    def load_and_sample_data(self, strategy: str = "hybrid", target_size: int = 300) -> pd.DataFrame:
        """加载并智能采样数据"""
        print("🔄 加载NutriBench数据集...")
        
        try:
            df = pd.read_parquet('NutriBench/v2/train-00000-of-00001.parquet')
            print(f"✅ 成功加载: {len(df):,} 条数据")
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return pd.DataFrame()
        
        # 智能采样
        print(f"\n🎯 开始智能采样 (策略: {strategy}, 目标: {target_size} 条)...")
        sampler = SmartDataGenerationStrategy(df)
        selected_data = sampler.generate_smart_sample(strategy=strategy, target_size=target_size)
        
        return selected_data
    
    def generate_conversations_batch(self, meal_data_batch: List[Dict], 
                                   conversations_per_meal: int = 3) -> List[Dict[str, Any]]:
        """批量生成对话"""
        print(f"\n🤖 开始批量生成对话...")
        print(f"📊 餐食数量: {len(meal_data_batch)}")
        print(f"📊 每餐对话数: {conversations_per_meal}")
        print(f"📊 预期总对话数: {len(meal_data_batch) * conversations_per_meal}")
        
        # 创建增强版构建器
        builder = EnhancedQwenBuilder(self.qwen_config)
        
        all_conversations = []
        successful_count = 0
        failed_count = 0
        
        age_groups = ["0-6m", "6m_start", "6-9m", "9-12m", "12-24m"]
        conversation_types = [
            "nutrition_analysis", "feeding_guidance", "texture_advice", 
            "portion_guidance", "safety_advice", "problem_solving"
        ]
        
        for i, meal_data in enumerate(meal_data_batch):
            print(f"\n📝 处理餐食 {i+1}/{len(meal_data_batch)}: {meal_data['meal_description'][:50]}...")
            
            meal_conversations = 0
            attempts = 0
            max_attempts = conversations_per_meal * 2  # 允许一些失败重试
            
            while meal_conversations < conversations_per_meal and attempts < max_attempts:
                attempts += 1
                
                # 智能选择年龄组和对话类型
                age_group = self._smart_age_selection(meal_data)
                conv_type = random.choice(conversation_types)
                
                try:
                    conversation = builder.generate_enhanced_conversation(
                        meal_data, age_group, conv_type
                    )
                    
                    if conversation:
                        all_conversations.append(conversation)
                        meal_conversations += 1
                        successful_count += 1
                        print(f"  ✅ 生成对话 {meal_conversations}/{conversations_per_meal}: {conv_type} ({age_group})")
                    else:
                        failed_count += 1
                        print(f"  ❌ 生成失败: {conv_type} ({age_group})")
                    
                    # API限制延迟
                    time.sleep(1.0)
                    
                except Exception as e:
                    failed_count += 1
                    print(f"  ❌ 生成异常: {str(e)}")
                    time.sleep(2.0)  # 异常后稍长延迟
            
            if meal_conversations < conversations_per_meal:
                print(f"  ⚠️ 餐食 {i+1} 只生成了 {meal_conversations}/{conversations_per_meal} 个对话")
        
        success_rate = successful_count / (successful_count + failed_count) * 100 if (successful_count + failed_count) > 0 else 0
        
        print(f"\n🎉 批量生成完成!")
        print(f"📊 成功生成: {successful_count} 个对话")
        print(f"📊 失败次数: {failed_count}")
        print(f"📊 成功率: {success_rate:.1f}%")
        
        return all_conversations
    
    def _smart_age_selection(self, meal_data: Dict) -> str:
        """智能选择年龄组"""
        energy = meal_data.get('energy', 0)
        protein = meal_data.get('protein', 0)
        
        # 基于营养特征智能选择年龄组
        if energy < 100 and protein < 5:
            # 低能量低蛋白，适合早期辅食
            return random.choice(["6m_start", "6-9m"])
        elif energy > 400 or protein > 25:
            # 高能量高蛋白，适合较大婴幼儿
            return random.choice(["12-24m"])
        elif 200 <= energy <= 400 and 10 <= protein <= 20:
            # 中等营养，适合中期辅食
            return random.choice(["9-12m", "12-24m"])
        else:
            # 随机选择
            return random.choice(["6m_start", "6-9m", "9-12m", "12-24m"])
    
    def generate_efficient_dataset(self, strategy_name: str = "balanced") -> Dict[str, Any]:
        """高效生成数据集"""
        print("🚀 高效数据集生成器启动")
        print("=" * 70)
        
        if strategy_name not in self.generation_strategies:
            print(f"❌ 不支持的策略: {strategy_name}")
            return {}
        
        strategy = self.generation_strategies[strategy_name]
        print(f"📋 选择策略: {strategy['description']}")
        print(f"📊 采样目标: {strategy['sample_size']} 条餐食")
        print(f"📊 每餐对话: {strategy['conversations_per_meal']} 个")
        print(f"📊 预期输出: {strategy['expected_output']} 条对话")
        
        # 第一步：智能采样
        print(f"\n{'='*70}")
        print("第一步：智能数据采样")
        print(f"{'='*70}")
        
        selected_data = self.load_and_sample_data(
            strategy="hybrid", 
            target_size=strategy['sample_size']
        )
        
        if selected_data.empty:
            print("❌ 数据采样失败")
            return {}
        
        # 第二步：批量生成对话
        print(f"\n{'='*70}")
        print("第二步：批量生成对话")
        print(f"{'='*70}")
        
        meal_data_list = selected_data.to_dict('records')
        conversations = self.generate_conversations_batch(
            meal_data_list, 
            strategy['conversations_per_meal']
        )
        
        # 第三步：保存结果
        print(f"\n{'='*70}")
        print("第三步：保存生成结果")
        print(f"{'='*70}")
        
        timestamp = int(time.time())
        dataset_filename = f"efficient_dataset_{strategy_name}_{timestamp}.json"
        sample_filename = f"selected_samples_{strategy_name}_{timestamp}.parquet"
        
        # 保存对话数据集
        with open(dataset_filename, 'w', encoding='utf-8') as f:
            json.dump(conversations, f, ensure_ascii=False, indent=2)
        
        # 保存采样数据
        selected_data.to_parquet(sample_filename)
        
        # 生成统计报告
        stats = self._generate_statistics(conversations, selected_data, strategy)
        stats_filename = f"generation_stats_{strategy_name}_{timestamp}.json"
        
        with open(stats_filename, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"💾 对话数据集: {dataset_filename}")
        print(f"💾 采样数据: {sample_filename}")
        print(f"💾 统计报告: {stats_filename}")
        
        # 显示最终统计
        print(f"\n🎉 高效生成完成!")
        print(f"📊 实际生成对话: {len(conversations)} 条")
        print(f"📊 目标完成率: {len(conversations)/strategy['expected_output']*100:.1f}%")
        print(f"📊 数据利用率: {len(selected_data)/15617*100:.3f}%")
        
        return {
            "conversations": conversations,
            "selected_samples": selected_data,
            "statistics": stats,
            "files": {
                "dataset": dataset_filename,
                "samples": sample_filename,
                "stats": stats_filename
            }
        }
    
    def _generate_statistics(self, conversations: List[Dict], 
                           selected_data: pd.DataFrame, 
                           strategy: Dict) -> Dict[str, Any]:
        """生成详细统计"""
        if not conversations:
            return {}
        
        # 对话类型统计
        type_counts = {}
        age_counts = {}
        country_counts = {}
        
        for conv in conversations:
            metadata = conv['metadata']
            
            conv_type = metadata['type']
            type_counts[conv_type] = type_counts.get(conv_type, 0) + 1
            
            age_group = metadata['age_group']
            age_counts[age_group] = age_counts.get(age_group, 0) + 1
            
            country = metadata.get('country', 'Unknown')
            country_counts[country] = country_counts.get(country, 0) + 1
        
        # 营养数据统计
        nutrition_stats = {
            "energy": {
                "min": float(selected_data['energy'].min()),
                "max": float(selected_data['energy'].max()),
                "mean": float(selected_data['energy'].mean()),
                "std": float(selected_data['energy'].std())
            },
            "protein": {
                "min": float(selected_data['protein'].min()),
                "max": float(selected_data['protein'].max()),
                "mean": float(selected_data['protein'].mean()),
                "std": float(selected_data['protein'].std())
            }
        }
        
        return {
            "generation_summary": {
                "strategy": strategy['description'],
                "target_samples": strategy['sample_size'],
                "actual_samples": len(selected_data),
                "target_conversations": strategy['expected_output'],
                "actual_conversations": len(conversations),
                "completion_rate": len(conversations) / strategy['expected_output'],
                "efficiency_score": len(conversations) / len(selected_data)
            },
            "conversation_distribution": {
                "by_type": type_counts,
                "by_age_group": age_counts,
                "by_country": country_counts
            },
            "data_quality": {
                "countries_covered": selected_data['country'].nunique(),
                "nutrition_diversity": nutrition_stats,
                "avg_description_length": float(selected_data['meal_description'].str.len().mean())
            },
            "generation_metadata": {
                "model": "qwen-plus",
                "portion_conversion_enabled": True,
                "official_guidelines_based": True,
                "generation_timestamp": int(time.time())
            }
        }

def main():
    """主函数"""
    print("⚡ 高效数据集生成器")
    print("=" * 70)
    
    # API密钥配置
    api_key = "sk-5eba46fbcff649d5bf28313bc865de10"  # 您的API密钥
    
    if not api_key:
        print("❌ 请配置API密钥")
        return
    
    # 创建生成器
    generator = EfficientDatasetGenerator(api_key)
    
    # 显示可用策略
    print("📋 可用生成策略:")
    for name, strategy in generator.generation_strategies.items():
        print(f"  {name}: {strategy['description']}")
        print(f"    - 采样: {strategy['sample_size']} 条")
        print(f"    - 每餐对话: {strategy['conversations_per_meal']} 个")
        print(f"    - 预期输出: {strategy['expected_output']} 条")
        print()
    
    # 选择策略
    strategy_choice = input("请选择生成策略 (balanced/quality_focused/quantity_focused/custom): ").strip()
    if not strategy_choice:
        strategy_choice = "balanced"
    
    if strategy_choice not in generator.generation_strategies:
        print(f"❌ 无效策略，使用默认策略: balanced")
        strategy_choice = "balanced"
    
    # 开始生成
    print(f"\n🚀 开始使用策略: {strategy_choice}")
    result = generator.generate_efficient_dataset(strategy_choice)
    
    if result:
        print(f"\n✅ 生成成功!")
        print(f"📁 生成文件:")
        for file_type, filename in result['files'].items():
            print(f"  {file_type}: {filename}")
    else:
        print(f"\n❌ 生成失败")

if __name__ == "__main__":
    main()
