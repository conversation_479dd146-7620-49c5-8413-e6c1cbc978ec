#!/usr/bin/env python3
"""
营养指导LLM提示模板库
包含经过优化的提示模板，确保生成高质量的营养指导对话
"""

from typing import Dict, List, Any
from dataclasses import dataclass
import json

@dataclass
class PromptTemplate:
    """提示模板数据类"""
    name: str
    system_prompt: str
    user_template: str
    example_output: str
    quality_criteria: List[str]

class NutritionPromptLibrary:
    """营养指导提示模板库"""
    
    def __init__(self):
        self.templates = self._initialize_templates()
        self.age_specific_guidelines = self._load_age_guidelines()
        self.safety_guidelines = self._load_safety_guidelines()
    
    def _initialize_templates(self) -> Dict[str, PromptTemplate]:
        """初始化提示模板"""
        templates = {}
        
        # 营养分析模板
        templates["nutrition_analysis"] = PromptTemplate(
            name="营养成分分析",
            system_prompt="""你是一位资深的儿童营养师，拥有15年的临床经验。你的专业背景包括：

🎓 专业资质：
- 注册营养师资格
- 儿童营养学硕士学位
- 熟悉中国居民膳食指南(2022)
- 掌握WHO婴幼儿喂养建议

📋 工作原则：
1. 基于科学证据提供建议
2. 考虑中国婴幼儿体质特点
3. 注重实用性和可操作性
4. 强调食品安全和营养均衡
5. 语言温和、专业、易懂

⚠️ 重要提醒：
- 你的建议不能替代医疗诊断
- 遇到严重营养问题应建议就医
- 始终考虑个体差异
- 强调循序渐进的喂养原则

请以专业、亲切的语气回答家长的营养咨询问题。""",
            
            user_template="""我需要您帮我分析一下这个餐食的营养价值：

👶 宝宝信息：
- 年龄：{age_group}
- 发育阶段：{development_stage}

🍽️ 餐食信息：
- 描述：{meal_description}
- 碳水化合物：{carb}克
- 蛋白质：{protein}克
- 脂肪：{fat}克
- 总能量：{energy}千卡
- 来源国家：{country}

❓ 我的问题：
这个餐食对我的宝宝来说营养价值如何？是否适合这个年龄段？还需要注意什么？

请从以下几个方面给我专业的分析：
1. 营养成分评估
2. 年龄适宜性分析
3. 营养建议
4. 安全注意事项""",
            
            example_output="""根据您提供的信息，我来为您详细分析这个餐食的营养价值：

📊 **营养成分评估**
这个餐食的营养构成为：
- 碳水化合物：提供主要能量来源，含量适中
- 蛋白质：支持生长发育，含量{protein_assessment}
- 脂肪：促进大脑发育，含量{fat_assessment}
- 总能量：{energy_assessment}

📈 **年龄适宜性分析**
对于{age_group}的宝宝：
- 能量占日需求的{energy_percentage}%，{energy_suitability}
- 营养密度{nutrition_density}，符合该年龄段特点
- 食物质地和消化难度{texture_suitability}

💡 **营养建议**
{nutrition_recommendations}

⚠️ **安全注意事项**
{safety_precautions}

🌟 **总体评价**
{overall_assessment}""",
            
            quality_criteria=[
                "包含具体的营养数据分析",
                "提供年龄适宜性评估",
                "给出实用的营养建议",
                "包含必要的安全提醒",
                "语言专业且易懂",
                "结构清晰，使用emoji增强可读性"
            ]
        )
        
        # 饮食多样性监测模板
        templates["diversity_monitoring"] = PromptTemplate(
            name="饮食多样性监测",
            system_prompt="""你是一位专注于儿童营养多样性的专家，具有以下专业背景：

🔬 专业领域：
- 儿童膳食多样性评估
- 营养缺乏症预防
- 食物过敏管理
- 文化饮食适应

🎯 评估重点：
1. 食物种类多样性
2. 营养素平衡性
3. 膳食结构合理性
4. 文化饮食融合
5. 季节性食材利用

📝 评估方法：
- 使用食物多样性评分系统
- 参考中国居民平衡膳食宝塔
- 考虑地域饮食特色
- 关注营养素密度

请帮助家长评估和改善宝宝的饮食多样性。""",
            
            user_template="""请帮我评估这个餐食的多样性价值：

👶 宝宝年龄：{age_group}

🍽️ 今日餐食：{meal_description}
营养成分：能量{energy}千卡，蛋白质{protein}克，脂肪{fat}克，碳水{carb}克

🤔 我关心的问题：
1. 这个餐食在营养多样性方面表现如何？
2. 涵盖了哪些食物类别？
3. 还缺少哪些营养成分？
4. 下一餐应该如何搭配？
5. 一周内如何安排更均衡？""",
            
            example_output="""让我为您全面评估这个餐食的多样性价值：

🌈 **食物多样性分析**
{diversity_score}

📊 **营养类别覆盖**
{food_groups_coverage}

⚖️ **营养平衡评估**
{nutrition_balance}

🎯 **改进建议**
{improvement_suggestions}

📅 **一周搭配规划**
{weekly_planning}""",
            
            quality_criteria=[
                "提供具体的多样性评分",
                "分析食物类别覆盖情况",
                "给出平衡膳食建议",
                "提供实用的搭配方案",
                "考虑季节和地域特色"
            ]
        )
        
        # 健康指导模板
        templates["health_guidance"] = PromptTemplate(
            name="个性化健康指导",
            system_prompt="""你是一位全科儿童营养顾问，专门提供个性化的健康指导：

👨‍⚕️ 专业能力：
- 个体化营养评估
- 生长发育监测
- 营养干预方案设计
- 家庭喂养指导

🔍 评估维度：
1. 体格发育状况
2. 营养摄入充足性
3. 消化吸收能力
4. 食物偏好特点
5. 家庭饮食环境

💊 干预策略：
- 营养补充建议
- 膳食结构调整
- 喂养方式优化
- 生活习惯培养

请根据宝宝的具体情况提供个性化的健康指导。""",
            
            user_template="""请为我的宝宝提供个性化的营养指导：

👶 宝宝基本信息：
- 年龄：{age_group}
- 体重：{weight}kg
- 身高：{height}cm（如有）
- 特殊情况：{special_conditions}

🍽️ 当前饮食：
{meal_description}
营养成分：{nutrition_info}

❓ 我的担心：
{parent_concerns}

请帮我分析：
1. 宝宝的营养状况如何？
2. 当前饮食是否充足？
3. 需要补充什么营养？
4. 如何调整饮食计划？""",
            
            example_output="""基于您提供的信息，我来为宝宝制定个性化的营养指导方案：

👶 **宝宝营养状况评估**
{nutrition_status}

📊 **当前饮食分析**
{current_diet_analysis}

🎯 **营养缺口识别**
{nutrition_gaps}

💡 **个性化建议**
{personalized_recommendations}

📋 **行动计划**
{action_plan}

📞 **随访建议**
{follow_up_suggestions}""",
            
            quality_criteria=[
                "提供个性化的营养评估",
                "识别具体的营养缺口",
                "给出可操作的改进方案",
                "包含随访和监测建议",
                "考虑家庭实际情况"
            ]
        )
        
        return templates
    
    def _load_age_guidelines(self) -> Dict[str, Dict]:
        """加载年龄段特定指导原则"""
        return {
            "6-12m": {
                "key_principles": [
                    "继续母乳喂养",
                    "及时添加辅食",
                    "食物从细到粗",
                    "口味清淡无盐",
                    "观察过敏反应"
                ],
                "nutrition_focus": ["铁", "锌", "维生素D"],
                "texture_progression": "泥状→碎末状→小颗粒",
                "feeding_frequency": "每日5-6次"
            },
            "1-3y": {
                "key_principles": [
                    "食物多样化",
                    "培养自主进食",
                    "规律用餐时间",
                    "适量控制零食",
                    "鼓励尝试新食物"
                ],
                "nutrition_focus": ["钙", "铁", "优质蛋白"],
                "texture_progression": "接近成人食物",
                "feeding_frequency": "三餐两点"
            },
            "3-6y": {
                "key_principles": [
                    "均衡膳食结构",
                    "培养良好习惯",
                    "控制糖盐摄入",
                    "增加户外活动",
                    "家庭共餐"
                ],
                "nutrition_focus": ["全面均衡", "维生素", "膳食纤维"],
                "texture_progression": "成人食物",
                "feeding_frequency": "三餐一点"
            }
        }
    
    def _load_safety_guidelines(self) -> Dict[str, List[str]]:
        """加载食品安全指导原则"""
        return {
            "choking_hazards": [
                "避免整颗坚果、硬糖",
                "切小葡萄、樱桃番茄",
                "去除鱼刺、骨头",
                "适当的食物质地"
            ],
            "allergy_prevention": [
                "逐一引入新食物",
                "观察过敏反应",
                "记录食物日记",
                "不延迟致敏食物"
            ],
            "food_hygiene": [
                "确保食材新鲜",
                "充分清洗处理",
                "适宜的储存温度",
                "餐具清洁消毒"
            ]
        }
    
    def get_template(self, template_name: str) -> PromptTemplate:
        """获取指定的提示模板"""
        return self.templates.get(template_name)
    
    def list_templates(self) -> List[str]:
        """列出所有可用的模板"""
        return list(self.templates.keys())
    
    def validate_output(self, template_name: str, output: str) -> Dict[str, Any]:
        """验证LLM输出质量"""
        template = self.templates.get(template_name)
        if not template:
            return {"valid": False, "error": "模板不存在"}
        
        validation_results = {
            "valid": True,
            "score": 0,
            "criteria_met": [],
            "criteria_failed": [],
            "suggestions": []
        }
        
        # 检查质量标准
        for criterion in template.quality_criteria:
            if self._check_criterion(output, criterion):
                validation_results["criteria_met"].append(criterion)
                validation_results["score"] += 1
            else:
                validation_results["criteria_failed"].append(criterion)
        
        # 计算总分
        total_criteria = len(template.quality_criteria)
        validation_results["score"] = validation_results["score"] / total_criteria * 100
        
        # 判断是否合格
        validation_results["valid"] = validation_results["score"] >= 70
        
        return validation_results
    
    def _check_criterion(self, output: str, criterion: str) -> bool:
        """检查输出是否满足特定标准"""
        # 简化的标准检查逻辑
        criterion_keywords = {
            "包含具体的营养数据分析": ["营养", "分析", "克", "千卡"],
            "提供年龄适宜性评估": ["年龄", "适合", "发育", "阶段"],
            "给出实用的营养建议": ["建议", "推荐", "可以", "应该"],
            "包含必要的安全提醒": ["注意", "安全", "避免", "小心"],
            "语言专业且易懂": ["专业", "营养师", "建议"],
            "结构清晰，使用emoji增强可读性": ["📊", "💡", "⚠️", "🌟"]
        }
        
        keywords = criterion_keywords.get(criterion, [])
        return any(keyword in output for keyword in keywords)

def main():
    """主函数示例"""
    library = NutritionPromptLibrary()
    
    print("=== 营养指导提示模板库 ===")
    print(f"可用模板: {library.list_templates()}")
    
    # 示例：获取营养分析模板
    template = library.get_template("nutrition_analysis")
    if template:
        print(f"\n模板名称: {template.name}")
        print(f"质量标准: {template.quality_criteria}")

if __name__ == "__main__":
    main()
