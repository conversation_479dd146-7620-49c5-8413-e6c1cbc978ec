#!/usr/bin/env python3
"""
测试NutriBench数据加载和处理
"""

import pandas as pd
import json
import os
from typing import Dict, List, Any

def load_nutribench_data(data_path: str = "NutriBench") -> pd.DataFrame:
    """加载NutriBench数据集"""
    print("🔄 加载NutriBench数据集...")
    
    try:
        # 尝试加载v2版本（推荐）
        v2_path = f"{data_path}/v2/train-00000-of-00001.parquet"
        if os.path.exists(v2_path):
            file_size = os.path.getsize(v2_path)
            print(f"📁 v2文件大小: {file_size:,} bytes")
            
            if file_size > 1000:  # 确保不是LFS指针文件
                df = pd.read_parquet(v2_path)
                print(f"✅ 成功加载v2数据: {len(df)} 条记录")
                print(f"📊 数据形状: {df.shape}")
                print(f"📋 列名: {list(df.columns)}")
                
                if 'country' in df.columns:
                    print(f"🌍 覆盖国家: {df['country'].nunique()} 个")
                    country_counts = df['country'].value_counts().head(5)
                    print(f"🏆 主要国家: {dict(country_counts)}")
                
                return df
            else:
                print("❌ v2文件太小，可能是LFS指针文件")
    except Exception as e:
        print(f"❌ v2数据加载失败: {e}")
    
    try:
        # 尝试加载v1版本
        print("🔄 尝试加载v1数据...")
        v1_files = [
            f"{data_path}/v1/wweia_meal_natural-00000-of-00001.parquet",
            f"{data_path}/v1/wweia_meal_metric-00000-of-00001.parquet",
            f"{data_path}/v1/who_meal_natural-00000-of-00001.parquet",
            f"{data_path}/v1/who_meal_metric-00000-of-00001.parquet"
        ]
        
        all_data = []
        for file_path in v1_files:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                if file_size > 1000:  # 确保不是LFS指针文件
                    try:
                        df_part = pd.read_parquet(file_path)
                        df_part['source'] = os.path.basename(file_path).replace('-00000-of-00001.parquet', '')
                        all_data.append(df_part)
                        print(f"✅ 加载 {os.path.basename(file_path)}: {len(df_part)} 条")
                    except Exception as e:
                        print(f"❌ 跳过 {file_path}: {e}")
                else:
                    print(f"⚠️ 跳过小文件 {file_path}: {file_size} bytes")
        
        if all_data:
            df = pd.concat(all_data, ignore_index=True)
            print(f"✅ 成功合并v1数据: {len(df)} 条记录")
            return df
    except Exception as e:
        print(f"❌ v1数据加载失败: {e}")
    
    # 如果都失败了，返回None
    print("❌ 无法加载任何NutriBench数据")
    return None

def analyze_data(df: pd.DataFrame):
    """分析数据集"""
    print(f"\n📊 数据分析:")
    print(f"  总样本数: {len(df)}")
    print(f"  列数: {len(df.columns)}")
    
    # 检查必要的列
    required_cols = ['meal_description', 'carb', 'protein', 'fat', 'energy']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        print(f"⚠️ 缺少列: {missing_cols}")
    else:
        print(f"✅ 包含所有必要的营养列")
    
    # 营养成分统计
    if all(col in df.columns for col in ['carb', 'protein', 'fat', 'energy']):
        print(f"\n🥗 营养成分统计:")
        for nutrient in ['carb', 'protein', 'fat', 'energy']:
            values = df[nutrient]
            print(f"  {nutrient}: {values.min():.1f} - {values.max():.1f} (平均: {values.mean():.1f})")
    
    # 国家分布
    if 'country' in df.columns:
        print(f"\n🌍 国家分布:")
        country_counts = df['country'].value_counts()
        for country, count in country_counts.head(10).items():
            percentage = count / len(df) * 100
            print(f"  {country}: {count} 条 ({percentage:.1f}%)")
    
    # 显示样本
    print(f"\n📖 数据样本:")
    for i in range(min(3, len(df))):
        row = df.iloc[i]
        print(f"  {i+1}. {row['meal_description'][:80]}...")
        if all(col in row for col in ['carb', 'protein', 'fat', 'energy']):
            print(f"     营养: 碳水{row['carb']}g, 蛋白质{row['protein']}g, 脂肪{row['fat']}g, 能量{row['energy']}kcal")
        if 'country' in row:
            print(f"     国家: {row['country']}")
        print()

def test_data_generation(df: pd.DataFrame, sample_size: int = 5):
    """测试数据生成功能"""
    print(f"\n🧪 测试数据生成功能...")
    
    # 随机采样
    sample_df = df.sample(n=min(sample_size, len(df)), random_state=42)
    print(f"📝 选择了 {len(sample_df)} 个样本进行测试")
    
    # 模拟生成对话数据
    generated_conversations = []
    
    for i, (_, row) in enumerate(sample_df.iterrows()):
        meal_data = row.to_dict()
        
        # 生成简单的问答对
        conversation = {
            "instruction": f"请分析这个餐食的营养价值：{meal_data['meal_description']}",
            "output": f"这个餐食含有碳水化合物{meal_data['carb']}克、蛋白质{meal_data['protein']}克、脂肪{meal_data['fat']}克，总能量{meal_data['energy']}千卡。",
            "metadata": {
                "type": "nutrition_analysis",
                "age_group": "1-3y",
                "country": meal_data.get('country', 'Unknown'),
                "source": "nutribench_test"
            }
        }
        
        generated_conversations.append(conversation)
    
    # 保存测试结果
    output_file = 'nutribench_test_conversations.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(generated_conversations, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 生成了 {len(generated_conversations)} 条测试对话")
    print(f"💾 保存到: {output_file}")
    
    # 显示示例
    print(f"\n📖 生成的对话示例:")
    if generated_conversations:
        example = generated_conversations[0]
        print(f"问题: {example['instruction']}")
        print(f"回答: {example['output']}")
        print(f"元数据: {example['metadata']}")

def main():
    """主函数"""
    print("=" * 60)
    print("🧪 NutriBench数据加载和处理测试")
    print("=" * 60)
    
    # 加载数据
    df = load_nutribench_data()
    
    if df is not None:
        # 分析数据
        analyze_data(df)
        
        # 测试数据生成
        test_data_generation(df)
        
        print(f"\n✅ 测试完成！数据集可以正常使用。")
        print(f"📊 数据集包含 {len(df)} 条记录，可用于LLM训练数据生成。")
    else:
        print(f"\n❌ 测试失败！请检查NutriBench文件是否正确下载。")
        print(f"💡 建议:")
        print(f"  1. 确认文件路径正确")
        print(f"  2. 检查文件是否完整下载（不是LFS指针）")
        print(f"  3. 尝试重新下载数据集")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
