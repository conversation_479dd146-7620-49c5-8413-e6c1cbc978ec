#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿社会情绪发展指导数据集分析工具
提供数据集质量评估、统计分析和可视化功能
"""

import json
from collections import Counter, defaultdict
from typing import Dict, List, Any
from datetime import datetime

try:
    import matplotlib.pyplot as plt
    import numpy as np
    HAS_PLOTTING = True
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
except ImportError:
    HAS_PLOTTING = False
    print("警告: matplotlib未安装，将跳过可视化功能")

class InfantDatasetAnalyzer:
    def __init__(self, dataset_path: str):
        with open(dataset_path, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        self.age_mapping = {
            "0-1m": "新生儿期(0-1月)",
            "1-6m": "婴儿早期(1-6月)", 
            "6-12m": "婴儿晚期(6-12月)",
            "12-18m": "幼儿早期(12-18月)",
            "18-24m": "幼儿中期(18-24月)",
            "24-36m": "幼儿晚期(24-36月)"
        }
        
        self.domain_mapping = {
            "emotion": "情绪发展",
            "social": "社会交往",
            "cognitive": "认知发展", 
            "motor": "运动发展",
            "language": "语言发展"
        }
        
        self.scenario_mapping = {
            "daily_care": "日常护理",
            "milestone": "发育里程碑",
            "problem_behavior": "问题行为",
            "assessment": "发育评估"
        }

    def basic_statistics(self) -> Dict:
        """基础统计信息"""
        stats = {
            "total_samples": len(self.data),
            "age_distribution": Counter(item["age_range"] for item in self.data),
            "domain_distribution": Counter(item["development_domain"] for item in self.data),
            "scenario_distribution": Counter(item["scenario_type"] for item in self.data),
            "setting_distribution": Counter(item["context"]["setting"] for item in self.data)
        }
        
        # 计算对话轮数统计
        dialogue_lengths = [len(item["guidance_dialogue"]) for item in self.data]
        stats["dialogue_stats"] = {
            "mean_length": sum(dialogue_lengths) / len(dialogue_lengths),
            "median_length": sorted(dialogue_lengths)[len(dialogue_lengths)//2],
            "min_length": min(dialogue_lengths),
            "max_length": max(dialogue_lengths)
        }
        
        return stats

    def content_analysis(self) -> Dict:
        """内容质量分析"""
        analysis = {
            "behavior_diversity": self._analyze_behavior_diversity(),
            "guidance_quality": self._analyze_guidance_quality(),
            "educational_completeness": self._analyze_educational_completeness()
        }
        
        return analysis

    def _analyze_behavior_diversity(self) -> Dict:
        """分析行为描述的多样性"""
        all_behaviors = []
        behavior_by_age = defaultdict(list)
        
        for item in self.data:
            behaviors = item["infant_behavior"]["observable_signs"]
            all_behaviors.extend(behaviors)
            behavior_by_age[item["age_range"]].extend(behaviors)
        
        return {
            "total_unique_behaviors": len(set(all_behaviors)),
            "most_common_behaviors": Counter(all_behaviors).most_common(10),
            "behaviors_by_age": {age: len(set(behaviors)) for age, behaviors in behavior_by_age.items()}
        }

    def _analyze_guidance_quality(self) -> Dict:
        """分析指导质量"""
        guidance_types = []
        guidance_lengths = []
        
        for item in self.data:
            for dialogue in item["guidance_dialogue"]:
                guidance_types.append(dialogue["guidance_type"])
                guidance_lengths.append(len(dialogue["content"]))
        
        return {
            "guidance_type_distribution": Counter(guidance_types),
            "average_guidance_length": sum(guidance_lengths) / len(guidance_lengths),
            "guidance_length_by_type": {
                gtype: sum([length for gtype2, length in zip(guidance_types, guidance_lengths) if gtype2 == gtype]) /
                       len([length for gtype2, length in zip(guidance_types, guidance_lengths) if gtype2 == gtype])
                for gtype in set(guidance_types)
            }
        }

    def _analyze_educational_completeness(self) -> Dict:
        """分析教育内容完整性"""
        has_milestones = sum(1 for item in self.data if "developmental_context" in item)
        has_assessment = sum(1 for item in self.data if "assessment_dimensions" in item)
        has_parent_education = sum(1 for item in self.data if "parent_education" in item)
        
        return {
            "samples_with_milestones": has_milestones,
            "samples_with_assessment": has_assessment,
            "samples_with_parent_education": has_parent_education,
            "completeness_rate": {
                "milestones": has_milestones / len(self.data),
                "assessment": has_assessment / len(self.data),
                "parent_education": has_parent_education / len(self.data)
            }
        }

    def generate_report(self, output_path: str = "dataset_analysis_report.md"):
        """生成分析报告"""
        stats = self.basic_statistics()
        content = self.content_analysis()
        
        report = f"""# 婴幼儿社会情绪发展指导数据集分析报告

## 1. 基础统计信息

### 1.1 数据集规模
- **总样本数**: {stats['total_samples']}
- **平均对话轮数**: {stats['dialogue_stats']['mean_length']:.1f}
- **对话轮数范围**: {stats['dialogue_stats']['min_length']} - {stats['dialogue_stats']['max_length']}

### 1.2 年龄分布
"""
        
        for age, count in stats['age_distribution'].items():
            percentage = count / stats['total_samples'] * 100
            report += f"- **{self.age_mapping[age]}**: {count} ({percentage:.1f}%)\n"
        
        report += f"""
### 1.3 发展领域分布
"""
        
        for domain, count in stats['domain_distribution'].items():
            percentage = count / stats['total_samples'] * 100
            report += f"- **{self.domain_mapping[domain]}**: {count} ({percentage:.1f}%)\n"
        
        report += f"""
### 1.4 场景类型分布
"""
        
        for scenario, count in stats['scenario_distribution'].items():
            percentage = count / stats['total_samples'] * 100
            report += f"- **{self.scenario_mapping[scenario]}**: {count} ({percentage:.1f}%)\n"
        
        report += f"""
## 2. 内容质量分析

### 2.1 行为描述多样性
- **独特行为描述总数**: {content['behavior_diversity']['total_unique_behaviors']}
- **最常见行为** (前5个):
"""
        
        for behavior, count in content['behavior_diversity']['most_common_behaviors'][:5]:
            report += f"  - {behavior}: {count}次\n"
        
        report += f"""
### 2.2 指导质量分析
- **平均指导内容长度**: {content['guidance_quality']['average_guidance_length']:.1f}字符
- **指导类型分布**:
"""
        
        for gtype, count in content['guidance_quality']['guidance_type_distribution'].items():
            report += f"  - {gtype}: {count}次\n"
        
        report += f"""
### 2.3 教育内容完整性
- **包含发育里程碑**: {content['educational_completeness']['samples_with_milestones']} ({content['educational_completeness']['completeness_rate']['milestones']:.1%})
- **包含评估维度**: {content['educational_completeness']['samples_with_assessment']} ({content['educational_completeness']['completeness_rate']['assessment']:.1%})
- **包含家长教育**: {content['educational_completeness']['samples_with_parent_education']} ({content['educational_completeness']['completeness_rate']['parent_education']:.1%})

## 3. 数据集特点

### 3.1 优势
1. **年龄覆盖全面**: 涵盖0-36个月所有关键发育阶段
2. **领域分布均衡**: 五大发展领域都有充分覆盖
3. **场景类型丰富**: 包含日常护理、里程碑、问题行为等多种场景
4. **专业性强**: 融入了发育心理学和早期干预的专业知识

### 3.2 应用价值
1. **LLM训练**: 可用于训练专业的婴幼儿发育指导模型
2. **家长教育**: 为家长提供科学的育儿指导
3. **专业培训**: 支持早教工作者和医护人员的能力建设
4. **研究支撑**: 为相关学术研究提供高质量数据基础

### 3.3 改进建议
1. **增加真实案例**: 结合临床实践中的真实案例
2. **多模态扩展**: 考虑加入图像、视频等多模态数据
3. **个性化定制**: 根据不同文化背景和家庭情况进行定制
4. **持续更新**: 根据最新研究成果持续更新内容

## 4. 质量保证

### 4.1 数据来源
- 基于权威的PEC（Parental Emotion Coaching）理论框架
- 参考ASQ:SE-2等国际标准化评估工具
- 融合《婴幼儿早期发展服务指南》等官方文件

### 4.2 专业审核
- 建议邀请儿童发展专家进行内容审核
- 进行临床验证和效果评估
- 建立持续的质量监控机制

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
"""
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report)
        
        print(f"分析报告已保存到: {output_path}")
        return report

    def create_visualizations(self, output_dir: str = "./"):
        """创建可视化图表"""
        if not HAS_PLOTTING:
            print("跳过可视化：matplotlib未安装")
            return

        stats = self.basic_statistics()

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('婴幼儿社会情绪发展指导数据集分析', fontsize=16, fontweight='bold')

        # 年龄分布
        ages = [self.age_mapping[age] for age in stats['age_distribution'].keys()]
        counts = list(stats['age_distribution'].values())
        axes[0, 0].bar(ages, counts, color='skyblue')
        axes[0, 0].set_title('年龄分布')
        axes[0, 0].set_ylabel('样本数量')
        axes[0, 0].tick_params(axis='x', rotation=45)

        # 发展领域分布
        domains = [self.domain_mapping[domain] for domain in stats['domain_distribution'].keys()]
        domain_counts = list(stats['domain_distribution'].values())
        axes[0, 1].pie(domain_counts, labels=domains, autopct='%1.1f%%', startangle=90)
        axes[0, 1].set_title('发展领域分布')

        # 场景类型分布
        scenarios = [self.scenario_mapping[scenario] for scenario in stats['scenario_distribution'].keys()]
        scenario_counts = list(stats['scenario_distribution'].values())
        axes[1, 0].bar(scenarios, scenario_counts, color='lightgreen')
        axes[1, 0].set_title('场景类型分布')
        axes[1, 0].set_ylabel('样本数量')
        axes[1, 0].tick_params(axis='x', rotation=45)

        # 对话轮数分布
        dialogue_lengths = [len(item["guidance_dialogue"]) for item in self.data]
        axes[1, 1].hist(dialogue_lengths, bins=10, color='orange', alpha=0.7)
        axes[1, 1].set_title('对话轮数分布')
        axes[1, 1].set_xlabel('对话轮数')
        axes[1, 1].set_ylabel('频次')

        plt.tight_layout()
        plt.savefig(f"{output_dir}/dataset_analysis.png", dpi=300, bbox_inches='tight')
        plt.show()

        print(f"可视化图表已保存到: {output_dir}/dataset_analysis.png")

if __name__ == "__main__":
    # 分析增强后的数据集
    analyzer = InfantDatasetAnalyzer("enhanced_infant_emotion_dataset.json")
    
    # 生成分析报告
    analyzer.generate_report()
    
    # 创建可视化图表
    analyzer.create_visualizations()
