#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分批处理器 - 将大任务分解为小批次，避免长时间运行
"""

import json
import time
from qwen_enhanced_dataset_builder import EmotionGuidanceDatasetBuilder

class BatchProcessor:
    """分批处理器"""
    
    def __init__(self, api_key: str, model: str = "qwen-turbo"):
        self.api_key = api_key
        self.model = model
        
    def process_in_batches(self, input_file: str, batch_size: int = 20, total_samples: int = 200):
        """分批处理数据"""
        
        print(f"🔄 分批处理模式")
        print(f"总样本数: {total_samples}")
        print(f"批次大小: {batch_size}")
        print(f"总批次数: {total_samples // batch_size}")
        print(f"使用模型: {self.model}")
        
        all_results = []
        
        for batch_num in range(0, total_samples, batch_size):
            batch_end = min(batch_num + batch_size, total_samples)
            current_batch_size = batch_end - batch_num
            
            print(f"\n📦 处理批次 {batch_num//batch_size + 1}: 样本 {batch_num+1}-{batch_end}")
            
            # 创建构建器（每批次使用快速模式）
            builder = EmotionGuidanceDatasetBuilder(
                self.api_key, 
                fast_mode=True,
                model=self.model
            )
            
            batch_start_time = time.time()
            
            # 处理当前批次
            batch_results = builder.build_dataset(
                input_file=input_file,
                output_file=f"batch_{batch_num//batch_size + 1}_result.json",
                target_ages=["12-18m", "18-24m"],  # 先处理2个年龄段
                max_samples=current_batch_size
            )
            
            batch_time = time.time() - batch_start_time
            print(f"⏱️  批次耗时: {batch_time/60:.1f}分钟")
            print(f"平均每样本: {batch_time/current_batch_size:.1f}秒")
            
            all_results.extend(batch_results)
            
            # 批次间休息
            if batch_end < total_samples:
                print("😴 批次间休息30秒...")
                time.sleep(30)
        
        # 合并所有结果
        final_output = f"final_batch_result_{int(time.time())}.json"
        with open(final_output, 'w', encoding='utf-8') as f:
            json.dump(all_results, f, ensure_ascii=False, indent=2)
        
        print(f"\n✅ 所有批次处理完成！")
        print(f"总样本数: {len(all_results)}")
        print(f"结果保存到: {final_output}")
        
        return all_results

def quick_speed_test():
    """快速速度测试"""
    API_KEY = "sk-5eba46fbcff649d5bf28313bc865de10"
    
    models_to_test = ["qwen-turbo", "qwen-plus"]  # 先测试较快的模型
    
    for model in models_to_test:
        print(f"\n🧪 测试模型: {model}")
        
        builder = EmotionGuidanceDatasetBuilder(
            API_KEY, 
            fast_mode=True,
            model=model
        )
        
        start_time = time.time()
        
        # 只处理2个样本测试速度
        results = builder.build_dataset(
            input_file="child_chat_data.json",
            output_file=f"speed_test_{model}.json",
            target_ages=["12-18m"],
            max_samples=2
        )
        
        elapsed = time.time() - start_time
        print(f"模型 {model}: {elapsed:.1f}秒/2样本 = {elapsed/2:.1f}秒/样本")

if __name__ == "__main__":
    print("🚀 批处理和速度测试工具")
    
    choice = input("\n选择模式:\n1. 快速速度测试\n2. 分批处理\n请输入 (1/2): ").strip()
    
    if choice == "1":
        quick_speed_test()
    elif choice == "2":
        API_KEY = "sk-5eba46fbcff649d5bf28313bc865de10"
        
        print("\n配置分批处理:")
        batch_size = int(input("批次大小 (建议10-20): ") or "20")
        total_samples = int(input("总样本数 (建议50-100): ") or "50")
        model = input("模型选择 (qwen-turbo/qwen-plus/qwen-max): ") or "qwen-turbo"
        
        processor = BatchProcessor(API_KEY, model)
        processor.process_in_batches("child_chat_data.json", batch_size, total_samples)
    else:
        print("无效选择")
