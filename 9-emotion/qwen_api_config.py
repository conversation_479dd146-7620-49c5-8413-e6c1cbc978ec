#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen API配置和测试工具
"""

import json
import os
from typing import Dict, Any

class QwenConfig:
    """Qwen API配置管理"""
    
    def __init__(self):
        self.config_file = "qwen_config.json"
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                self.api_key = config.get("api_key", "")
                self.base_url = config.get("base_url", "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation")
                self.model = config.get("model", "qwen-max")
        else:
            self.create_default_config()
    
    def create_default_config(self):
        """创建默认配置文件"""
        default_config = {
            "api_key": "sk-5eba46fbcff649d5bf28313bc865de10",
            "base_url": "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation",
            "model": "qwen-max",
            "parameters": {
                "temperature": 0.7,
                "max_tokens": 2000,
                "top_p": 0.8
            }
        }
        
        with open(self.config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, ensure_ascii=False, indent=2)
        
        print(f"已创建默认配置文件: {self.config_file}")
        print("请编辑配置文件，填入您的API密钥")
        
        self.api_key = ""
        self.base_url = default_config["base_url"]
        self.model = default_config["model"]
    
    def update_api_key(self, api_key: str):
        """更新API密钥"""
        self.api_key = api_key
        
        # 更新配置文件
        if os.path.exists(self.config_file):
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            config["api_key"] = api_key
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
        
        print("API密钥已更新")
    
    def test_api_connection(self):
        """测试API连接"""
        if not self.api_key or self.api_key == "your_api_key_here":
            print("❌ 请先设置有效的API密钥")
            return False
        
        from qwen_enhanced_dataset_builder import QwenAPIClient
        
        client = QwenAPIClient(self.api_key, self.base_url)
        test_prompt = "请简单介绍一下婴幼儿情绪发展的特点。"
        
        print("正在测试API连接...")
        response = client.call_qwen(test_prompt, self.model)
        
        if response:
            print("✅ API连接测试成功")
            print(f"响应示例: {response[:100]}...")
            return True
        else:
            print("❌ API连接测试失败")
            return False

def setup_qwen_api():
    """设置Qwen API"""
    print("=== Qwen API 设置向导 ===\n")
    
    config = QwenConfig()
    
    if not config.api_key or config.api_key == "your_api_key_here":
        print("请输入您的Qwen API密钥:")
        print("(可以从阿里云DashScope控制台获取: https://dashscope.console.aliyun.com/)")
        
        api_key = input("API密钥: ").strip()
        if api_key:
            config.update_api_key(api_key)
        else:
            print("未输入API密钥，退出设置")
            return False
    
    # 测试连接
    if config.test_api_connection():
        print("\n✅ Qwen API设置完成，可以开始构建数据集")
        return True
    else:
        print("\n❌ API设置失败，请检查密钥是否正确")
        return False

if __name__ == "__main__":
    setup_qwen_api()
