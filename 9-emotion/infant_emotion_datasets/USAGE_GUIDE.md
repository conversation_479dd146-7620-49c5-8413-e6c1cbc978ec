# 数据集使用指南 (Usage Guide)

## 🚀 快速开始

### 1. 加载数据集
```python
import json

# 加载单个数据集
def load_dataset(filename):
    with open(filename, 'r', encoding='utf-8') as f:
        return json.load(f)

# 示例：加载增强数据集
data = load_dataset('enhanced_infant_emotion_dataset.json')
print(f"数据集包含 {len(data)} 个样本")
```

### 2. 数据筛选
```python
# 按年龄段筛选
def filter_by_age(data, target_age):
    age_key = 'age_range' if 'age_range' in data[0] else 'target_age'
    return [item for item in data if item[age_key] == target_age]

# 按发展领域筛选
def filter_by_domain(data, domain):
    if 'development_domain' in data[0]:
        return [item for item in data if item['development_domain'] == domain]
    return data

# 示例使用
emotion_data_12_18m = filter_by_age(
    filter_by_domain(data, 'emotion'), 
    '12-18m'
)
```

### 3. 数据合并
```python
# 合并多个数据集
def merge_datasets(filenames):
    merged_data = []
    for filename in filenames:
        data = load_dataset(filename)
        merged_data.extend(data)
    return merged_data

# 示例：合并核心训练数据
core_datasets = [
    'enhanced_infant_emotion_dataset.json',
    'qwen_enhanced_infant_emotion_dataset.json'
]
merged_data = merge_datasets(core_datasets)
```

## 📋 推荐使用方案

### 方案1: 基础模型训练
**目标**: 训练一个基础的婴幼儿情绪指导模型

```python
# 推荐数据集组合
training_datasets = [
    'enhanced_infant_emotion_dataset.json',      # 600样本 - 基础场景
    'infant_emotion_guidance_dataset.json',     # 600样本 - 指导专项
    'infant_guidance_instruction_data.json'     # 600样本 - 指令格式
]

# 总计: 1,800个样本
total_data = merge_datasets(training_datasets)
```

### 方案2: 高质量微调
**目标**: 在基础模型上进行高质量微调

```python
# 高质量数据集
fine_tuning_dataset = 'qwen_enhanced_infant_emotion_dataset.json'  # 200样本

# 加载数据
fine_tune_data = load_dataset(fine_tuning_dataset)

# 这个数据集包含详细的情绪分析和专业指导
```

### 方案3: 专项能力增强
**目标**: 增强特定领域的能力

```python
# 情绪指导专项
emotion_datasets = [
    'emotion_coaching_dataset.json',            # 116样本 - 纯情绪指导
    'qwen_enhanced_infant_emotion_dataset.json' # 200样本 - 高质量情绪分析
]

# 发展评估专项
assessment_dataset = 'developmental_assessment_dataset.json'  # 125样本

# 家长教育专项
parent_dataset = 'parent_education_dataset.json'  # 600样本
```

### 方案4: 全面训练 (推荐)
**目标**: 获得最全面的能力覆盖

```python
# 使用所有数据集
all_datasets = [
    'enhanced_infant_emotion_dataset.json',
    'infant_emotion_guidance_dataset.json', 
    'qwen_enhanced_infant_emotion_dataset.json',
    'emotion_coaching_dataset.json',
    'developmental_assessment_dataset.json',
    'parent_education_dataset.json',
    'professional_training_dataset.json',
    'infant_guidance_conversation_data.json',
    'infant_guidance_instruction_data.json'
]

# 总计: 4,041个样本
comprehensive_data = merge_datasets(all_datasets)
```

## 🎯 按应用场景选择

### 智能育儿助手
```python
parenting_datasets = [
    'parent_education_dataset.json',             # 家长教育
    'infant_guidance_conversation_data.json',   # 对话交互
    'enhanced_infant_emotion_dataset.json'      # 基础场景
]
```

### 专业培训系统
```python
professional_datasets = [
    'professional_training_dataset.json',       # 专业培训
    'developmental_assessment_dataset.json',    # 发展评估
    'qwen_enhanced_infant_emotion_dataset.json' # 高质量分析
]
```

### 情绪指导专家
```python
emotion_expert_datasets = [
    'emotion_coaching_dataset.json',            # 情绪指导专项
    'qwen_enhanced_infant_emotion_dataset.json', # 高质量情绪分析
    'enhanced_infant_emotion_dataset.json'      # 基础情绪场景
]
```

## 🔧 数据预处理

### 标准化年龄格式
```python
def standardize_age_format(data):
    """统一年龄格式"""
    for item in data:
        if 'target_age' in item:
            # qwen数据集使用target_age
            item['age_range'] = item['target_age']
        # 确保所有数据都有age_range字段
    return data
```

### 提取训练对
```python
def extract_training_pairs(data):
    """提取训练对"""
    pairs = []
    
    for item in data:
        if 'instruction' in item:
            # 指令格式数据
            pairs.append({
                'input': item['instruction'] + '\n' + item['input'],
                'output': item['output']
            })
        elif 'infant_behavior' in item:
            # 结构化场景数据
            input_text = f"年龄: {item['age_range']}\n场景: {item['context']['background']}\n行为: {item['infant_behavior']['description']}"
            output_text = item['guidance']['immediate_response']
            pairs.append({
                'input': input_text,
                'output': output_text
            })
    
    return pairs
```

### 数据增强
```python
def augment_data(data, augment_factor=2):
    """简单的数据增强"""
    augmented = []
    
    for item in data:
        augmented.append(item)  # 原始数据
        
        # 创建变体（示例：修改年龄段）
        if augment_factor > 1:
            for i in range(augment_factor - 1):
                variant = item.copy()
                # 这里可以添加具体的增强逻辑
                augmented.append(variant)
    
    return augmented
```

## 📊 数据分析工具

### 统计分析
```python
def analyze_dataset(data):
    """分析数据集统计信息"""
    from collections import Counter
    
    analysis = {
        'total_samples': len(data),
        'age_distribution': {},
        'domain_distribution': {},
        'scenario_types': {}
    }
    
    # 年龄分布
    if 'age_range' in data[0]:
        ages = [item['age_range'] for item in data]
        analysis['age_distribution'] = dict(Counter(ages))
    
    # 发展领域分布
    if 'development_domain' in data[0]:
        domains = [item['development_domain'] for item in data]
        analysis['domain_distribution'] = dict(Counter(domains))
    
    # 场景类型分布
    if 'scenario_type' in data[0]:
        types = [item['scenario_type'] for item in data]
        analysis['scenario_types'] = dict(Counter(types))
    
    return analysis

# 使用示例
data = load_dataset('enhanced_infant_emotion_dataset.json')
stats = analyze_dataset(data)
print(f"数据集分析: {stats}")
```

### 质量检查
```python
def check_data_quality(data):
    """检查数据质量"""
    issues = []
    
    for i, item in enumerate(data):
        # 检查必需字段
        if 'scenario_id' not in item:
            issues.append(f"样本 {i}: 缺少scenario_id")
        
        # 检查年龄格式
        age_key = 'age_range' if 'age_range' in item else 'target_age'
        if age_key in item:
            valid_ages = {'0-1m', '1-6m', '6-12m', '12-18m', '18-24m', '24-36m', '0-6m'}
            if item[age_key] not in valid_ages:
                issues.append(f"样本 {i}: 无效年龄 {item[age_key]}")
    
    return issues
```

## 💡 最佳实践

### 1. 数据集选择原则
- **质量优先**: 优先使用qwen_enhanced数据集进行微调
- **数量平衡**: 确保各年龄段和发展领域的平衡
- **应用导向**: 根据具体应用场景选择相关数据集

### 2. 训练策略
- **分阶段训练**: 先基础训练，再专项增强，最后高质量微调
- **数据验证**: 训练前使用validate_datasets.py检查数据质量
- **增量更新**: 支持新数据的增量添加

### 3. 评估指标
- **覆盖率**: 确保所有年龄段和发展领域都有覆盖
- **一致性**: 检查数据格式和标注的一致性
- **实用性**: 评估生成内容的实际应用价值

## 🛠️ 工具脚本

数据集文件夹提供了以下工具脚本：

- `analyze_datasets.py`: 数据集分析工具
- `validate_datasets.py`: 数据质量验证工具
- `fix_duplicate_ids.py`: 重复ID修复工具

使用方法：
```bash
# 分析数据集
python3 analyze_datasets.py

# 验证数据质量
python3 validate_datasets.py

# 修复重复ID
python3 fix_duplicate_ids.py
```

## 📞 技术支持

如果在使用过程中遇到问题：

1. 查看README.md了解数据集详情
2. 查看DATASET_OVERVIEW.md了解数据集概览
3. 运行验证脚本检查数据质量
4. 参考本使用指南的示例代码

---

*祝您训练顺利！*
