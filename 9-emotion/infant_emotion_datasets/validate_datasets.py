#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集验证脚本 - 检查数据质量和完整性
"""

import json
import os
from collections import Counter

def validate_dataset(filename):
    """验证单个数据集"""
    print(f"\n🔍 验证数据集: {filename}")
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            print("❌ 错误: 数据格式不是列表")
            return False
        
        if len(data) == 0:
            print("❌ 错误: 数据集为空")
            return False
        
        print(f"✅ 基本格式检查通过 ({len(data)} 个样本)")
        
        # 检查必需字段
        sample = data[0]
        required_fields = []
        
        if 'scenario_id' in sample:
            required_fields = ['scenario_id']
            if 'age_range' in sample:
                required_fields.extend(['age_range', 'development_domain'])
            elif 'target_age' in sample:
                required_fields.extend(['target_age'])
        elif 'instruction' in sample:
            required_fields = ['instruction', 'input', 'output']
        elif 'conversation' in sample:
            required_fields = ['conversation']
        
        # 验证所有样本的字段完整性
        missing_fields = []
        for i, item in enumerate(data):
            for field in required_fields:
                if field not in item:
                    missing_fields.append(f"样本{i}: 缺少字段 '{field}'")
        
        if missing_fields:
            print(f"❌ 字段完整性检查失败:")
            for error in missing_fields[:5]:  # 只显示前5个错误
                print(f"   {error}")
            if len(missing_fields) > 5:
                print(f"   ... 还有 {len(missing_fields) - 5} 个错误")
            return False
        else:
            print("✅ 字段完整性检查通过")
        
        # 检查年龄分布
        if 'age_range' in sample:
            ages = [item['age_range'] for item in data]
            age_dist = Counter(ages)
            print(f"📊 年龄分布: {dict(age_dist)}")
            
            # 检查年龄值是否合法
            valid_ages = {'0-1m', '1-6m', '6-12m', '12-18m', '18-24m', '24-36m'}
            invalid_ages = set(ages) - valid_ages
            if invalid_ages:
                print(f"❌ 发现无效年龄值: {invalid_ages}")
                return False
        
        elif 'target_age' in sample:
            ages = [item['target_age'] for item in data]
            age_dist = Counter(ages)
            print(f"📊 年龄分布: {dict(age_dist)}")
            
            # 检查年龄值是否合法
            valid_ages = {'0-6m', '6-12m', '12-18m', '18-24m', '24-36m'}
            invalid_ages = set(ages) - valid_ages
            if invalid_ages:
                print(f"❌ 发现无效年龄值: {invalid_ages}")
                return False
        
        # 检查发展领域分布
        if 'development_domain' in sample:
            domains = [item['development_domain'] for item in data]
            domain_dist = Counter(domains)
            print(f"🧠 发展领域分布: {dict(domain_dist)}")
            
            # 检查领域值是否合法
            valid_domains = {'cognitive', 'motor', 'language', 'social', 'emotion'}
            invalid_domains = set(domains) - valid_domains
            if invalid_domains:
                print(f"❌ 发现无效发展领域: {invalid_domains}")
                return False
        
        # 检查ID唯一性
        if 'scenario_id' in sample:
            ids = [item['scenario_id'] for item in data]
            if len(ids) != len(set(ids)):
                duplicate_count = len(ids) - len(set(ids))
                print(f"❌ 发现 {duplicate_count} 个重复的scenario_id")
                return False
            else:
                print("✅ scenario_id唯一性检查通过")
        
        print(f"✅ 数据集 {filename} 验证通过")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ JSON格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        return False

def generate_summary_report():
    """生成数据集汇总报告"""
    print("\n" + "="*60)
    print("📋 数据集汇总报告")
    print("="*60)
    
    total_samples = 0
    total_size = 0
    datasets_by_type = {}
    age_distribution = Counter()
    domain_distribution = Counter()
    
    for filename in sorted(os.listdir('.')):
        if filename.endswith('.json') and filename != 'summary_report.json':
            try:
                with open(filename, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                if isinstance(data, list) and data:
                    sample_count = len(data)
                    file_size = os.path.getsize(filename) / 1024 / 1024  # MB
                    
                    total_samples += sample_count
                    total_size += file_size
                    
                    # 分类数据集类型
                    sample = data[0]
                    if 'target_age' in sample:
                        dataset_type = 'qwen_enhanced'
                    elif 'age_range' in sample:
                        dataset_type = 'structured_scenario'
                    elif 'instruction' in sample:
                        dataset_type = 'instruction_following'
                    elif 'conversation' in sample:
                        dataset_type = 'conversation'
                    else:
                        dataset_type = 'unknown'
                    
                    if dataset_type not in datasets_by_type:
                        datasets_by_type[dataset_type] = {'count': 0, 'samples': 0}
                    datasets_by_type[dataset_type]['count'] += 1
                    datasets_by_type[dataset_type]['samples'] += sample_count
                    
                    # 统计年龄和领域分布
                    if 'age_range' in sample:
                        ages = [item['age_range'] for item in data]
                        age_distribution.update(ages)
                    elif 'target_age' in sample:
                        ages = [item['target_age'] for item in data]
                        age_distribution.update(ages)
                    
                    if 'development_domain' in sample:
                        domains = [item['development_domain'] for item in data]
                        domain_distribution.update(domains)
                        
            except Exception as e:
                print(f"处理 {filename} 时出错: {e}")
    
    print(f"📊 总计: {total_samples:,} 个样本")
    print(f"💾 总大小: {total_size:.2f} MB")
    print(f"📁 数据集数量: {sum(info['count'] for info in datasets_by_type.values())}")
    
    print(f"\n📋 按类型分布:")
    for dtype, info in datasets_by_type.items():
        print(f"   {dtype}: {info['count']} 个数据集, {info['samples']:,} 个样本")
    
    if age_distribution:
        print(f"\n👶 年龄分布:")
        for age, count in sorted(age_distribution.items()):
            print(f"   {age}: {count:,} 个样本")
    
    if domain_distribution:
        print(f"\n🧠 发展领域分布:")
        for domain, count in sorted(domain_distribution.items()):
            print(f"   {domain}: {count:,} 个样本")
    
    # 保存汇总报告
    report = {
        'total_samples': total_samples,
        'total_size_mb': round(total_size, 2),
        'datasets_by_type': datasets_by_type,
        'age_distribution': dict(age_distribution),
        'domain_distribution': dict(domain_distribution),
        'generated_at': '2024-07-25'
    }
    
    with open('summary_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n💾 汇总报告已保存到 summary_report.json")

def main():
    """主验证函数"""
    print("🔍 婴幼儿情绪发展数据集验证工具")
    print("="*50)
    
    json_files = [f for f in os.listdir('.') if f.endswith('.json') and f != 'summary_report.json']
    
    if not json_files:
        print("❌ 未找到JSON数据集文件")
        return
    
    print(f"发现 {len(json_files)} 个数据集文件")
    
    valid_count = 0
    invalid_count = 0
    
    for filename in sorted(json_files):
        if validate_dataset(filename):
            valid_count += 1
        else:
            invalid_count += 1
    
    print("\n" + "="*50)
    print("📊 验证结果汇总:")
    print(f"✅ 通过验证: {valid_count} 个数据集")
    print(f"❌ 验证失败: {invalid_count} 个数据集")
    
    if invalid_count == 0:
        print("🎉 所有数据集验证通过！")
    else:
        print("⚠️  请修复验证失败的数据集")
    
    # 生成汇总报告
    generate_summary_report()

if __name__ == "__main__":
    main()
