#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复重复ID的脚本
"""

import json
import random
from collections import Counter

def fix_duplicate_ids(filename):
    """修复数据集中的重复ID"""
    print(f"🔧 修复数据集: {filename}")
    
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list) or not data:
            print("❌ 数据格式错误")
            return False
        
        # 检查是否有scenario_id字段
        if 'scenario_id' not in data[0]:
            print("✅ 无需修复 (没有scenario_id字段)")
            return True
        
        # 收集所有ID
        ids = [item['scenario_id'] for item in data]
        id_counts = Counter(ids)
        
        # 找出重复的ID
        duplicates = {id_val: count for id_val, count in id_counts.items() if count > 1}
        
        if not duplicates:
            print("✅ 无重复ID，无需修复")
            return True
        
        print(f"发现重复ID: {len(duplicates)} 个")
        
        # 修复重复ID
        used_ids = set()
        fixed_count = 0
        
        for i, item in enumerate(data):
            original_id = item['scenario_id']
            
            if original_id in used_ids:
                # 生成新的唯一ID
                base_parts = original_id.split('_')
                if len(base_parts) >= 2:
                    prefix = '_'.join(base_parts[:-1])
                    new_id = f"{prefix}_{random.randint(1000, 9999)}"
                else:
                    new_id = f"{original_id}_{random.randint(1000, 9999)}"
                
                # 确保新ID是唯一的
                while new_id in used_ids:
                    new_id = f"{prefix}_{random.randint(1000, 9999)}"
                
                item['scenario_id'] = new_id
                fixed_count += 1
                print(f"  修复: {original_id} -> {new_id}")
            
            used_ids.add(item['scenario_id'])
        
        # 保存修复后的数据
        backup_filename = f"{filename}.backup"
        with open(backup_filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 修复完成: {fixed_count} 个重复ID已修复")
        print(f"📁 备份文件: {backup_filename}")
        return True
        
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        return False

def main():
    """主修复函数"""
    print("🔧 重复ID修复工具")
    print("="*40)
    
    # 需要修复的文件列表
    files_to_fix = [
        'enhanced_infant_emotion_dataset.json',
        'infant_emotion_guidance_dataset.json', 
        'parent_education_dataset.json',
        'professional_training_dataset.json'
    ]
    
    fixed_count = 0
    failed_count = 0
    
    for filename in files_to_fix:
        try:
            if fix_duplicate_ids(filename):
                fixed_count += 1
            else:
                failed_count += 1
        except FileNotFoundError:
            print(f"⚠️  文件不存在: {filename}")
            failed_count += 1
        except Exception as e:
            print(f"❌ 处理 {filename} 时出错: {e}")
            failed_count += 1
        print()
    
    print("="*40)
    print("📊 修复结果汇总:")
    print(f"✅ 成功修复: {fixed_count} 个文件")
    print(f"❌ 修复失败: {failed_count} 个文件")
    
    if failed_count == 0:
        print("🎉 所有文件修复完成！")
    else:
        print("⚠️  部分文件修复失败，请检查错误信息")

if __name__ == "__main__":
    main()
