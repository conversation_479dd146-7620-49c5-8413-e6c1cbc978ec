#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Qwen API的婴幼儿社会情绪发展指导数据集构建器
通过调用Qwen API进行情感分析和指导策略提取，构建高质量的专业数据集
"""

import json
import time
import random
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import requests

@dataclass
class EmotionAnalysis:
    """情感分析结果"""
    initial_emotion: str
    emotion_changes: List[str]
    final_emotion: str
    emotion_triggers: List[str]
    regulation_strategies: List[str]

@dataclass
class GuidanceAnalysis:
    """指导策略分析结果"""
    coaching_techniques: List[str]
    intervention_points: List[str]
    effectiveness_indicators: List[str]
    theoretical_basis: List[str]

class QwenAPIClient:
    """Qwen API客户端"""
    
    def __init__(self, api_key: str, base_url: str = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"):
        self.api_key = api_key
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def call_qwen(self, prompt: str, model: str = "qwen-max") -> Optional[str]:
        """调用Qwen API"""
        payload = {
            "model": model,
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            },
            "parameters": {
                "temperature": 0.7,
                "max_tokens": 2000
            }
        }
        
        try:
            response = requests.post(self.base_url, headers=self.headers, json=payload)
            response.raise_for_status()
            
            result = response.json()
            if "output" in result and "text" in result["output"]:
                return result["output"]["text"]
            else:
                print(f"API响应格式异常: {result}")
                return None
                
        except requests.exceptions.RequestException as e:
            print(f"API调用失败: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"JSON解析失败: {e}")
            return None

class EmotionGuidanceDatasetBuilder:
    """情绪指导数据集构建器"""

    def __init__(self, api_key: str, fast_mode: bool = False, model: str = "qwen-max"):
        self.qwen_client = QwenAPIClient(api_key)
        self.fast_mode = fast_mode  # 快速模式：减少API调用，使用更多规则生成
        self.model = model  # 可选择的模型：qwen-turbo(快), qwen-plus(平衡), qwen-max(慢但质量高)
        
        # 情感分析提示模板
        self.emotion_analysis_prompt = """
请分析以下对话中孩子的情感变化过程和智能助手的调节策略：

对话内容：
{dialogue}

请从以下几个维度进行专业分析：

1. 情感识别与变化：
   - 孩子的初始情绪状态
   - 对话过程中的情绪变化轨迹
   - 最终的情绪状态
   - 情绪变化的触发因素

2. 指导策略分析：
   - 智能助手使用的情绪指导技巧
   - 关键的干预时机和方式
   - 指导效果的评估指标
   - 所体现的理论基础（如情绪指导理论、认知行为理论等）

3. 社会情绪发展要素：
   - 涉及的社会情绪发展技能
   - 年龄适宜性评估
   - 发展性指导建议

请以JSON格式返回分析结果，包含以上所有维度的详细信息。
"""

        # 婴幼儿适配提示模板
        self.infant_adaptation_prompt = """
请将以下学龄儿童的情绪指导对话适配为适合婴幼儿（0-3岁）的场景：

原始对话：
{original_dialogue}

适配要求：
1. 年龄段：{target_age}
2. 发展特点：考虑该年龄段的认知、语言、社交发展水平
3. 表达方式：从语言表达转为行为表达（哭闹、肢体动作、表情等）
4. 指导策略：调整为适合婴幼儿的养育指导方式
5. 场景设置：调整为典型的婴幼儿日常生活场景

请生成一个完整的婴幼儿情绪指导场景，包括：
- 场景背景描述
- 婴幼儿的具体行为表现
- 养育者的观察和理解
- 专业的指导建议和策略
- 预期的发展目标

请以结构化的JSON格式返回结果。
"""

        # 专业增强提示模板
        self.professional_enhancement_prompt = """
请对以下婴幼儿情绪指导场景进行专业增强：

场景内容：
{scenario}

增强要求：
1. 理论基础：融入情绪指导理论、依恋理论、发育心理学等专业知识
2. 评估维度：添加基于ASQ:SE-2等标准化工具的评估要点
3. 干预策略：提供循证的早期干预策略和技巧
4. 家长教育：包含科学育儿知识和实践指导
5. 发展监测：提供后续观察和评估的建议

请生成专业增强后的完整场景，确保内容的科学性和实用性。
"""

    def analyze_dialogue_emotion(self, dialogue: str) -> Optional[Dict]:
        """分析对话中的情感变化"""
        prompt = self.emotion_analysis_prompt.format(dialogue=dialogue)
        response = self.qwen_client.call_qwen(prompt, self.model)
        
        if response:
            try:
                # 尝试提取JSON部分
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                if json_start != -1 and json_end != -1:
                    json_str = response[json_start:json_end]
                    return json.loads(json_str)
            except json.JSONDecodeError:
                print(f"无法解析情感分析结果: {response}")
        
        return None

    def adapt_to_infant_scenario(self, original_dialogue: str, target_age: str) -> Optional[Dict]:
        """将对话适配为婴幼儿场景"""
        prompt = self.infant_adaptation_prompt.format(
            original_dialogue=original_dialogue,
            target_age=target_age
        )
        response = self.qwen_client.call_qwen(prompt)
        
        if response:
            try:
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                if json_start != -1 and json_end != -1:
                    json_str = response[json_start:json_end]
                    return json.loads(json_str)
            except json.JSONDecodeError:
                print(f"无法解析适配结果: {response}")
        
        return None

    def enhance_with_professional_knowledge(self, scenario: Dict) -> Optional[Dict]:
        """用专业知识增强场景"""
        prompt = self.professional_enhancement_prompt.format(
            scenario=json.dumps(scenario, ensure_ascii=False, indent=2)
        )
        response = self.qwen_client.call_qwen(prompt)
        
        if response:
            try:
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                if json_start != -1 and json_end != -1:
                    json_str = response[json_start:json_end]
                    return json.loads(json_str)
            except json.JSONDecodeError:
                print(f"无法解析增强结果: {response}")
        
        return None

    def process_dialogue_sample(self, dialogue_data: Dict, target_age: str) -> Optional[Dict]:
        """处理单个对话样本"""
        try:
            # 1. 提取对话内容 - 适配您的数据格式
            if "input" in dialogue_data:
                # 您的数据格式：使用</s>分隔对话轮次
                dialogue_text = dialogue_data["input"].replace("</s>", "\n")
            elif "dialog" in dialogue_data:
                # 处理结构化对话
                dialogue_text = self._format_structured_dialogue(dialogue_data["dialog"])
            else:
                print("无法识别对话格式")
                return None

            topic = dialogue_data.get("topic", "未知话题")
            print(f"正在分析对话: {topic}")

            # 2. 情感分析
            emotion_analysis = self.analyze_dialogue_emotion(dialogue_text)
            if not emotion_analysis:
                print("情感分析失败，使用默认分析")
                emotion_analysis = self._create_default_emotion_analysis(dialogue_text, topic)

            time.sleep(0.5)  # 减少API调用间隔

            # 3. 适配为婴幼儿场景
            infant_scenario = self.adapt_to_infant_scenario(dialogue_text, target_age)
            if not infant_scenario:
                print("婴幼儿场景适配失败，使用规则生成")
                infant_scenario = self._create_rule_based_infant_scenario(topic, target_age)

            time.sleep(0.5)  # 减少API调用间隔

            # 4. 专业知识增强
            enhanced_scenario = self.enhance_with_professional_knowledge(infant_scenario)
            if not enhanced_scenario:
                print("专业增强失败，使用基础场景")
                enhanced_scenario = infant_scenario

            # 5. 整合结果
            final_result = {
                "scenario_id": f"qwen_enhanced_{target_age}_{random.randint(1000, 9999)}",
                "original_topic": topic,
                "original_dialogue": dialogue_text[:200] + "..." if len(dialogue_text) > 200 else dialogue_text,
                "target_age": target_age,
                "emotion_analysis": emotion_analysis,
                "infant_scenario": enhanced_scenario,
                "generation_method": "qwen_api_enhanced",
                "timestamp": time.time()
            }

            return final_result

        except Exception as e:
            print(f"处理对话样本时出错: {e}")
            return None

    def _format_structured_dialogue(self, dialog_list: List[Dict]) -> str:
        """格式化结构化对话"""
        formatted = []
        for turn in dialog_list:
            speaker = turn.get("speaker", "未知")
            text = turn.get("text", "")
            formatted.append(f"{speaker}: {text}")
        return "\n".join(formatted)

    def _create_default_emotion_analysis(self, dialogue_text: str, topic: str) -> Dict:
        """创建默认的情感分析结果"""
        # 基于话题和对话内容的简单情感分析
        emotion_keywords = {
            "happy": ["开心", "高兴", "兴奋", "愉快", "快乐", "喜欢"],
            "sad": ["难过", "伤心", "失落", "沮丧", "哭", "悲伤"],
            "angry": ["生气", "愤怒", "烦躁", "讨厌", "气愤"],
            "fear": ["害怕", "恐惧", "紧张", "担心", "焦虑"],
            "surprise": ["惊讶", "意外", "好奇", "新奇"],
            "neutral": ["平静", "正常", "一般"]
        }

        detected_emotions = []
        for emotion, keywords in emotion_keywords.items():
            if any(keyword in dialogue_text for keyword in keywords):
                detected_emotions.append(emotion)

        if not detected_emotions:
            detected_emotions = ["neutral"]

        return {
            "emotion_trajectory": {
                "initial_emotion": detected_emotions[0],
                "emotion_changes": [
                    {"stage": "开始", "emotion": detected_emotions[0], "intensity": "中等"},
                    {"stage": "发展", "emotion": detected_emotions[-1] if len(detected_emotions) > 1 else detected_emotions[0], "intensity": "中等"}
                ],
                "final_emotion": detected_emotions[-1],
                "emotion_triggers": [f"与{topic}相关的情境"]
            },
            "guidance_strategies": {
                "techniques_used": ["情绪确认", "共情回应", "解决方案探索"],
                "intervention_points": ["情绪识别", "情绪理解", "应对策略"],
                "effectiveness_indicators": ["情绪缓解", "问题解决", "关系改善"]
            },
            "developmental_elements": {
                "skills_addressed": ["情绪识别", "情绪表达", "社交技能"],
                "theoretical_basis": ["情绪指导理论", "发展心理学"]
            }
        }

    def _create_rule_based_infant_scenario(self, topic: str, target_age: str) -> Dict:
        """基于规则创建婴幼儿场景"""
        age_behaviors = {
            "0-6m": ["哭闹", "微笑", "注视", "抓握", "踢腿"],
            "6-12m": ["坐立", "爬行", "拍手", "咿呀学语", "认生"],
            "12-18m": ["走路", "指认", "简单词汇", "模仿", "分离焦虑"],
            "18-24m": ["跑跳", "两词句", "自我意识", "情绪爆发", "探索"],
            "24-36m": ["完整句子", "角色游戏", "情绪调节", "同伴互动", "规则理解"]
        }

        behaviors = random.sample(age_behaviors.get(target_age, ["一般行为"]), 2)

        scenario_mapping = {
            "吃饭": "喂养时的情绪反应",
            "睡觉": "睡眠时间的安抚需求",
            "游戏": "游戏中的探索行为",
            "哭闹": "情绪表达和安抚",
            "学习": "认知发展活动",
            "社交": "与他人的互动反应"
        }

        background = scenario_mapping.get(topic, f"与{topic}相关的日常活动")

        return {
            "scenario_background": f"{target_age}婴幼儿在{background}中的表现",
            "infant_behaviors": {
                "observable_signs": behaviors,
                "emotional_state": "需要观察和理解",
                "developmental_indicators": [f"{target_age}阶段的典型表现"]
            },
            "caregiver_guidance": {
                "observation_points": ["观察婴儿的行为信号", "理解情绪表达"],
                "response_strategies": ["及时回应", "提供安全感", "适当引导"],
                "developmental_goals": ["促进健康发展", "建立安全依恋"]
            },
            "professional_insights": {
                "theoretical_basis": f"基于{target_age}阶段的发展特点",
                "key_principles": ["回应性照护", "发展适宜性", "个体差异"]
            }
        }

    def build_dataset(self, input_file: str, output_file: str, 
                     target_ages: List[str] = None, max_samples: int = 100):
        """构建完整数据集"""
        if target_ages is None:
            target_ages = ["0-6m", "6-12m", "12-18m", "18-24m", "24-36m"]
        
        # 加载原始数据
        with open(input_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        print(f"加载了 {len(original_data)} 个原始对话")
        
        enhanced_dataset = []
        samples_per_age = max_samples // len(target_ages)
        
        for age_idx, age in enumerate(target_ages):
            print(f"\n开始处理年龄段: {age} ({age_idx+1}/{len(target_ages)})")
            age_samples = []

            # 随机选择样本进行处理
            selected_samples = random.sample(original_data, min(samples_per_age, len(original_data)))

            start_time = time.time()
            for i, sample in enumerate(selected_samples):
                # 计算预估剩余时间
                if i > 0:
                    elapsed = time.time() - start_time
                    avg_time_per_sample = elapsed / i
                    remaining_samples = len(selected_samples) - i
                    eta_seconds = remaining_samples * avg_time_per_sample
                    eta_minutes = eta_seconds / 60
                    print(f"处理进度: {i+1}/{len(selected_samples)} | 预计剩余: {eta_minutes:.1f}分钟")
                else:
                    print(f"处理进度: {i+1}/{len(selected_samples)}")

                result = self.process_dialogue_sample(sample, age)
                if result:
                    age_samples.append(result)
                    print(f"✅ 成功生成场景: {result['scenario_id']}")
                else:
                    print("❌ 场景生成失败")

                # 避免API调用过于频繁
                if not self.fast_mode:
                    time.sleep(1)  # 正常模式1秒间隔
                else:
                    time.sleep(0.3)  # 快速模式0.3秒间隔
            
            enhanced_dataset.extend(age_samples)
            print(f"年龄段 {age} 完成，生成 {len(age_samples)} 个场景")
        
        # 保存结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(enhanced_dataset, f, ensure_ascii=False, indent=2)
        
        print(f"\n数据集构建完成！")
        print(f"总共生成 {len(enhanced_dataset)} 个增强场景")
        print(f"结果已保存到: {output_file}")
        
        return enhanced_dataset

# 使用示例
if __name__ == "__main__":
    # 请替换为您的Qwen API密钥
    API_KEY = "sk-5eba46fbcff649d5bf28313bc865de10"
    
    # 创建数据集构建器 - 使用快速模式和turbo模型
    builder = EmotionGuidanceDatasetBuilder(
        API_KEY,
        fast_mode=True,  # 启用快速模式
        model="qwen-turbo"  # 使用最快的模型
    )

    # 构建数据集
    builder.build_dataset(
        input_file="child_chat_data.json",
        output_file="qwen_enhanced_infant_emotion_dataset.json",
        target_ages=["12-18m", "18-24m"],  # 先处理2个年龄段测试
        max_samples=50  # 先处理少量样本测试效果
    )
