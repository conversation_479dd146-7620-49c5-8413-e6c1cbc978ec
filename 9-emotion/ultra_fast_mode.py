#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
超快速模式 - 最小化API调用，最大化规则生成
"""

import json
import time
import random
from qwen_enhanced_dataset_builder import EmotionGuidanceDatasetBuilder

class UltraFastBuilder(EmotionGuidanceDatasetBuilder):
    """超快速构建器 - 只在关键步骤使用API"""
    
    def __init__(self, api_key: str):
        super().__init__(api_key, fast_mode=True, model="qwen-turbo")
        self.api_call_count = 0
        self.rule_generation_count = 0
    
    def process_dialogue_sample(self, dialogue_data, target_age):
        """超快速处理 - 只做情感分析，其他用规则生成"""
        try:
            # 1. 提取对话内容
            if "input" in dialogue_data:
                dialogue_text = dialogue_data["input"].replace("</s>", "\n")
            else:
                return None
            
            topic = dialogue_data.get("topic", "未知话题")
            print(f"⚡ 超快速处理: {topic}")
            
            # 2. 只做情感分析（使用API）
            emotion_analysis = self.analyze_dialogue_emotion(dialogue_text)
            if emotion_analysis:
                self.api_call_count += 1
                print("  ✅ API情感分析完成")
            else:
                emotion_analysis = self._create_default_emotion_analysis(dialogue_text, topic)
                self.rule_generation_count += 1
                print("  🔧 规则情感分析完成")
            
            # 3. 婴幼儿场景适配（使用规则）
            infant_scenario = self._create_rule_based_infant_scenario(topic, target_age)
            self.rule_generation_count += 1
            print("  🔧 规则场景生成完成")
            
            # 4. 专业增强（使用规则）
            enhanced_scenario = self._enhance_with_rules(infant_scenario, target_age)
            self.rule_generation_count += 1
            print("  🔧 规则专业增强完成")
            
            # 5. 整合结果
            final_result = {
                "scenario_id": f"ultra_fast_{target_age}_{random.randint(1000, 9999)}",
                "original_topic": topic,
                "target_age": target_age,
                "emotion_analysis": emotion_analysis,
                "infant_scenario": enhanced_scenario,
                "generation_method": "ultra_fast_hybrid",
                "api_calls_used": self.api_call_count,
                "rule_generations_used": self.rule_generation_count,
                "timestamp": time.time()
            }
            
            return final_result
            
        except Exception as e:
            print(f"❌ 处理失败: {e}")
            return None
    
    def _enhance_with_rules(self, scenario, target_age):
        """基于规则的专业增强"""
        
        # 年龄特定的发展要点
        age_development = {
            "0-6m": {
                "key_skills": ["视觉追踪", "社交微笑", "头部控制", "抓握反射"],
                "emotional_milestones": ["基本情绪表达", "安抚反应", "依恋形成"],
                "guidance_focus": ["回应性照护", "感官刺激", "安全依恋建立"]
            },
            "6-12m": {
                "key_skills": ["坐立", "爬行", "物体永恒性", "陌生人焦虑"],
                "emotional_milestones": ["分离焦虑", "情绪调节初步", "社交参照"],
                "guidance_focus": ["探索支持", "安全感建立", "语言输入"]
            },
            "12-18m": {
                "key_skills": ["行走", "指向", "模仿", "简单词汇"],
                "emotional_milestones": ["自主性发展", "情绪表达多样化", "同理心萌芽"],
                "guidance_focus": ["自主性支持", "语言发展", "社交技能"]
            },
            "18-24m": {
                "key_skills": ["跑跳", "两词句", "假想游戏", "自我意识"],
                "emotional_milestones": ["情绪命名", "自我控制", "同伴意识"],
                "guidance_focus": ["情绪教育", "行为引导", "社交互动"]
            },
            "24-36m": {
                "key_skills": ["完整句子", "角色游戏", "规则理解", "同伴游戏"],
                "emotional_milestones": ["情绪调节", "同理心发展", "道德感萌芽"],
                "guidance_focus": ["情绪管理", "社交技能", "规则学习"]
            }
        }
        
        age_info = age_development.get(target_age, age_development["12-18m"])
        
        # 增强场景
        enhanced = scenario.copy()
        enhanced.update({
            "developmental_context": {
                "key_skills": age_info["key_skills"],
                "emotional_milestones": age_info["emotional_milestones"],
                "guidance_focus": age_info["guidance_focus"]
            },
            "assessment_dimensions": [
                "情绪识别与表达",
                "社会交往能力",
                "自我调节能力",
                "依恋关系质量"
            ],
            "intervention_strategies": [
                "回应性互动",
                "环境适应支持",
                "发展适宜性活动",
                "家庭指导"
            ],
            "theoretical_framework": {
                "attachment_theory": "安全依恋关系是社会情绪发展的基础",
                "developmental_theory": f"{target_age}阶段的发展特点和需求",
                "emotion_coaching": "情绪指导和调节策略"
            }
        })
        
        return enhanced

def ultra_fast_demo():
    """超快速演示"""
    print("⚡ 超快速模式演示")
    
    API_KEY = "sk-5eba46fbcff649d5bf28313bc865de10"
    
    builder = UltraFastBuilder(API_KEY)
    
    start_time = time.time()
    
    # 处理10个样本
    results = builder.build_dataset(
        input_file="child_chat_data.json",
        output_file="ultra_fast_result.json",
        target_ages=["12-18m"],
        max_samples=10
    )
    
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n⚡ 超快速模式完成！")
    print(f"总耗时: {total_time:.1f}秒 ({total_time/60:.1f}分钟)")
    print(f"平均每样本: {total_time/10:.1f}秒")
    print(f"API调用次数: {builder.api_call_count}")
    print(f"规则生成次数: {builder.rule_generation_count}")
    print(f"API调用比例: {builder.api_call_count/(builder.api_call_count + builder.rule_generation_count)*100:.1f}%")
    
    # 估算大规模处理
    samples_200 = (total_time / 10) * 200
    print(f"\n📊 处理200个样本预计: {samples_200/60:.1f}分钟 ({samples_200/3600:.1f}小时)")

if __name__ == "__main__":
    ultra_fast_demo()
