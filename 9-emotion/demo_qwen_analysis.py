#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen API情感分析演示
展示如何使用Qwen API分析对话中的情感变化和指导策略
"""

import json
from qwen_enhanced_dataset_builder import QwenAPIClient

def demo_emotion_analysis():
    """演示情感分析功能"""
    
    # 示例对话（从您的数据集中选取）
    sample_dialogue = """
孩子：我好兴奋啊，今天我们要去看电影！
智能助手：哇，看电影真是很有趣呢！但是我注意到你好像很兴奋，是不是有点紧张呢？
孩子：是啊，我就是很兴奋啊，因为我好久都没有看电影了，这次我想看一部很刺激的动作片！
智能助手：哦，我明白了。你想看一部能让你热血沸腾的电影。但是有时候太兴奋也容易紧张，你有没想过如何控制自己的情绪，让自己更放松呢？
孩子：嗯，我一直很想放松自己，可我总是做不到，在观影时我总是会仔细盯着屏幕，想不再去想其他的事情，可真的是做不到。
智能助手：我明白你的感受，应该说你很想享受这次观影过程。那么有没有想过平时可以有意识地练习让自己更放松呢？比如平时可以用一些缓慢的呼吸法来减轻你的紧张程度？
孩子：听起来不错，我觉得这个方法可以试试。如果真的能管用，那么下次再看电影的时候就可以比较放心了。
智能助手：对，只要坚持练习，一定会有所成效。而且，如果你在观影中感到紧张，不妨试着转移自己的注意力，让自己在笛调和音乐中缓解一下情绪，也可以让自己的注意力从屏幕上转移到座位上，想想座位的柔软和舒适，这样就可以消除紧张的感觉。
孩子：嗯，好的，我一定会试试看。真的谢谢你的建议。
智能助手：不用谢，这就是我的工作。当你遇到情绪上的困难时，记得随时和我聊天，我愿意陪伴你一起成长。
"""
    
    # 情感分析提示
    analysis_prompt = f"""
请专业分析以下对话中孩子的情感变化和智能助手的指导策略：

对话内容：
{sample_dialogue}

请从以下维度进行分析：

1. 情感轨迹分析：
   - 孩子的初始情绪：兴奋、期待
   - 情绪变化过程：兴奋 → 紧张担忧 → 困惑 → 接受建议 → 感谢
   - 情绪触发因素：即将看电影的期待、对自己情绪控制的担忧

2. 指导策略识别：
   - 情绪确认：助手确认了孩子的兴奋情绪
   - 问题探索：引导孩子思考情绪管理
   - 技巧传授：提供具体的放松方法（呼吸法、注意力转移）
   - 鼓励支持：给予持续的支持和陪伴

3. 社会情绪发展要素：
   - 情绪识别能力：帮助孩子认识自己的情绪状态
   - 情绪调节技能：教授具体的调节方法
   - 自我效能感：增强孩子对情绪管理的信心

请以JSON格式返回详细分析结果。
"""
    
    print("=== Qwen API 情感分析演示 ===\n")
    print("分析对话:")
    print(sample_dialogue[:200] + "...\n")
    
    # 注意：这里需要您的实际API密钥
    print("注意：请确保已设置有效的Qwen API密钥")
    print("可以运行 python3 qwen_api_config.py 进行设置\n")
    
    # 模拟API调用结果（实际使用时会调用真实API）
    mock_analysis_result = {
        "emotion_trajectory": {
            "initial_emotion": "兴奋、期待",
            "emotion_changes": [
                {"stage": "开始", "emotion": "兴奋", "intensity": "高", "description": "对看电影充满期待"},
                {"stage": "中期", "emotion": "紧张担忧", "intensity": "中", "description": "担心无法控制情绪"},
                {"stage": "后期", "emotion": "接受", "intensity": "低", "description": "愿意尝试建议的方法"},
                {"stage": "结束", "emotion": "感谢", "intensity": "积极", "description": "对获得帮助表示感谢"}
            ],
            "final_emotion": "积极、感谢",
            "emotion_triggers": ["即将看电影的期待", "对自己情绪控制能力的担忧", "获得实用建议"]
        },
        "guidance_strategies": {
            "techniques_used": [
                "情绪确认与验证",
                "引导式提问",
                "具体技巧传授",
                "鼓励与支持"
            ],
            "intervention_points": [
                "识别过度兴奋状态",
                "探索情绪管理需求",
                "提供具体解决方案",
                "建立持续支持关系"
            ],
            "effectiveness_indicators": [
                "孩子表达愿意尝试",
                "情绪从焦虑转向积极",
                "主动表达感谢",
                "建立信任关系"
            ]
        },
        "developmental_elements": {
            "skills_addressed": [
                "情绪识别能力",
                "情绪调节技能",
                "自我效能感",
                "求助能力"
            ],
            "age_appropriateness": "适合学龄儿童，需要适配为婴幼儿场景",
            "theoretical_basis": [
                "情绪指导理论",
                "认知行为理论",
                "社会学习理论"
            ]
        }
    }
    
    print("分析结果示例:")
    print(json.dumps(mock_analysis_result, ensure_ascii=False, indent=2))
    
    return mock_analysis_result

def demo_infant_adaptation():
    """演示婴幼儿场景适配"""
    
    adaptation_prompt = """
请将上述学龄儿童的看电影情绪管理对话适配为适合12-18个月幼儿的场景：

适配要求：
1. 年龄特点：12-18个月幼儿的认知和语言发展水平
2. 表达方式：从语言表达转为行为表达（哭闹、肢体动作、表情等）
3. 场景调整：从看电影调整为适合幼儿的活动
4. 指导方式：调整为适合养育者的观察和回应策略

生成适配后的场景...
"""
    
    print("\n=== 婴幼儿场景适配演示 ===\n")
    
    # 模拟适配结果
    mock_adaptation_result = {
        "scenario_background": "12-18个月的宝宝第一次去游乐园，面对新环境和刺激活动时的情绪反应",
        "infant_behaviors": {
            "initial_behavior": "看到游乐设施时眼睛发亮，兴奋地拍手，想要靠近",
            "emotion_progression": [
                "兴奋探索：指着旋转木马，发出兴奋的声音",
                "紧张不安：听到音乐和人群声音，开始抓紧妈妈的衣服",
                "寻求安慰：回头看妈妈，表情有些不安",
                "逐渐适应：在妈妈的安抚下，重新表现出好奇"
            ],
            "physical_manifestations": ["拍手", "指向", "抓握", "回头张望", "身体紧贴养育者"]
        },
        "caregiver_guidance": {
            "observation_points": [
                "注意宝宝的面部表情变化",
                "观察身体语言和姿态",
                "识别寻求安慰的信号"
            ],
            "response_strategies": [
                "用温和的声音描述环境：'宝宝看到了旋转木马，很漂亮对不对？'",
                "提供身体安慰：轻抚背部，给予拥抱",
                "渐进式接近：先在远处观察，再慢慢靠近",
                "情绪命名：'宝宝有点紧张是吗？妈妈在这里，很安全的'"
            ],
            "developmental_goals": [
                "帮助宝宝适应新环境",
                "建立安全感和信任",
                "促进情绪调节能力发展",
                "支持探索行为"
            ]
        },
        "professional_insights": {
            "attachment_theory": "通过及时回应宝宝的情绪信号，强化安全依恋关系",
            "emotional_development": "12-18个月是情绪表达和调节能力快速发展的关键期",
            "environmental_adaptation": "新环境适应是社会情绪发展的重要组成部分"
        }
    }
    
    print("适配结果示例:")
    print(json.dumps(mock_adaptation_result, ensure_ascii=False, indent=2))
    
    return mock_adaptation_result

def main():
    """主演示函数"""
    print("🤖 基于Qwen API的婴幼儿社会情绪发展数据集构建演示\n")
    
    # 1. 情感分析演示
    emotion_analysis = demo_emotion_analysis()
    
    # 2. 婴幼儿适配演示
    infant_adaptation = demo_infant_adaptation()
    
    print("\n=== 使用说明 ===")
    print("1. 首先运行: python3 qwen_api_config.py 设置API密钥")
    print("2. 然后运行: python3 qwen_enhanced_dataset_builder.py 构建数据集")
    print("3. 数据集将包含:")
    print("   - 原始对话的情感分析")
    print("   - 适配的婴幼儿场景")
    print("   - 专业的指导策略")
    print("   - 发展性评估要点")
    
    print("\n=== 预期效果 ===")
    print("✅ 高质量的情感分析和指导策略提取")
    print("✅ 专业的婴幼儿场景适配")
    print("✅ 基于理论的发展性指导")
    print("✅ 适合LLM训练的结构化数据")

if __name__ == "__main__":
    main()
