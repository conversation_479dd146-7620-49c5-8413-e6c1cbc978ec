# 基于Qwen API的婴幼儿社会情绪发展数据集构建指南

## 🎯 项目概述

本项目结合Qwen API的强大分析能力，对您现有的对话数据集进行深度分析和转换，构建专业的婴幼儿社会情绪发展指导数据集。

### 核心功能
1. **情感轨迹分析**: 分析对话中孩子的情感变化过程
2. **指导策略提取**: 识别智能助手使用的情绪指导技巧
3. **婴幼儿场景适配**: 将学龄儿童场景转换为0-3岁婴幼儿场景
4. **专业知识增强**: 融入发育心理学和早期干预理论

## 🚀 快速开始

### 1. 环境准备
```bash
# 确保Python环境
python3 --version

# 安装依赖
pip install requests
```

### 2. API密钥设置
```bash
# 运行配置向导
python3 qwen_api_config.py
```

按提示输入您的Qwen API密钥（从[阿里云DashScope](https://dashscope.console.aliyun.com/)获取）

### 3. 查看演示
```bash
# 运行演示，了解分析效果
python3 demo_qwen_analysis.py
```

### 4. 构建数据集
```bash
# 开始构建增强数据集
python3 qwen_enhanced_dataset_builder.py
```

## 📊 数据处理流程

### 输入数据格式
您的 `child_chat_data.json` 应包含对话数据，支持以下格式：

```json
{
  "topic": "情绪管理",
  "input": "完整的对话文本...",
  "dialog": [
    {"speaker": "child", "text": "孩子的话"},
    {"speaker": "assistant", "text": "助手的回应"}
  ]
}
```

### 处理步骤

#### 第1步：情感分析
Qwen API分析每个对话中的：
- **情感轨迹**: 初始情绪 → 变化过程 → 最终情绪
- **触发因素**: 导致情绪变化的关键因素
- **调节策略**: 智能助手使用的指导技巧

#### 第2步：婴幼儿适配
将学龄儿童场景转换为婴幼儿场景：
- **年龄特点**: 考虑0-3岁的发育水平
- **表达方式**: 从语言转为行为表达
- **场景调整**: 适配为典型的婴幼儿生活场景
- **指导策略**: 调整为养育者指导方式

#### 第3步：专业增强
融入专业知识：
- **理论基础**: 依恋理论、情绪指导理论等
- **评估维度**: 基于ASQ:SE-2等标准化工具
- **发展目标**: 明确的发展性指导目标

### 输出数据结构
```json
{
  "scenario_id": "qwen_enhanced_12-18m_1234",
  "original_topic": "情绪管理",
  "target_age": "12-18m",
  "emotion_analysis": {
    "emotion_trajectory": {...},
    "guidance_strategies": {...},
    "developmental_elements": {...}
  },
  "infant_scenario": {
    "scenario_background": "...",
    "infant_behaviors": {...},
    "caregiver_guidance": {...},
    "professional_insights": {...}
  }
}
```

## 🔧 配置选项

### API参数调整
编辑 `qwen_config.json`:
```json
{
  "api_key": "your_api_key",
  "model": "qwen-turbo",
  "parameters": {
    "temperature": 0.7,
    "max_tokens": 2000,
    "top_p": 0.8
  }
}
```

### 构建参数
在 `qwen_enhanced_dataset_builder.py` 中调整：
```python
builder.build_dataset(
    input_file="child_chat_data.json",
    output_file="qwen_enhanced_infant_emotion_dataset.json",
    target_ages=["0-6m", "6-12m", "12-18m", "18-24m", "24-36m"],
    max_samples=100  # 调整样本数量
)
```

## 📈 质量控制

### 1. API调用优化
- **频率控制**: 每次调用间隔2秒，避免超限
- **错误处理**: 自动重试和错误记录
- **结果验证**: JSON格式验证和内容检查

### 2. 内容质量保证
- **专业性**: 基于权威理论和标准
- **适龄性**: 严格按年龄段特点适配
- **一致性**: 统一的数据结构和标准

### 3. 人工审核建议
- **专家评估**: 邀请儿童发展专家审核
- **临床验证**: 在实际应用中验证效果
- **持续改进**: 根据反馈持续优化

## 💡 使用技巧

### 1. 提示词优化
根据您的具体需求，可以调整提示词模板：
- 增加特定的理论框架要求
- 强调某些发展维度
- 调整专业术语的使用

### 2. 批量处理
对于大量数据：
```python
# 分批处理，避免长时间运行
for batch in data_batches:
    process_batch(batch)
    time.sleep(60)  # 批次间休息
```

### 3. 结果后处理
```python
# 过滤和清理结果
filtered_results = [
    result for result in results 
    if result and validate_result(result)
]
```

## 🔍 常见问题

### Q1: API调用失败怎么办？
- 检查API密钥是否正确
- 确认网络连接正常
- 查看API配额是否充足

### Q2: 生成的内容质量不理想？
- 调整提示词模板
- 修改API参数（temperature等）
- 增加人工审核环节

### Q3: 处理速度太慢？
- 减少样本数量进行测试
- 优化API调用频率
- 考虑并行处理（注意API限制）

## 📊 预期效果

### 数据集特点
- **高质量**: 基于Qwen API的深度分析
- **专业性**: 融入多个专业理论框架
- **适龄性**: 严格按婴幼儿发育特点设计
- **实用性**: 可直接用于LLM训练和应用

### 应用价值
1. **模型训练**: 训练专业的婴幼儿发育指导模型
2. **临床应用**: 支持早期筛查和干预
3. **家长教育**: 提供科学的育儿指导
4. **研究支撑**: 为学术研究提供高质量数据

## 📞 技术支持

如遇到问题，请：
1. 查看错误日志和输出信息
2. 检查配置文件和API设置
3. 参考演示代码和文档
4. 提交Issue描述具体问题

---

*通过结合Qwen API的强大能力和专业的婴幼儿发展知识，我们可以构建出高质量、专业性强的社会情绪发展指导数据集，为AI在早期儿童发展领域的应用奠定坚实基础。*
