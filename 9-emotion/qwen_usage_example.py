#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen API使用示例
展示如何使用Qwen API分析您的对话数据并生成婴幼儿场景
"""

import json
from qwen_enhanced_dataset_builder import EmotionGuidanceDatasetBuilder

def demo_with_your_data():
    """使用您的实际数据进行演示"""
    
    print("=== 基于您的数据的Qwen API演示 ===\n")
    
    # 读取您的数据
    with open('child_chat_data.json', 'r', encoding='utf-8') as f:
        your_data = json.load(f)
    
    print(f"加载了 {len(your_data)} 个对话样本")
    
    # 选择一个示例进行分析
    sample = your_data[0]  # 第一个样本：去动物园
    
    print(f"\n原始对话话题: {sample['topic']}")
    print(f"原始对话内容预览: {sample['input'][:100]}...")
    
    # 模拟Qwen API分析结果（实际使用时会调用真实API）
    print("\n=== 模拟Qwen API分析过程 ===")
    
    # 1. 情感分析
    print("\n1. 情感轨迹分析:")
    emotion_analysis = {
        "emotion_trajectory": {
            "initial_emotion": "兴奋期待",
            "emotion_changes": [
                {"stage": "开始", "emotion": "兴奋", "description": "对去动物园充满期待"},
                {"stage": "中期", "emotion": "担忧", "description": "担心下雨影响计划"},
                {"stage": "结束", "emotion": "安心", "description": "得到解决方案后放心"}
            ],
            "final_emotion": "满意感谢",
            "emotion_triggers": ["期待动物园", "天气担忧", "获得建议"]
        },
        "guidance_strategies": {
            "techniques_used": ["情绪确认", "问题解决", "替代方案提供"],
            "intervention_points": ["识别担忧", "提供解决方案", "增强信心"],
            "effectiveness_indicators": ["情绪转变", "问题解决", "表达感谢"]
        }
    }
    
    for change in emotion_analysis["emotion_trajectory"]["emotion_changes"]:
        print(f"  {change['stage']}: {change['emotion']} - {change['description']}")
    
    # 2. 婴幼儿场景适配
    print("\n2. 婴幼儿场景适配 (12-18个月):")
    infant_scenario = {
        "scenario_background": "12-18个月的宝宝第一次去动物园的情绪反应",
        "infant_behaviors": {
            "initial_behavior": "看到动物时眼睛发亮，兴奋地指向动物",
            "emotion_progression": [
                "兴奋探索：指着动物，发出兴奋的声音'啊啊'",
                "好奇观察：专注地看着动物的动作",
                "轻微不安：听到动物叫声时身体靠近妈妈",
                "适应接受：在妈妈的安抚下重新表现出兴趣"
            ],
            "physical_manifestations": ["指向", "发声", "身体靠近养育者", "专注凝视"]
        },
        "caregiver_guidance": {
            "observation_points": [
                "注意宝宝的指向行为和发声",
                "观察对新环境的适应过程",
                "识别寻求安全感的信号"
            ],
            "response_strategies": [
                "跟随宝宝的视线，描述看到的动物：'宝宝看到小猴子了！'",
                "提供身体安慰：轻抚背部，给予拥抱",
                "适度引导：'我们慢慢走近看看小兔子'",
                "情绪命名：'宝宝很兴奋是吗？这些动物很有趣对不对？'"
            ],
            "developmental_goals": [
                "促进语言发展（动物名称学习）",
                "支持探索行为和好奇心",
                "建立对新环境的适应能力",
                "增强亲子互动和依恋关系"
            ]
        }
    }
    
    print(f"  场景背景: {infant_scenario['scenario_background']}")
    print(f"  初始行为: {infant_scenario['infant_behaviors']['initial_behavior']}")
    print("  情绪发展过程:")
    for progression in infant_scenario['infant_behaviors']['emotion_progression']:
        print(f"    - {progression}")
    
    # 3. 专业增强
    print("\n3. 专业知识增强:")
    professional_enhancement = {
        "theoretical_framework": {
            "attachment_theory": "通过及时回应宝宝的探索信号，强化安全依恋关系",
            "cognitive_development": "12-18个月是符号思维和语言发展的关键期",
            "environmental_adaptation": "新环境探索有助于认知发展和适应能力提升"
        },
        "assessment_dimensions": [
            "社会情绪发展：观察对新环境的适应能力",
            "认知发展：注意力集中时间和探索兴趣",
            "语言发展：指向行为和发声表达",
            "运动发展：行走稳定性和协调性"
        ],
        "intervention_strategies": [
            "环境适应支持：渐进式接触，从远到近",
            "语言输入丰富：描述性语言，动物名称教学",
            "情绪调节支持：识别并回应情绪信号",
            "探索行为鼓励：安全范围内的自主探索"
        ]
    }
    
    print("  理论基础:")
    for theory, description in professional_enhancement["theoretical_framework"].items():
        print(f"    {theory}: {description}")
    
    # 4. 最终整合结果
    print("\n=== 生成的完整数据样本 ===")
    final_result = {
        "scenario_id": "qwen_enhanced_12-18m_demo",
        "original_topic": sample['topic'],
        "target_age": "12-18m",
        "emotion_analysis": emotion_analysis,
        "infant_scenario": infant_scenario,
        "professional_enhancement": professional_enhancement,
        "generation_method": "qwen_api_enhanced"
    }
    
    print(json.dumps(final_result, ensure_ascii=False, indent=2)[:500] + "...")
    
    return final_result

def show_api_setup_guide():
    """显示API设置指南"""
    print("\n=== Qwen API 设置指南 ===")
    print("1. 获取API密钥:")
    print("   - 访问 https://dashscope.console.aliyun.com/")
    print("   - 注册/登录阿里云账号")
    print("   - 开通DashScope服务")
    print("   - 获取API Key")
    
    print("\n2. 设置API密钥:")
    print("   - 运行: python3 qwen_api_config.py")
    print("   - 输入您的API密钥")
    print("   - 测试连接")
    
    print("\n3. 开始构建数据集:")
    print("   - 运行: python3 qwen_enhanced_dataset_builder.py")
    print("   - 等待处理完成")
    print("   - 查看生成的数据集文件")

def estimate_api_cost():
    """估算API调用成本"""
    print("\n=== API调用成本估算 ===")
    
    # 假设参数
    total_samples = 100  # 要处理的样本数
    calls_per_sample = 3  # 每个样本需要3次API调用
    tokens_per_call = 1500  # 每次调用平均token数
    
    total_calls = total_samples * calls_per_sample
    total_tokens = total_calls * tokens_per_call
    
    # Qwen-turbo价格（示例，请查看最新价格）
    price_per_1k_tokens = 0.008  # 元/1K tokens
    estimated_cost = (total_tokens / 1000) * price_per_1k_tokens
    
    print(f"处理 {total_samples} 个样本的估算:")
    print(f"  总API调用次数: {total_calls}")
    print(f"  总Token消耗: {total_tokens:,}")
    print(f"  估算成本: ¥{estimated_cost:.2f}")
    print(f"  平均每个样本: ¥{estimated_cost/total_samples:.3f}")
    
    print("\n注意:")
    print("- 实际成本可能因对话长度和API响应而有所不同")
    print("- 建议先用少量样本测试")
    print("- 可以调整batch_size来控制成本")

if __name__ == "__main__":
    print("🤖 Qwen API 婴幼儿数据集构建演示\n")
    
    # 1. 演示数据分析
    try:
        demo_result = demo_with_your_data()
        print("✅ 演示完成！")
    except FileNotFoundError:
        print("❌ 未找到 child_chat_data.json 文件")
        print("请确保文件在当前目录下")
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
    
    # 2. 显示设置指南
    show_api_setup_guide()
    
    # 3. 成本估算
    estimate_api_cost()
    
    print("\n=== 下一步操作 ===")
    print("1. 设置Qwen API密钥")
    print("2. 运行完整的数据集构建")
    print("3. 检查生成的数据质量")
    print("4. 根据需要调整参数和提示词")
