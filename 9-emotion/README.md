# 婴幼儿社会情绪发展指导文本数据集

基于PEC（Parental Emotion Coaching）理论框架，构建的专门针对0-3岁婴幼儿社会情绪发展指导的高质量文本数据集。

## 📊 数据集概览

- **总样本数**: 600个专业场景
- **年龄覆盖**: 0-36个月，按发育阶段细分
- **发展领域**: 情绪、社交、认知、运动、语言五大维度
- **场景类型**: 日常护理、发育里程碑、问题行为、发育评估
- **专业性**: 融合ASQ:SE-2、《婴幼儿早期发展服务指南》等权威资源

## 🎯 核心特色

### 1. 科学的年龄分层
```
0-1个月   (新生儿期)     - 100个样本
1-6个月   (婴儿早期)     - 100个样本  
6-12个月  (婴儿晚期)     - 100个样本
12-18个月 (幼儿早期)     - 100个样本
18-24个月 (幼儿中期)     - 100个样本
24-36个月 (幼儿晚期)     - 100个样本
```

### 2. 全面的发展维度
- **情绪发展**: 情绪识别、表达、调节能力
- **社会交往**: 依恋关系、陌生人反应、同伴互动
- **认知发展**: 感知觉、记忆、思维发展
- **运动发展**: 大运动、精细动作协调
- **语言发展**: 理解、表达、交流能力

### 3. 丰富的数据结构
每个样本包含：
- **基础信息**: 年龄段、发展领域、场景类型
- **情境描述**: 环境设置、参与者、背景信息
- **行为观察**: 可观察行为、情绪状态、发育指标
- **专业指导**: 多轮对话、指导策略、技巧建议
- **教育内容**: 学习目标、后续建议、家长教育要点
- **评估维度**: 专业评估标准和质量控制

## 📁 文件结构

```
├── 核心数据集
│   ├── infant_emotion_guidance_dataset.json      # 基础数据集
│   └── enhanced_infant_emotion_dataset.json      # 增强版数据集
│
├── 专门化数据集
│   ├── emotion_coaching_dataset.json             # 情绪指导专用
│   ├── developmental_assessment_dataset.json     # 发育评估专用
│   ├── parent_education_dataset.json             # 家长教育专用
│   └── professional_training_dataset.json        # 专业培训专用
│
├── LLM训练格式
│   ├── infant_guidance_instruction_data.json     # 指令微调格式
│   └── infant_guidance_conversation_data.json    # 对话训练格式
│
├── 工具脚本
│   ├── generate_infant_dataset.py                # 数据生成器
│   ├── enhance_infant_dataset.py                 # 数据增强器
│   ├── analyze_dataset.py                        # 分析工具
│   └── usage_example.py                          # 使用示例
│
└── 文档
    ├── infant_emotion_dataset_plan.md            # 构建方案
    ├── dataset_analysis_report.md                # 分析报告
    └── README.md                                  # 本文档
```

## 🚀 快速开始

### 1. 数据集使用
```python
import json

# 加载数据集
with open('enhanced_infant_emotion_dataset.json', 'r', encoding='utf-8') as f:
    dataset = json.load(f)

# 查看样本结构
sample = dataset[0]
print(f"年龄段: {sample['age_range']}")
print(f"发展领域: {sample['development_domain']}")
print(f"观察行为: {sample['infant_behavior']['observable_signs']}")
```

### 2. 运行分析工具
```bash
python3 analyze_dataset.py
```

### 3. 查看使用示例
```bash
python3 usage_example.py
```

## 💡 应用场景

### 1. LLM模型训练
- **指令微调**: 训练专业的婴幼儿发育指导模型
- **对话系统**: 构建智能育儿助手
- **知识问答**: 开发专业咨询系统

### 2. 专业应用
- **发育评估**: 支持早期筛查和评估
- **干预指导**: 提供个性化干预建议
- **家长教育**: 科学育儿知识普及

### 3. 研究支撑
- **学术研究**: 为相关研究提供数据基础
- **临床应用**: 支持临床实践和验证
- **政策制定**: 为政策制定提供参考

## 📈 数据质量

### 统计指标
- **行为描述多样性**: 39种独特行为描述
- **平均对话轮数**: 4.1轮专业指导
- **内容完整性**: 100%包含评估维度和家长教育内容
- **专业覆盖度**: 融合发育心理学、早期干预等多个专业领域

### 质量保证
- **理论基础**: 基于PEC理论和ASQ:SE-2标准
- **专业审核**: 建议专家审核和临床验证
- **持续更新**: 根据最新研究成果持续改进

## 🔧 技术实现

### 数据生成流程
1. **基础适配**: 将学龄儿童场景适配为婴幼儿场景
2. **专业增强**: 添加发育里程碑和专业指导
3. **质量控制**: 多维度评估和一致性检查
4. **格式转换**: 生成多种训练格式

### 核心算法
- **场景映射**: 智能识别和转换原始场景
- **行为生成**: 基于年龄特点生成合适行为
- **指导策略**: 融合多种专业指导方法
- **质量评估**: 多维度质量控制机制

## 📚 理论基础

### 核心理论框架
- **PEC理论**: Parental Emotion Coaching父母情绪指导
- **依恋理论**: 安全依恋关系的建立和维护
- **发育心理学**: 婴幼儿认知和情绪发展规律
- **早期干预**: 基于证据的早期干预策略

### 参考标准
- **ASQ:SE-2**: 国际标准化社会情绪发展评估工具
- **《婴幼儿早期发展服务指南》**: 国家卫健委官方指导文件
- **《3-6岁儿童学习与发展指南》**: 教育部发布的发展指南

## 🤝 贡献指南

### 数据质量改进
1. **专家审核**: 邀请儿童发展专家审核内容
2. **临床验证**: 在实际应用中验证效果
3. **用户反馈**: 收集使用者的反馈和建议

### 功能扩展
1. **多模态数据**: 添加图像、视频等多模态内容
2. **个性化定制**: 根据不同文化背景进行定制
3. **实时更新**: 根据最新研究成果持续更新

## 📄 许可证

本数据集仅供学术研究和非商业用途使用。使用时请遵循以下原则：
- 保护儿童隐私和权益
- 确保内容的科学性和安全性
- 引用时请注明数据来源

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 技术问题：提交Issue或Pull Request
- 学术合作：联系项目维护者
- 商业咨询：请通过正式渠道联系

---

*本数据集致力于推动婴幼儿社会情绪发展领域的研究和应用，为每个孩子的健康成长贡献力量。*
