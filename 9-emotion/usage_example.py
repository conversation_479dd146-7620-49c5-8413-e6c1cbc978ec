#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿社会情绪发展指导数据集使用示例
展示如何使用数据集进行模型训练、评估和应用
"""

import json
import random
from typing import Dict, List, Any

class InfantGuidanceSystem:
    def __init__(self, dataset_path: str):
        """初始化指导系统"""
        with open(dataset_path, 'r', encoding='utf-8') as f:
            self.dataset = json.load(f)
        
        # 按年龄和领域建立索引
        self.age_index = {}
        self.domain_index = {}
        
        for item in self.dataset:
            age = item['age_range']
            domain = item['development_domain']
            
            if age not in self.age_index:
                self.age_index[age] = []
            self.age_index[age].append(item)
            
            if domain not in self.domain_index:
                self.domain_index[domain] = []
            self.domain_index[domain].append(item)

    def get_guidance_by_age(self, age: str, limit: int = 5) -> List[Dict]:
        """根据年龄获取指导建议"""
        if age in self.age_index:
            return random.sample(self.age_index[age], min(limit, len(self.age_index[age])))
        return []

    def get_guidance_by_domain(self, domain: str, limit: int = 5) -> List[Dict]:
        """根据发展领域获取指导建议"""
        if domain in self.domain_index:
            return random.sample(self.domain_index[domain], min(limit, len(self.domain_index[domain])))
        return []

    def search_by_behavior(self, behavior: str, limit: int = 5) -> List[Dict]:
        """根据行为描述搜索相关指导"""
        matches = []
        for item in self.dataset:
            behaviors = item['infant_behavior']['observable_signs']
            if any(behavior in b for b in behaviors):
                matches.append(item)
        
        return random.sample(matches, min(limit, len(matches)))

    def generate_training_data(self, format_type: str = "instruction") -> List[Dict]:
        """生成训练数据"""
        training_data = []
        
        for item in self.dataset:
            if format_type == "instruction":
                # 指令微调格式
                context = f"年龄：{item['age_range']}，发展领域：{item['development_domain']}"
                context += f"，场景：{item['context']['background']}"
                context += f"，观察到的行为：{', '.join(item['infant_behavior']['observable_signs'])}"
                
                instruction = "请为这个婴幼儿发展场景提供专业的指导建议。"
                
                response = ""
                for dialogue in item['guidance_dialogue']:
                    if dialogue['speaker'] == 'system':
                        response += dialogue['content'] + " "
                
                training_data.append({
                    "instruction": instruction,
                    "input": context,
                    "output": response.strip()
                })
                
            elif format_type == "conversation":
                # 对话格式
                conversation = []
                for dialogue in item['guidance_dialogue']:
                    role = "assistant" if dialogue['speaker'] == 'system' else "user"
                    conversation.append({
                        "role": role,
                        "content": dialogue['content']
                    })
                
                training_data.append({
                    "conversation": conversation,
                    "metadata": {
                        "age_range": item['age_range'],
                        "domain": item['development_domain'],
                        "scenario_type": item['scenario_type']
                    }
                })
        
        return training_data

    def demonstrate_usage(self):
        """演示系统使用方法"""
        print("=== 婴幼儿社会情绪发展指导系统演示 ===\n")
        
        # 1. 按年龄查询
        print("1. 按年龄查询指导建议")
        print("查询：6-12个月婴儿的指导建议")
        guidance = self.get_guidance_by_age("6-12m", 2)
        for i, item in enumerate(guidance, 1):
            print(f"\n场景 {i}:")
            print(f"  背景: {item['context']['background']}")
            print(f"  观察行为: {', '.join(item['infant_behavior']['observable_signs'])}")
            print(f"  指导建议:")
            for dialogue in item['guidance_dialogue'][:2]:  # 只显示前两轮
                if dialogue['speaker'] == 'system':
                    print(f"    - {dialogue['content']}")
        
        # 2. 按发展领域查询
        print("\n" + "="*50)
        print("2. 按发展领域查询指导建议")
        print("查询：情绪发展相关指导")
        guidance = self.get_guidance_by_domain("emotion", 2)
        for i, item in enumerate(guidance, 1):
            print(f"\n场景 {i}:")
            print(f"  年龄段: {item['age_range']}")
            print(f"  情绪状态: {item['infant_behavior']['emotional_state']}")
            print(f"  学习目标: {', '.join(item['learning_objectives'])}")
        
        # 3. 按行为搜索
        print("\n" + "="*50)
        print("3. 按行为描述搜索")
        print("搜索：包含'哭闹'的场景")
        guidance = self.search_by_behavior("哭闹", 1)
        if guidance:
            item = guidance[0]
            print(f"\n找到场景:")
            print(f"  年龄段: {item['age_range']}")
            print(f"  场景类型: {item['scenario_type']}")
            print(f"  后续建议: {', '.join(item['follow_up_suggestions'])}")
        
        # 4. 生成训练数据示例
        print("\n" + "="*50)
        print("4. 训练数据格式示例")
        training_sample = self.generate_training_data("instruction")[0]
        print(f"指令: {training_sample['instruction']}")
        print(f"输入: {training_sample['input'][:100]}...")
        print(f"输出: {training_sample['output'][:100]}...")

def create_specialized_datasets():
    """创建专门化的数据集"""
    system = InfantGuidanceSystem("enhanced_infant_emotion_dataset.json")
    
    # 创建不同用途的数据集
    datasets = {
        "emotion_coaching": [],
        "developmental_assessment": [],
        "parent_education": [],
        "professional_training": []
    }
    
    for item in system.dataset:
        # 情绪指导数据集
        if item['development_domain'] == 'emotion':
            datasets["emotion_coaching"].append(item)
        
        # 发育评估数据集
        if item['scenario_type'] == 'assessment':
            datasets["developmental_assessment"].append(item)
        
        # 家长教育数据集
        if 'parent_education' in item:
            datasets["parent_education"].append(item)
        
        # 专业培训数据集
        if 'assessment_dimensions' in item:
            datasets["professional_training"].append(item)
    
    # 保存专门化数据集
    for name, data in datasets.items():
        if data:
            with open(f"{name}_dataset.json", 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"已创建 {name} 数据集，包含 {len(data)} 个样本")

def export_for_llm_training():
    """导出用于LLM训练的格式"""
    system = InfantGuidanceSystem("enhanced_infant_emotion_dataset.json")
    
    # 生成指令微调格式
    instruction_data = system.generate_training_data("instruction")
    with open("infant_guidance_instruction_data.json", 'w', encoding='utf-8') as f:
        json.dump(instruction_data, f, ensure_ascii=False, indent=2)
    
    # 生成对话格式
    conversation_data = system.generate_training_data("conversation")
    with open("infant_guidance_conversation_data.json", 'w', encoding='utf-8') as f:
        json.dump(conversation_data, f, ensure_ascii=False, indent=2)
    
    print(f"已导出指令微调数据: {len(instruction_data)} 条")
    print(f"已导出对话数据: {len(conversation_data)} 条")

if __name__ == "__main__":
    # 演示系统使用
    system = InfantGuidanceSystem("enhanced_infant_emotion_dataset.json")
    system.demonstrate_usage()
    
    print("\n" + "="*50)
    print("5. 创建专门化数据集")
    create_specialized_datasets()
    
    print("\n" + "="*50)
    print("6. 导出LLM训练格式")
    export_for_llm_training()
    
    print("\n=== 演示完成 ===")
    print("数据集已准备就绪，可用于:")
    print("1. LLM模型训练和微调")
    print("2. 婴幼儿发育评估系统开发")
    print("3. 家长教育应用构建")
    print("4. 专业人员培训平台")
    print("5. 学术研究和临床应用")
