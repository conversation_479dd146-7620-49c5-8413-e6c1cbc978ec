# CribNet图像数据集转文本数据集工具

## 项目简介

本工具专门用于将CribNet图像数据集转换为婴幼儿家庭场景运动监测的文本数据集。CribNet是一个专注于婴儿床安全监测的计算机视觉数据集，包含玩具检测(CRIBHD-T)、毯子检测(CRIBHD-B)和组合场景(CRIBHD-C)三个子集。

## 功能特点

- 🔄 **多模态转换**: 支持GPT-4V、Gemini和本地模型进行图像到文本转换
- 📊 **结构化输出**: 生成包含发育评估、安全风险分析的结构化文本描述
- 🎯 **专业标准**: 基于国家卫健委发育里程碑和WHO标准进行评估
- 📈 **多格式导出**: 支持JSON、CSV、训练格式等多种输出格式
- 📋 **统计分析**: 自动生成数据集统计报告和质量分析

## 安装要求

### 基础依赖
```bash
pip install opencv-python numpy pandas pathlib argparse datetime
```

### 可选依赖（根据使用的模型选择）
```bash
# GPT-4V支持
pip install openai

# Gemini支持
pip install google-generativeai

# 图像处理
pip install pillow

# 配置文件支持
pip install pyyaml
```

## 数据集准备

### 1. 下载CribHD数据集
从[CribHD官方网站](https://coe.northeastern.edu/Research/AClab/CribHD/)下载数据集

### 2. 数据集结构
确保数据集按以下结构组织：
```
CribHD/
├── CRIBHD-B/ (毯子数据)
│   ├── COCO/
│   │   ├── train/
│   │   │   ├── images/
│   │   │   └── _annotations.coco.json
│   │   ├── test/
│   │   └── valid/
│   ├── PascalVOC/
│   └── YOLOv8/
├── CRIBHD-T/ (玩具数据)
│   └── (同CRIBHD-B结构)
└── CRIBHD-C/ (组合场景)
    └── images/
```

## 使用方法

### 1. 基础使用
```bash
python cribnet_to_text_converter.py \
    --dataset_path /path/to/CribHD \
    --output_path /path/to/output \
    --model_type local
```

### 2. 使用GPT-4V
```bash
python cribnet_to_text_converter.py \
    --dataset_path /path/to/CribHD \
    --output_path /path/to/output \
    --model_type gpt4v \
    --api_key your_openai_api_key
```

### 3. 使用Gemini
```bash
python cribnet_to_text_converter.py \
    --dataset_path /path/to/CribHD \
    --output_path /path/to/output \
    --model_type gemini \
    --api_key your_gemini_api_key
```

### 4. 处理特定子集
```bash
# 只处理毯子数据
python cribnet_to_text_converter.py \
    --dataset_path /path/to/CribHD \
    --output_path /path/to/output \
    --subset cribhd_b \
    --max_images 100
```

### 5. 导出多种格式
```bash
python cribnet_to_text_converter.py \
    --dataset_path /path/to/CribHD \
    --output_path /path/to/output \
    --export_formats
```

## 配置文件使用

### 1. 修改配置文件
编辑`config.yaml`文件，设置数据集路径、模型参数等：

```yaml
dataset:
  source_path: "/path/to/CribHD"
  output_path: "/path/to/output"
  subset: "all"

model:
  type: "gpt4v"
  api_key: "your_api_key"
```

### 2. 使用配置文件运行
```bash
python3 cribnet_to_text_converter.py --config config.yaml
```

## 输出格式

### 1. JSON格式
```json
{
  "metadata": {
    "source_dataset": "CribHD",
    "conversion_time": "2024-01-01T12:00:00",
    "total_images": 1000
  },
  "data": [
    {
      "image_id": "train00001",
      "estimated_age_months": 8,
      "motor_skills": ["坐立", "抓握"],
      "safety_assessment": {
        "risks_detected": ["小玩具"],
        "recommendations": ["移除小物品"]
      },
      "raw_description": "8个月大的婴儿独立坐在婴儿床中..."
    }
  ]
}
```

### 2. CSV格式
包含图像ID、年龄估计、运动技能、安全风险等字段的表格数据

### 3. 训练格式
按train/valid/test分割的机器学习训练数据

## 文本描述示例

### 基础描述
```
这是一张8个月大婴儿在婴儿床中的图像。婴儿能够独立坐立，显示出良好的躯干控制能力。
婴儿正在用双手抓握一个彩色玩具，展现了钳形抓握的精细动作技能。
```

### 发育评估
```
发育评估：
- 大运动：独立坐立符合6-12个月发育里程碑
- 精细动作：钳形抓握表明手眼协调发育正常
- 认知发展：对玩具的探索行为显示好奇心和学习能力
```

### 安全风险分析
```
安全风险评估：
- 检测到小型玩具部件，存在潜在窒息风险
- 建议：移除小于3.17cm的玩具部件
- 毯子位置适当，未覆盖面部
```

## 质量控制

### 1. 自动质量检查
- 描述长度验证
- 关键词一致性检查
- 年龄估计合理性验证

### 2. 统计分析
- 年龄分布统计
- 运动技能频次分析
- 安全风险类型统计

### 3. 人工审核
- 支持随机抽样人工审核
- 质量评分和反馈机制

## 扩展功能

### 1. 自定义提示词
可以修改`_build_prompt`方法来自定义图像描述生成的提示词模板

### 2. 添加新的评估维度
在配置文件中添加新的发育里程碑或安全标准

### 3. 集成其他模型
可以扩展`_generate_with_local_model`方法来集成BLIP、CLIP等本地模型

## 注意事项

### 1. API使用限制
- GPT-4V和Gemini需要API密钥，可能产生费用
- 注意API调用频率限制

### 2. 数据隐私
- 确保婴幼儿图像数据的隐私保护
- 遵守相关数据保护法规

### 3. 专业性声明
- 生成的文本描述仅供研究参考
- 不能替代专业医疗评估

## 技术支持

如有问题或建议，请联系：
- 邮箱：<EMAIL>
- GitHub Issues：[项目地址]

## 许可证

本项目仅供学术研究使用，商业使用请联系作者获得授权。
