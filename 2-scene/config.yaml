# CribNet图像转文本数据集配置文件

# 数据集配置
dataset:
  # CribHD数据集路径
  source_path: "/Users/<USER>/zju/code/2-scene/CribHD"
  # 输出文本数据集路径
  output_path: "/Users/<USER>/zju/code/2-scene/output/cribhd_text_dataset"
  
  # 要处理的子集 (all, cribhd_b, cribhd_t, cribhd_c)
  subset: "all"
  
  # 最大处理图像数量 (null表示处理所有)
  max_images: null
  
  # 是否导出多种格式
  export_formats: true

# 模型配置
model:
  # 模型类型: qwen, gpt4v, gemini, local
  type: "qwen"
  
  # API配置 (仅当使用qwen、gpt4v或gemini时需要)
  api_key: sk-5eba46fbcff649d5bf28313bc865de10

  # Qwen配置
  qwen:
    model_name: "qwen-vl-plus-0809"  # 图像处理需要多模态模型 (qwen-vl-plus 或 qwen-vl-max)
    max_tokens: 2000
    temperature: 0.3
    # 可选的HTTP API配置
    use_http_api: false
    api_url: "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"

  # GPT-4V配置
  gpt4v:
    model_name: "gpt-4-vision-preview"
    max_tokens: 1000
    temperature: 0.3

  # Gemini配置
  gemini:
    model_name: "gemini-pro-vision"
    temperature: 0.3
  
  # 本地模型配置
  local:
    # 可以配置本地模型路径等
    model_path: null

# 文本生成配置
text_generation:
  # 描述语言
  language: "zh"
  
  # 描述详细程度 (basic, detailed, comprehensive)
  detail_level: "detailed"
  
  # 是否包含发育评估
  include_development_assessment: true
  
  # 是否包含安全风险评估
  include_safety_assessment: true
  
  # 自定义提示词模板
  custom_prompt_template: null

# 婴幼儿发育里程碑配置
development_milestones:
  age_groups:
    "0-3months":
      gross_motor: ["抬头", "俯卧时头部稍微抬起", "仰卧时头部转向一侧"]
      fine_motor: ["握拳", "手指偶尔张开", "触碰反射"]
      cognitive: ["注视人脸", "跟踪移动物体", "对声音有反应"]
      social: ["对熟悉声音有反应", "短暂注视人脸", "被安抚时安静下来"]
    
    "3-6months":
      gross_motor: ["翻身", "俯卧时用前臂支撑", "坐立需要支撑", "头部控制稳定"]
      fine_motor: ["抓握玩具", "双手互相触碰", "将物品放入口中", "摇晃玩具"]
      cognitive: ["认识熟悉的人", "对镜子中的自己感兴趣", "模仿表情"]
      social: ["社交性微笑", "咿呀学语", "对陌生人表现出好奇"]
    
    "6-12months":
      gross_motor: ["独立坐立", "爬行", "扶站", "迈步", "从坐位到爬行"]
      fine_motor: ["钳形抓握", "敲击物品", "撕纸", "指点", "拍手"]
      cognitive: ["物体永恒性", "模仿动作", "理解简单指令", "探索因果关系"]
      social: ["认生", "挥手再见", "玩躲猫猫", "模仿简单动作"]
    
    "12-24months":
      gross_motor: ["独立行走", "跑步", "上下楼梯", "踢球", "向后走"]
      fine_motor: ["搭积木", "涂鸦", "翻书页", "使用勺子", "投掷球类"]
      cognitive: ["词汇爆发期", "假想游戏", "分类物品", "理解简单故事"]
      social: ["平行游戏", "表达基本需求", "模仿家务活动", "表现出独立性"]

# 安全评估配置
safety_assessment:
  # 玩具安全标准
  toys:
    size_threshold_cm: 3.17  # 小于此尺寸的玩具存在窒息风险
    distance_threshold_cm: 30  # 距离婴儿口部的安全距离
    material_risks: ["硬质", "尖锐", "小零件", "绳索"]
    
  # 毯子安全标准
  blankets:
    coverage_risks: ["覆盖面部", "过厚", "松散"]
    position_risks: ["缠绕", "堆积", "阻挡呼吸道"]
    
  # 环境安全标准
  environment:
    lighting_requirements: ["充足光线", "避免强光直射"]
    temperature_comfort: ["适宜温度", "良好通风"]
    space_safety: ["无尖锐边角", "稳固结构"]

# 输出格式配置
output_formats:
  # JSON格式配置
  json:
    enabled: true
    pretty_print: true
    include_metadata: true
  
  # CSV格式配置
  csv:
    enabled: true
    encoding: "utf-8"
    separator: ","
  
  # 训练格式配置
  training:
    enabled: true
    split_ratios:
      train: 0.7
      valid: 0.15
      test: 0.15
  
  # 统计报告配置
  statistics:
    enabled: true
    include_charts: false  # 需要matplotlib
    detailed_analysis: true

# 处理配置
processing:
  # 并行处理
  parallel_processing: false
  num_workers: 4
  
  # 批处理大小
  batch_size: 10
  
  # 错误处理
  continue_on_error: true
  max_retries: 3
  
  # 进度显示
  show_progress: true
  log_level: "INFO"

# 质量控制配置
quality_control:
  # 描述长度限制
  min_description_length: 50
  max_description_length: 2000
  
  # 关键词检查
  required_keywords: ["婴儿", "婴儿床"]
  forbidden_keywords: []
  
  # 一致性检查
  consistency_check: true
  
  # 人工审核
  manual_review:
    enabled: false
    sample_rate: 0.1  # 10%的样本需要人工审核
