#!/usr/bin/env python3
"""
CribNet图像转文本数据集快速开始脚本
帮助用户快速设置和运行转换器
"""

import os
import sys
import json
import argparse
from pathlib import Path
from cribnet_to_text_converter import CribNetTextConverter

def check_dependencies():
    """检查依赖包是否安装"""
    print("检查依赖包...")
    
    required_packages = [
        ("cv2", "opencv-python"),
        ("numpy", "numpy"),
        ("pandas", "pandas"),
        ("PIL", "Pillow")
    ]
    
    optional_packages = [
        ("openai", "openai"),
        ("google.generativeai", "google-generativeai"),
        ("yaml", "PyYAML")
    ]
    
    missing_required = []
    missing_optional = []
    
    # 检查必需包
    for package, pip_name in required_packages:
        try:
            __import__(package)
            print(f"✓ {pip_name}")
        except ImportError:
            print(f"✗ {pip_name} (必需)")
            missing_required.append(pip_name)
    
    # 检查可选包
    for package, pip_name in optional_packages:
        try:
            __import__(package)
            print(f"✓ {pip_name}")
        except ImportError:
            print(f"- {pip_name} (可选)")
            missing_optional.append(pip_name)
    
    if missing_required:
        print(f"\n缺少必需依赖包，请运行:")
        print(f"pip install {' '.join(missing_required)}")
        return False
    
    if missing_optional:
        print(f"\n可选依赖包未安装:")
        print(f"pip install {' '.join(missing_optional)}")
    
    print("\n依赖检查完成！")
    return True

def setup_directories():
    """设置目录结构"""
    print("设置目录结构...")
    
    directories = [
        "data/CribHD",
        "output/text_dataset",
        "output/exported_formats",
        "logs",
        "temp"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✓ 创建目录: {directory}")
    
    print("目录设置完成！")

def download_sample_data():
    """下载或创建示例数据"""
    print("创建示例数据...")
    
    # 创建示例图像信息（实际使用时需要真实图像）
    sample_info = {
        "note": "这是示例数据，实际使用时请下载真实的CribHD数据集",
        "dataset_url": "https://coe.northeastern.edu/Research/AClab/CribHD/",
        "structure": {
            "CRIBHD-B": "毯子检测数据集",
            "CRIBHD-T": "玩具检测数据集", 
            "CRIBHD-C": "组合场景数据集"
        }
    }
    
    sample_file = Path("data/CribHD/dataset_info.json")
    with open(sample_file, 'w', encoding='utf-8') as f:
        json.dump(sample_info, f, ensure_ascii=False, indent=2)
    
    print(f"✓ 示例信息已保存: {sample_file}")
    print("请从官方网站下载真实的CribHD数据集")

def interactive_setup():
    """交互式设置"""
    print("\n=== CribNet转换器交互式设置 ===")
    
    # 选择模型类型
    print("\n1. 选择模型类型:")
    print("   1) local - 本地模型（免费，功能基础）")
    print("   2) gpt4v - GPT-4V（需要API密钥，效果最好）")
    print("   3) gemini - Gemini Vision（需要API密钥，效果良好）")
    
    while True:
        choice = input("请选择模型类型 (1-3): ").strip()
        if choice == "1":
            model_type = "local"
            api_key = None
            break
        elif choice == "2":
            model_type = "gpt4v"
            api_key = input("请输入OpenAI API密钥: ").strip()
            if not api_key:
                print("GPT-4V需要API密钥")
                continue
            break
        elif choice == "3":
            model_type = "gemini"
            api_key = input("请输入Gemini API密钥: ").strip()
            if not api_key:
                print("Gemini需要API密钥")
                continue
            break
        else:
            print("请输入有效选择 (1-3)")
    
    # 数据集路径
    print("\n2. 数据集设置:")
    dataset_path = input("请输入CribHD数据集路径 [data/CribHD]: ").strip()
    if not dataset_path:
        dataset_path = "data/CribHD"
    
    # 输出路径
    output_path = input("请输入输出路径 [output/text_dataset]: ").strip()
    if not output_path:
        output_path = "output/text_dataset"
    
    # 子集选择
    print("\n3. 选择要处理的子集:")
    print("   1) all - 所有子集")
    print("   2) cribhd_b - 仅毯子数据")
    print("   3) cribhd_t - 仅玩具数据")
    print("   4) cribhd_c - 仅组合场景")
    
    subset_map = {"1": "all", "2": "cribhd_b", "3": "cribhd_t", "4": "cribhd_c"}
    while True:
        choice = input("请选择子集 (1-4): ").strip()
        if choice in subset_map:
            subset = subset_map[choice]
            break
        else:
            print("请输入有效选择 (1-4)")
    
    # 图像数量限制
    max_images_input = input("最大处理图像数量 (回车表示处理所有): ").strip()
    max_images = int(max_images_input) if max_images_input.isdigit() else None
    
    return {
        "model_type": model_type,
        "api_key": api_key,
        "dataset_path": dataset_path,
        "output_path": output_path,
        "subset": subset,
        "max_images": max_images
    }

def run_conversion(config):
    """运行转换"""
    print(f"\n=== 开始转换 ===")
    print(f"模型类型: {config['model_type']}")
    print(f"数据集路径: {config['dataset_path']}")
    print(f"输出路径: {config['output_path']}")
    print(f"处理子集: {config['subset']}")
    print(f"最大图像数: {config['max_images'] or '无限制'}")
    
    try:
        # 创建转换器
        converter = CribNetTextConverter(
            model_type=config['model_type'],
            api_key=config['api_key']
        )
        
        # 检查数据集是否存在
        if not os.path.exists(config['dataset_path']):
            print(f"错误: 数据集路径不存在 - {config['dataset_path']}")
            print("请下载CribHD数据集或检查路径设置")
            return False
        
        # 运行转换
        converter.convert_dataset(
            dataset_path=config['dataset_path'],
            output_path=config['output_path'],
            subset=config['subset'],
            max_images=config['max_images']
        )
        
        # 导出多种格式
        json_file = Path(config['output_path']) / "cribhd_text_dataset.json"
        if json_file.exists():
            print("\n导出多种格式...")
            converter.export_to_formats(str(json_file), config['output_path'])
        
        print("\n✓ 转换完成！")
        return True
        
    except Exception as e:
        print(f"转换过程中出现错误: {str(e)}")
        return False

def show_results(output_path):
    """显示转换结果"""
    print(f"\n=== 转换结果 ===")
    
    output_dir = Path(output_path)
    if not output_dir.exists():
        print("输出目录不存在")
        return
    
    # 列出生成的文件
    print("生成的文件:")
    for file_path in output_dir.rglob("*"):
        if file_path.is_file():
            size = file_path.stat().st_size
            print(f"  {file_path.relative_to(output_dir)} ({size} bytes)")
    
    # 显示统计信息
    stats_file = output_dir / "conversion_statistics.json"
    if stats_file.exists():
        with open(stats_file, 'r', encoding='utf-8') as f:
            stats = json.load(f)
        
        print(f"\n统计信息:")
        print(f"  总图像数: {stats.get('总体统计', {}).get('总图像数', 0)}")
        print(f"  转换时间: {stats.get('总体统计', {}).get('转换时间', 'N/A')}")
        
        # 子集分布
        subset_dist = stats.get('子集分布', {})
        if subset_dist:
            print(f"  子集分布:")
            for subset, count in subset_dist.items():
                print(f"    {subset}: {count}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="CribNet转换器快速开始")
    parser.add_argument("--skip-check", action="store_true", help="跳过依赖检查")
    parser.add_argument("--auto", action="store_true", help="使用默认配置自动运行")
    
    args = parser.parse_args()
    
    print("CribNet图像转文本数据集转换器")
    print("=" * 40)
    
    # 检查依赖
    if not args.skip_check:
        if not check_dependencies():
            sys.exit(1)
    
    # 设置目录
    setup_directories()
    
    # 创建示例数据
    download_sample_data()
    
    if args.auto:
        # 自动模式 - 使用默认配置
        config = {
            "model_type": "local",
            "api_key": None,
            "dataset_path": "data/CribHD",
            "output_path": "output/text_dataset",
            "subset": "all",
            "max_images": 5  # 限制为5张图像用于演示
        }
        print("\n使用默认配置运行...")
    else:
        # 交互模式
        config = interactive_setup()
    
    # 运行转换
    success = run_conversion(config)
    
    if success:
        show_results(config['output_path'])
        print(f"\n🎉 转换成功完成！")
        print(f"结果保存在: {config['output_path']}")
    else:
        print(f"\n❌ 转换失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
