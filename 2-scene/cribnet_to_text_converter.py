#!/usr/bin/env python3
"""
CribNet图像数据集转文本数据集转换器
基于CribNet数据集生成婴幼儿家庭场景运动监测的文本描述
"""

import os
import json
import cv2
import numpy as np
from pathlib import Path
import argparse
from typing import Dict, List, Tuple, Any
import base64
from datetime import datetime

# 多模态模型接口（需要根据实际使用的模型调整）
try:
    import openai  # GPT-4V
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False

try:
    import google.generativeai as genai  # Gemini
    HAS_GEMINI = True
except ImportError:
    HAS_GEMINI = False

try:
    import dashscope  # Qwen
    from dashscope import MultiModalConversation
    HAS_QWEN = True
except ImportError:
    HAS_QWEN = False

try:
    import requests  # 用于HTTP请求
    HAS_REQUESTS = True
except ImportError:
    HAS_REQUESTS = False

class CribNetTextConverter:
    """CribNet图像到文本转换器"""
    
    def __init__(self, model_type="qwen", api_key=None, use_llm_guidance=False, config=None):
        """
        初始化转换器

        Args:
            model_type: 使用的模型类型 ("qwen", "gpt4v", "gemini", "local")
            api_key: API密钥
            use_llm_guidance: 是否使用LLM生成个性化发育指导建议
            config: 配置字典
        """
        self.model_type = model_type
        self.api_key = api_key
        self.use_llm_guidance = use_llm_guidance
        self.config = config or {}

        # 从配置中获取模型特定设置
        if self.config and model_type in self.config.get('model', {}):
            model_config = self.config['model'][model_type]
            self.model_name = model_config.get('model_name')
            self.max_tokens = model_config.get('max_tokens', 2000)
            self.temperature = model_config.get('temperature', 0.3)
        else:
            # 默认设置
            if model_type == "qwen":
                self.model_name = "qwen-vl-plus"  # 默认使用多模态模型
            else:
                self.model_name = None
            self.max_tokens = 2000
            self.temperature = 0.3
        
        # 婴幼儿发育里程碑模板
        self.development_milestones = {
            "0-3months": {
                "gross_motor": ["抬头", "俯卧时头部稍微抬起", "仰卧时头部转向一侧"],
                "fine_motor": ["握拳", "手指偶尔张开", "触碰反射"],
                "cognitive": ["注视人脸", "跟踪移动物体", "对声音有反应"]
            },
            "3-6months": {
                "gross_motor": ["翻身", "俯卧时用前臂支撑", "坐立需要支撑"],
                "fine_motor": ["抓握玩具", "双手互相触碰", "将物品放入口中"],
                "cognitive": ["认识熟悉的人", "对镜子中的自己感兴趣", "模仿表情"]
            },
            "6-12months": {
                "gross_motor": ["独立坐立", "爬行", "扶站", "迈步"],
                "fine_motor": ["钳形抓握", "敲击物品", "撕纸", "指点"],
                "cognitive": ["物体永恒性", "模仿动作", "理解简单指令"]
            },
            "12-24months": {
                "gross_motor": ["独立行走", "跑步", "上下楼梯", "踢球"],
                "fine_motor": ["搭积木", "涂鸦", "翻书页", "使用勺子"],
                "cognitive": ["词汇爆发期", "假想游戏", "分类物品"]
            }
        }
        
        # 安全风险评估标准
        self.safety_criteria = {
            "toys": {
                "size_risk": "小于3.17cm的玩具存在窒息风险",
                "distance_risk": "距离婴儿口部30cm内的玩具需要关注",
                "material_risk": "硬质玩具可能造成碰撞伤害"
            },
            "blankets": {
                "coverage_risk": "覆盖面部的毯子存在窒息风险",
                "thickness_risk": "过厚的毯子可能影响呼吸",
                "position_risk": "毯子位置不当可能限制运动"
            }
        }
        
        self._setup_model()
    
    def _setup_model(self):
        """设置模型"""
        if self.model_type == "qwen" and HAS_QWEN:
            dashscope.api_key = self.api_key
        elif self.model_type == "gpt4v" and HAS_OPENAI:
            openai.api_key = self.api_key
        elif self.model_type == "gemini" and HAS_GEMINI:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel('gemini-pro-vision')
    
    def load_cribhd_dataset(self, dataset_path: str) -> Dict[str, Any]:
        """
        加载CribHD数据集
        
        Args:
            dataset_path: 数据集路径
            
        Returns:
            数据集信息字典
        """
        dataset_info = {
            "cribhd_b": {},  # 毯子数据
            "cribhd_t": {},  # 玩具数据
            "cribhd_c": {}   # 组合场景数据
        }
        
        dataset_path = Path(dataset_path)
        
        # 加载CRIBHD-B (毯子数据)
        cribhd_b_path = dataset_path / "CRIBHD-B" / "COCO"
        if cribhd_b_path.exists():
            dataset_info["cribhd_b"] = self._load_coco_format(cribhd_b_path)
        
        # 加载CRIBHD-T (玩具数据)
        cribhd_t_path = dataset_path / "CRIBHD-T" / "COCO"
        if cribhd_t_path.exists():
            dataset_info["cribhd_t"] = self._load_coco_format(cribhd_t_path)
        
        # 加载CRIBHD-C (组合场景数据) - 特殊结构处理
        cribhd_c_path = dataset_path / "CRIBHD-C"
        if cribhd_c_path.exists():
            dataset_info["cribhd_c"] = self._load_cribhd_c_format(cribhd_c_path)
        
        return dataset_info
    
    def _load_coco_format(self, coco_path: Path) -> Dict[str, Any]:
        """加载COCO格式的数据"""
        data = {"train": {}, "test": {}, "valid": {}}
        
        for split in ["train", "test", "valid"]:
            split_path = coco_path / split
            if split_path.exists():
                # 加载注释文件
                annotation_file = split_path / "_annotations.coco.json"
                if annotation_file.exists():
                    with open(annotation_file, 'r') as f:
                        annotations = json.load(f)
                    data[split]["annotations"] = annotations
                
                # 加载图像路径
                images_path = split_path / "images"
                if images_path.exists():
                    data[split]["images"] = list(images_path.glob("*.jpg"))
        
        return data
    
    def _load_images_only(self, images_path: Path) -> Dict[str, Any]:
        """加载仅包含图像的数据"""
        return {
            "images": list(images_path.glob("*.jpg"))
        }

    def _load_cribhd_c_format(self, cribhd_c_path: Path) -> Dict[str, Any]:
        """
        专门加载CRIBHD-C格式数据
        CRIBHD-C包含120张模拟危机场景图像，结构可能不同于其他子集
        """
        cribhd_c_data = {
            "images": [],
            "metadata": {
                "total_images": 0,
                "description": "模拟危机场景数据集，包含面部/四肢被遮挡、玩具靠近口鼻等风险场景",
                "special_features": ["高风险场景", "仿真玩偶", "多种道具", "复杂遮挡情况"]
            }
        }

        # 尝试多种可能的图像路径
        possible_image_paths = [
            cribhd_c_path / "images",           # 标准路径
            cribhd_c_path,                      # 直接在CRIBHD-C目录下
            cribhd_c_path / "data",             # 可能的data子目录
            cribhd_c_path / "crisis_scenes"     # 可能的危机场景目录
        ]

        images_found = []

        for image_path in possible_image_paths:
            if image_path.exists():
                # 支持多种图像格式
                for ext in ["*.jpg", "*.jpeg", "*.png", "*.JPG", "*.JPEG", "*.PNG"]:
                    images_found.extend(list(image_path.glob(ext)))

                if images_found:
                    print(f"在 {image_path} 找到 {len(images_found)} 张CRIBHD-C图像")
                    break

        # 去重并排序
        images_found = list(set(images_found))
        images_found.sort()

        cribhd_c_data["images"] = images_found
        cribhd_c_data["metadata"]["total_images"] = len(images_found)

        # 检查是否有注释文件
        annotation_files = [
            cribhd_c_path / "annotations.json",
            cribhd_c_path / "crisis_annotations.json",
            cribhd_c_path / "metadata.json"
        ]

        for ann_file in annotation_files:
            if ann_file.exists():
                try:
                    with open(ann_file, 'r', encoding='utf-8') as f:
                        annotations = json.load(f)
                    cribhd_c_data["annotations"] = annotations
                    print(f"加载CRIBHD-C注释文件: {ann_file}")
                    break
                except Exception as e:
                    print(f"加载注释文件失败 {ann_file}: {str(e)}")

        if len(images_found) == 0:
            print(f"警告: 在CRIBHD-C目录中未找到图像文件")
            print(f"检查的路径: {[str(p) for p in possible_image_paths]}")
        elif len(images_found) != 120:
            print(f"注意: CRIBHD-C预期120张图像，实际找到 {len(images_found)} 张")

        return cribhd_c_data
    
    def generate_text_description(self, image_path: str, annotations: Dict = None) -> Dict[str, Any]:
        """
        为单张图像生成文本描述
        
        Args:
            image_path: 图像路径
            annotations: COCO格式的注释信息
            
        Returns:
            文本描述字典
        """
        # 构建提示词
        prompt = self._build_prompt(annotations)
        
        # 根据模型类型生成描述
        if self.model_type == "qwen":
            description = self._generate_with_qwen(image_path, prompt)
        elif self.model_type == "gpt4v":
            description = self._generate_with_gpt4v(image_path, prompt)
        elif self.model_type == "gemini":
            description = self._generate_with_gemini(image_path, prompt)
        else:
            description = self._generate_with_local_model(image_path, prompt)
        
        # 解析和结构化描述
        structured_description = self._parse_description(description, annotations)
        
        return structured_description
    
    def _build_prompt(self, annotations: Dict = None) -> str:
        """构建用于图像描述生成的提示词"""
        base_prompt = """
你是一位专业的婴幼儿发育评估和家庭安全监测专家。请详细分析这张婴儿床场景图像，并提供专业的评估和指导建议。

请按以下结构进行分析：

## 1. 基础场景描述
- 婴儿年龄估计（0-3个月/3-6个月/6-12个月/12-24个月）
- 婴儿当前姿势和动作状态
- 婴儿床内物品清单（玩具、毯子、其他物品）

## 2. 运动发育评估
- 大运动技能观察（抬头、翻身、坐立、爬行、站立等）
- 精细动作技能观察（抓握、指点、操作物品等）
- 发育水平评估（正常/超前/需关注）
- 与该年龄段发育里程碑的对比

## 3. 安全风险评估
- 玩具安全性分析（尺寸、材质、位置）
- 毯子使用安全性（是否覆盖面部、限制活动）
- 其他潜在风险因素
- 风险等级评定（无风险/低风险/中风险/高风险）

## 4. 育儿指导建议
- 针对当前发育阶段的活动建议
- 安全改进措施
- 家长注意事项
- 促进发育的互动方式

## 5. 专业总结
- 整体发育状况评价
- 重点关注事项
- 下一阶段发育预期

请用中文回答，语言专业准确，适合家长理解和执行。
"""

        # 根据注释信息添加特定提示
        if annotations:
            # 检查是否为CRIBHD-C危机场景
            if annotations.get("dataset_type") == "CRIBHD-C":
                base_prompt += "\n\n⚠️ 特别注意：这是CRIBHD-C模拟危机场景数据集的图像"
                base_prompt += "\n\n重点分析要求："
                base_prompt += "\n1. 这是使用仿真玩偶和道具构建的模拟危机场景"
                base_prompt += "\n2. 重点识别和分析各种潜在的安全风险"
                base_prompt += "\n3. 特别关注面部遮挡、四肢受限、玩具靠近口鼻等高危情况"
                base_prompt += "\n4. 提供针对真实场景的预防建议和应急处理方法"
                base_prompt += "\n5. 评估风险等级并给出明确的安全警告"

                focus_areas = annotations.get("special_instructions", {}).get("focus_areas", [])
                if focus_areas:
                    base_prompt += f"\n\n分析重点：{chr(10).join(f'- {area}' for area in focus_areas)}"

            else:
                # 原有的玩具和毯子特殊提示
                categories = annotations.get("categories", [])
                if categories:
                    category_names = [cat.get("name", "") for cat in categories]
                    if "toy" in str(category_names).lower():
                        base_prompt += "\n\n特别关注：图像中包含玩具，请重点分析玩具的安全性，包括尺寸是否存在窒息风险、材质是否安全、与婴儿的距离是否合适。"
                    elif "blanket" in str(category_names).lower():
                        base_prompt += "\n\n特别关注：图像中包含毯子，请重点分析毯子的使用是否安全，是否存在覆盖面部或限制活动的风险。"

            # 添加注释信息（对于CRIBHD-C，只添加关键信息避免过长）
            if annotations.get("dataset_type") == "CRIBHD-C":
                key_annotations = {
                    "dataset_type": annotations.get("dataset_type"),
                    "scene_type": annotations.get("scene_type"),
                    "risk_level": annotations.get("risk_level"),
                    "image_name": annotations.get("image_name")
                }
                base_prompt += f"\n\n场景信息：{json.dumps(key_annotations, ensure_ascii=False, indent=2)}"
            else:
                base_prompt += f"\n\n标注信息参考：{json.dumps(annotations, ensure_ascii=False, indent=2)}"

        return base_prompt

    def _generate_with_qwen(self, image_path: str, prompt: str) -> str:
        """使用Qwen多模态模型生成描述"""
        if not HAS_QWEN:
            return "Qwen不可用，请安装dashscope库: pip install dashscope"

        try:
            # 构建消息格式
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"text": prompt},
                        {"image": f"file://{os.path.abspath(image_path)}"}
                    ]
                }
            ]

            # 调用Qwen VL模型
            model_name = self.model_name or 'qwen-vl-plus'

            # 如果配置的是文本模型，强制使用多模态模型
            if model_name in ['qwen-turbo', 'qwen-turbo-0919', 'qwen-turbo-1101']:
                print(f"警告: {model_name} 是文本模型，图像处理需要多模态模型，自动切换到 qwen-vl-plus")
                model_name = 'qwen-vl-plus'

            response = MultiModalConversation.call(
                model=model_name,
                messages=messages
            )

            if response.status_code == 200:
                return response.output.choices[0].message.content
            else:
                return f"Qwen API调用失败: {response.message}"

        except Exception as e:
            return f"Qwen生成失败: {str(e)}"

    def _generate_with_qwen_http(self, image_path: str, prompt: str) -> str:
        """使用HTTP请求调用Qwen API（备用方法）"""
        if not HAS_REQUESTS:
            return "requests库不可用，请安装: pip install requests"

        try:
            # 编码图像为base64
            with open(image_path, "rb") as image_file:
                base64_image = base64.b64encode(image_file.read()).decode('utf-8')

            # 构建请求
            url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/multimodal-generation/generation"
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            # 使用配置的模型名称，但确保是多模态模型
            model_name = self.model_name or 'qwen-vl-plus'
            if model_name in ['qwen-turbo', 'qwen-turbo-0919', 'qwen-turbo-1101']:
                print(f"警告: {model_name} 是文本模型，图像处理需要多模态模型，自动切换到 qwen-vl-plus")
                model_name = 'qwen-vl-plus'

            data = {
                "model": model_name,
                "input": {
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {"text": prompt},
                                {"image": f"data:image/jpeg;base64,{base64_image}"}
                            ]
                        }
                    ]
                },
                "parameters": {
                    "temperature": 0.3,
                    "max_tokens": 2000
                }
            }

            response = requests.post(url, headers=headers, json=data)

            if response.status_code == 200:
                result = response.json()
                return result["output"]["choices"][0]["message"]["content"]
            else:
                return f"Qwen HTTP API调用失败: {response.status_code} - {response.text}"

        except Exception as e:
            return f"Qwen HTTP生成失败: {str(e)}"

    def _generate_with_gpt4v(self, image_path: str, prompt: str) -> str:
        """使用GPT-4V生成描述"""
        if not HAS_OPENAI:
            return "GPT-4V不可用，请安装openai库"
        
        # 编码图像
        with open(image_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')
        
        try:
            response = openai.ChatCompletion.create(
                model="gpt-4-vision-preview",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{base64_image}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=1000
            )
            return response.choices[0].message.content
        except Exception as e:
            return f"GPT-4V生成失败: {str(e)}"
    
    def _generate_with_gemini(self, image_path: str, prompt: str) -> str:
        """使用Gemini生成描述"""
        if not HAS_GEMINI:
            return "Gemini不可用，请安装google-generativeai库"
        
        try:
            # 加载图像
            import PIL.Image
            image = PIL.Image.open(image_path)
            
            response = self.model.generate_content([prompt, image])
            return response.text
        except Exception as e:
            return f"Gemini生成失败: {str(e)}"
    
    def _generate_with_local_model(self, image_path: str, prompt: str) -> str:
        """使用本地模型生成描述（示例实现）"""
        # 这里可以集成本地的图像描述模型
        # 例如BLIP、CLIP等
        
        # 简单的基于规则的描述生成（示例）
        image = cv2.imread(image_path)
        height, width = image.shape[:2]
        
        description = f"""
基于图像分析的基础描述：
- 图像尺寸：{width}x{height}
- 这是一张婴儿床场景图像
- 需要使用更高级的模型进行详细分析
- 建议使用GPT-4V或Gemini获得更准确的描述
"""
        return description
    
    def _parse_description(self, description: str, annotations: Dict = None) -> Dict[str, Any]:
        """解析和结构化描述"""
        current_time = datetime.now().isoformat()

        # 提取CribHD特定信息
        cribhd_info = self._extract_cribhd_specific_info(description, annotations)

        structured = {
            "timestamp": current_time,
            "raw_description": description,
            "estimated_age_months": self._extract_age_estimate(description),
            "motor_skills": self._extract_motor_skills(description),
            "safety_assessment": self._extract_safety_info(description),
            "development_level": self._assess_development_level(description),
            "scene_type": "crib",
            "annotations": annotations or {},
            # CribHD特定字段
            "toy_properties": cribhd_info["toy_properties"],
            "blanket_properties": cribhd_info["blanket_properties"],
            "risk_assessment": cribhd_info["risk_assessment"],
            "development_guidance": cribhd_info["development_guidance"]
        }

        return structured

    def _assess_development_level(self, description: str) -> str:
        """评估发育水平"""
        if "超前" in description or "提前" in description:
            return "超前"
        elif "延迟" in description or "落后" in description or "需关注" in description:
            return "需关注"
        elif "正常" in description or "符合" in description:
            return "正常"
        else:
            return "正常"  # 默认值
    
    def _extract_age_estimate(self, description: str) -> int:
        """从描述中提取年龄估计"""
        # 简单的关键词匹配，实际应用中需要更复杂的NLP
        if "0-3个月" in description or "新生儿" in description:
            return 2
        elif "3-6个月" in description:
            return 4
        elif "6-12个月" in description:
            return 9
        elif "12-24个月" in description:
            return 18
        else:
            return 6  # 默认值
    
    def _extract_motor_skills(self, description: str) -> List[str]:
        """从描述中提取运动技能"""
        skills = []
        skill_keywords = ["抬头", "翻身", "坐立", "爬行", "站立", "行走", "抓握", "指点"]
        
        for skill in skill_keywords:
            if skill in description:
                skills.append(skill)
        
        return skills
    
    def _extract_safety_info(self, description: str) -> Dict[str, Any]:
        """从描述中提取安全信息"""
        safety_info = {
            "risks_detected": [],
            "safe_items": [],
            "recommendations": []
        }
        
        # 检测风险关键词
        risk_keywords = ["窒息", "覆盖", "小物品", "硬物", "绳索"]
        for keyword in risk_keywords:
            if keyword in description:
                safety_info["risks_detected"].append(keyword)
        
        return safety_info

    def _extract_cribhd_specific_info(self, description: str, annotations: Dict = None) -> Dict[str, Any]:
        """提取CribHD特定信息"""
        cribhd_info = {
            "toy_properties": {},
            "blanket_properties": {},
            "risk_assessment": {},
            "development_guidance": {}
        }

        # 提取玩具相关信息
        if "玩具" in description:
            cribhd_info["toy_properties"] = {
                "material": self._extract_toy_material(description),
                "shape": self._extract_toy_shape(description),
                "size": self._extract_toy_size(description),
                "presentation": self._extract_toy_presentation(description)
            }

        # 提取毯子相关信息
        if "毯子" in description or "毛毯" in description:
            cribhd_info["blanket_properties"] = {
                "coverage": self._extract_blanket_coverage(description),
                "position": self._extract_blanket_position(description),
                "thickness": self._extract_blanket_thickness(description)
            }

        # 风险评估
        cribhd_info["risk_assessment"] = self._assess_cribhd_risks(description, annotations)

        # 发育指导
        cribhd_info["development_guidance"] = self._generate_development_guidance(
            description, use_llm=self.use_llm_guidance
        )

        return cribhd_info

    def _extract_toy_material(self, description: str) -> str:
        """提取玩具材质"""
        if "软质" in description or "柔软" in description:
            return "软质"
        elif "硬质" in description or "坚硬" in description:
            return "硬质"
        else:
            return "未知"

    def _extract_toy_shape(self, description: str) -> str:
        """提取玩具形状"""
        if "圆形" in description or "球形" in description:
            return "圆形"
        elif "方形" in description or "多边形" in description or "棱角" in description:
            return "多边形"
        else:
            return "未知"

    def _extract_toy_size(self, description: str) -> str:
        """提取玩具尺寸"""
        if "小" in description and ("玩具" in description):
            return "小型"
        elif "大" in description and ("玩具" in description):
            return "大型"
        else:
            return "中等"

    def _extract_toy_presentation(self, description: str) -> str:
        """提取玩具呈现方式"""
        if "手持" in description or "抓握" in description:
            return "手持"
        elif "孤立" in description or "单独" in description:
            return "孤立"
        else:
            return "未知"

    def _extract_blanket_coverage(self, description: str) -> str:
        """提取毯子覆盖情况"""
        if "覆盖面部" in description or "遮住脸" in description:
            return "覆盖面部"
        elif "覆盖身体" in description:
            return "覆盖身体"
        elif "部分覆盖" in description:
            return "部分覆盖"
        else:
            return "正常覆盖"

    def _extract_blanket_position(self, description: str) -> str:
        """提取毯子位置"""
        if "缠绕" in description:
            return "缠绕"
        elif "堆积" in description:
            return "堆积"
        else:
            return "正常"

    def _extract_blanket_thickness(self, description: str) -> str:
        """提取毯子厚度"""
        if "厚" in description and "毯子" in description:
            return "厚重"
        elif "薄" in description and "毯子" in description:
            return "轻薄"
        else:
            return "适中"

    def _assess_cribhd_risks(self, description: str, annotations: Dict = None) -> Dict[str, Any]:
        """评估CribHD特定风险"""
        risks = {
            "choking_risk": False,
            "suffocation_risk": False,
            "injury_risk": False,
            "movement_restriction": False,
            "risk_level": "无风险",
            "specific_risks": []
        }

        # 窒息风险评估
        if ("小" in description and "玩具" in description and "口" in description) or \
           ("软质" in description and "靠近" in description):
            risks["choking_risk"] = True
            risks["specific_risks"].append("窒息危险（小而软的玩具靠近口部）")

        # 窒息风险评估（毯子）
        if "覆盖面部" in description or "遮住脸" in description:
            risks["suffocation_risk"] = True
            risks["specific_risks"].append("窒息风险（毯子覆盖面部）")

        # 伤害风险评估
        if ("硬质" in description and "大" in description and "靠近" in description):
            risks["injury_risk"] = True
            risks["specific_risks"].append("伤害危险（大而硬的玩具靠近身体）")

        # 活动限制风险
        if "缠绕" in description or "限制" in description:
            risks["movement_restriction"] = True
            risks["specific_risks"].append("活动受限（毯子限制四肢活动）")

        # 综合风险等级
        if risks["suffocation_risk"] or risks["choking_risk"]:
            risks["risk_level"] = "高风险"
        elif risks["injury_risk"] or risks["movement_restriction"]:
            risks["risk_level"] = "中风险"
        elif len(risks["specific_risks"]) > 0:
            risks["risk_level"] = "低风险"

        return risks

    def _generate_development_guidance(self, description: str, use_llm: bool = False) -> Dict[str, Any]:
        """
        生成发育指导建议

        Args:
            description: 图像描述文本
            use_llm: 是否使用LLM生成个性化建议
        """
        if use_llm and self.model_type in ["qwen", "gpt4v", "gemini"]:
            return self._generate_llm_guidance(description)
        else:
            return self._generate_rule_based_guidance(description)

    def _generate_rule_based_guidance(self, description: str) -> Dict[str, Any]:
        """基于规则的发育指导建议（标准化）"""
        guidance = {
            "age_appropriate_activities": [],
            "safety_recommendations": [],
            "interaction_suggestions": [],
            "next_milestones": [],
            "generation_method": "rule_based"
        }

        # 根据描述中的年龄和技能生成建议
        if "0-3个月" in description:
            guidance["age_appropriate_activities"] = [
                "提供黑白对比强烈的视觉刺激",
                "进行俯卧时间练习",
                "轻柔的音乐和声音刺激"
            ]
            guidance["next_milestones"] = ["抬头", "跟踪物体", "社交性微笑"]

        elif "3-6个月" in description:
            guidance["age_appropriate_activities"] = [
                "提供不同质地的玩具",
                "鼓励翻身练习",
                "进行简单的互动游戏"
            ]
            guidance["next_milestones"] = ["独立坐立", "抓握玩具", "发出咿呀声"]

        elif "6-12个月" in description:
            guidance["age_appropriate_activities"] = [
                "提供安全的爬行空间",
                "练习坐立平衡",
                "简单的因果关系游戏"
            ]
            guidance["next_milestones"] = ["爬行", "扶站", "理解简单指令"]

        elif "12-24个月" in description:
            guidance["age_appropriate_activities"] = [
                "鼓励独立行走",
                "提供搭建类玩具",
                "简单的语言交流"
            ]
            guidance["next_milestones"] = ["跑步", "词汇爆发", "假想游戏"]

        # 安全建议
        if "玩具" in description:
            guidance["safety_recommendations"].extend([
                "确保玩具直径大于3.17厘米",
                "定期检查玩具完整性",
                "选择适合年龄的玩具"
            ])

        if "毯子" in description:
            guidance["safety_recommendations"].extend([
                "避免毯子覆盖面部",
                "选择透气性好的材质",
                "确保毯子不会缠绕四肢"
            ])

        return guidance

    def _generate_llm_guidance(self, description: str) -> Dict[str, Any]:
        """使用LLM基于规则基础指导生成更丰富的个性化建议"""

        # 首先获取规则基础的标准建议
        base_guidance = self._generate_rule_based_guidance(description)

        # 构建增强型提示词，以规则基础建议为起点
        guidance_prompt = f"""
你是一位专业的婴幼儿发育评估专家。我已经根据标准发育里程碑为这个场景生成了基础的指导建议，请在此基础上提供更详细、更个性化的专业指导。

## 场景描述：
{description}

## 基础标准建议（请在此基础上扩展和个性化）：

### 适龄活动建议：
{chr(10).join(f"- {activity}" for activity in base_guidance.get('age_appropriate_activities', []))}

### 安全建议：
{chr(10).join(f"- {safety}" for safety in base_guidance.get('safety_recommendations', []))}

### 下阶段发育目标：
{chr(10).join(f"- {milestone}" for milestone in base_guidance.get('next_milestones', []))}

## 请提供以下增强内容：

1. **具体实施方法**：针对每个基础活动建议，提供具体的实施步骤和注意事项
2. **个性化调整**：根据当前场景的具体情况，调整或补充建议
3. **家长互动指导**：详细的亲子互动方式和技巧
4. **发育观察要点**：家长应该观察哪些发育信号
5. **常见问题解答**：针对这个发育阶段的常见疑虑

请按以下JSON格式输出：
{{
    "enhanced_activities": [
        {{
            "base_activity": "基础活动名称",
            "detailed_steps": ["步骤1", "步骤2", "步骤3"],
            "tips": ["注意事项1", "注意事项2"],
            "frequency": "建议频次",
            "duration": "建议时长"
        }}
    ],
    "personalized_safety": [
        {{
            "risk_factor": "具体风险因素",
            "immediate_action": "立即采取的行动",
            "prevention_tips": ["预防措施1", "预防措施2"],
            "monitoring_points": ["观察要点1", "观察要点2"]
        }}
    ],
    "interaction_guidance": [
        {{
            "interaction_type": "互动类型",
            "description": "详细描述",
            "benefits": ["益处1", "益处2"],
            "demonstration": "如何演示给家长"
        }}
    ],
    "developmental_observations": [
        {{
            "skill_area": "技能领域",
            "what_to_watch": ["观察要点1", "观察要点2"],
            "normal_variations": "正常变异范围",
            "when_to_consult": "何时需要咨询专业人士"
        }}
    ],
    "scene_specific_advice": "针对当前具体场景的个性化建议和解释",
    "parent_education": "家长教育要点和背景知识"
}}

要求：
- 保持专业性，但语言要通俗易懂
- 建议要实用且可操作
- 考虑中国家庭的实际情况
- 基于循证医学和发育心理学原理
"""

        try:
            llm_response = ""

            if self.model_type == "qwen":
                # 使用Qwen文本模型生成增强建议
                import dashscope
                from dashscope import Generation

                response = Generation.call(
                    model='qwen-turbo',  # 使用文本模型
                    prompt=guidance_prompt,
                    temperature=0.3,
                    max_tokens=2000
                )

                if response.status_code == 200:
                    llm_response = response.output.text

            elif self.model_type == "gpt4v":
                # 使用GPT-4进行文本生成
                import openai
                response = openai.ChatCompletion.create(
                    model="gpt-4",
                    messages=[{"role": "user", "content": guidance_prompt}],
                    temperature=0.3,
                    max_tokens=2000
                )
                llm_response = response.choices[0].message.content

            elif self.model_type == "gemini":
                # 使用Gemini文本模型
                import google.generativeai as genai
                model = genai.GenerativeModel('gemini-pro')
                response = model.generate_content(guidance_prompt)
                llm_response = response.text

            # 解析LLM响应
            if llm_response:
                enhanced_guidance = self._parse_llm_guidance_response(llm_response, base_guidance)
                if enhanced_guidance:
                    enhanced_guidance["generation_method"] = "llm_enhanced"
                    enhanced_guidance["base_guidance"] = base_guidance
                    return enhanced_guidance

            # 如果LLM生成失败，返回增强的规则基础建议
            enhanced_base = self._enhance_rule_based_guidance(base_guidance, description)
            enhanced_base["generation_method"] = "rule_based_enhanced"
            return enhanced_base

        except Exception as e:
            # 出错时返回增强的规则基础建议
            enhanced_base = self._enhance_rule_based_guidance(base_guidance, description)
            enhanced_base["generation_method"] = "rule_based_error"
            enhanced_base["error_note"] = f"LLM生成失败: {str(e)}"
            return enhanced_base

    def _parse_llm_guidance_response(self, llm_response: str, base_guidance: Dict) -> Dict[str, Any]:
        """解析LLM生成的增强指导建议"""
        try:
            import re

            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', llm_response, re.DOTALL)
            if json_match:
                enhanced_data = json.loads(json_match.group())

                # 合并基础建议和增强建议
                combined_guidance = {
                    # 保留原有的基础字段
                    "age_appropriate_activities": base_guidance.get("age_appropriate_activities", []),
                    "safety_recommendations": base_guidance.get("safety_recommendations", []),
                    "interaction_suggestions": base_guidance.get("interaction_suggestions", []),
                    "next_milestones": base_guidance.get("next_milestones", []),

                    # 添加LLM增强的详细内容
                    "enhanced_activities": enhanced_data.get("enhanced_activities", []),
                    "personalized_safety": enhanced_data.get("personalized_safety", []),
                    "interaction_guidance": enhanced_data.get("interaction_guidance", []),
                    "developmental_observations": enhanced_data.get("developmental_observations", []),
                    "scene_specific_advice": enhanced_data.get("scene_specific_advice", ""),
                    "parent_education": enhanced_data.get("parent_education", "")
                }

                return combined_guidance

            # 如果无法解析JSON，尝试提取文本内容
            return self._extract_guidance_from_text(llm_response, base_guidance)

        except Exception as e:
            print(f"解析LLM响应失败: {str(e)}")
            return None

    def _extract_guidance_from_text(self, text_response: str, base_guidance: Dict) -> Dict[str, Any]:
        """从文本响应中提取指导建议"""
        # 简单的文本解析逻辑
        enhanced_guidance = base_guidance.copy()

        # 添加LLM生成的文本作为个性化建议
        enhanced_guidance["llm_generated_advice"] = text_response
        enhanced_guidance["scene_specific_advice"] = "基于LLM分析的个性化建议，请查看详细文本内容"

        return enhanced_guidance

    def _enhance_rule_based_guidance(self, base_guidance: Dict, description: str) -> Dict[str, Any]:
        """增强规则基础的指导建议（不使用LLM时的备选方案）"""
        enhanced = base_guidance.copy()

        # 添加一些基于场景的增强信息
        enhanced["scene_analysis"] = self._analyze_scene_context(description)
        enhanced["detailed_explanations"] = self._add_detailed_explanations(base_guidance)
        enhanced["implementation_tips"] = self._add_implementation_tips(base_guidance)

        return enhanced

    def _analyze_scene_context(self, description: str) -> Dict[str, str]:
        """分析场景上下文"""
        context = {}

        if "玩具" in description:
            if "小" in description:
                context["toy_safety"] = "检测到小型玩具，需要特别注意窒息风险"
            if "软质" in description:
                context["toy_material"] = "软质玩具相对安全，但仍需注意尺寸"
            if "硬质" in description:
                context["toy_material"] = "硬质玩具需要确保无尖锐边角"

        if "毯子" in description:
            if "覆盖" in description:
                context["blanket_position"] = "注意毯子位置，避免覆盖面部"
            if "厚" in description:
                context["blanket_thickness"] = "厚毯子可能影响体温调节"

        # 分析年龄相关上下文
        if "0-3个月" in description or "新生儿" in description:
            context["age_focus"] = "新生儿期重点关注基础生理需求和感官刺激"
        elif "6-12个月" in description:
            context["age_focus"] = "这个阶段是运动技能快速发展期，需要提供充足的探索机会"
        elif "12-24个月" in description:
            context["age_focus"] = "学步期需要平衡安全保护和独立探索"

        return context

    def _add_detailed_explanations(self, base_guidance: Dict) -> Dict[str, List[str]]:
        """为基础建议添加详细解释"""
        explanations = {}

        activities = base_guidance.get("age_appropriate_activities", [])
        if activities:
            explanations["activity_explanations"] = []
            for activity in activities:
                if "视觉刺激" in activity:
                    explanations["activity_explanations"].append(
                        f"{activity}：有助于视觉系统发育，促进大脑神经连接的建立"
                    )
                elif "俯卧时间" in activity:
                    explanations["activity_explanations"].append(
                        f"{activity}：增强颈部和背部肌肉力量，为后续运动发育打基础"
                    )
                elif "爬行" in activity:
                    explanations["activity_explanations"].append(
                        f"{activity}：促进四肢协调，增强空间感知能力"
                    )
                else:
                    explanations["activity_explanations"].append(f"{activity}：促进相应发育领域的技能发展")

        return explanations

    def _add_implementation_tips(self, base_guidance: Dict) -> Dict[str, List[str]]:
        """添加实施技巧"""
        tips = {
            "general_tips": [
                "每次活动时间不宜过长，观察婴儿的反应和情绪",
                "在婴儿清醒且心情愉快时进行活动",
                "保持耐心，每个婴儿的发育节奏不同",
                "如有疑虑，及时咨询儿科医生或发育专家"
            ],
            "safety_tips": [
                "始终保持监督，不要让婴儿独自与小物品接触",
                "定期检查玩具和用品的安全性",
                "创造安全的探索环境，移除潜在危险物品"
            ]
        }

        return tips

    def convert_dataset(self, dataset_path: str, output_path: str,
                       subset: str = "all", max_images: int = None) -> None:
        """
        批量转换整个数据集

        Args:
            dataset_path: CribHD数据集路径
            output_path: 输出文本数据集路径
            subset: 要转换的子集 ("all", "cribhd_b", "cribhd_t", "cribhd_c")
            max_images: 最大处理图像数量
        """
        print(f"开始转换CribHD数据集: {dataset_path}")
        print(f"输出路径: {output_path}")

        # 加载数据集
        dataset_info = self.load_cribhd_dataset(dataset_path)

        # 创建输出目录
        output_path = Path(output_path)
        output_path.mkdir(parents=True, exist_ok=True)

        # 转换结果存储
        converted_data = {
            "metadata": {
                "source_dataset": "CribHD",
                "conversion_time": datetime.now().isoformat(),
                "model_used": self.model_type,
                "total_images": 0,
                "subsets": []
            },
            "data": []
        }

        image_count = 0

        # 处理不同子集
        if subset == "all" or subset == "cribhd_b":
            print("处理CRIBHD-B (毯子数据)...")
            blanket_data = self._convert_subset(
                dataset_info["cribhd_b"], "blanket", max_images, image_count
            )
            converted_data["data"].extend(blanket_data)
            converted_data["metadata"]["subsets"].append("cribhd_b")
            image_count += len(blanket_data)

        if subset == "all" or subset == "cribhd_t":
            print("处理CRIBHD-T (玩具数据)...")
            toy_data = self._convert_subset(
                dataset_info["cribhd_t"], "toy", max_images, image_count
            )
            converted_data["data"].extend(toy_data)
            converted_data["metadata"]["subsets"].append("cribhd_t")
            image_count += len(toy_data)

        if subset == "all" or subset == "cribhd_c":
            print("处理CRIBHD-C (模拟危机场景数据)...")
            print("注意：CRIBHD-C包含120张使用仿真玩偶构建的高风险模拟场景")
            crisis_data = self._convert_subset(
                dataset_info["cribhd_c"], "combined", max_images, image_count
            )
            converted_data["data"].extend(crisis_data)
            converted_data["metadata"]["subsets"].append("cribhd_c")
            converted_data["metadata"]["cribhd_c_info"] = {
                "description": "模拟危机场景数据集",
                "total_images": len(crisis_data),
                "risk_level": "high",
                "special_notes": "使用仿真玩偶和道具构建的模拟场景，专门用于训练危机识别能力"
            }
            image_count += len(crisis_data)

        converted_data["metadata"]["total_images"] = image_count

        # 保存转换结果
        output_file = output_path / "cribhd_text_dataset.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(converted_data, f, ensure_ascii=False, indent=2)

        print(f"转换完成！共处理 {image_count} 张图像")
        print(f"结果保存至: {output_file}")

        # 生成统计报告
        self._generate_statistics_report(converted_data, output_path)

    def _convert_subset(self, subset_data: Dict, subset_type: str,
                       max_images: int = None, start_count: int = 0) -> List[Dict]:
        """转换数据子集"""
        converted_items = []

        if subset_type in ["blanket", "toy"]:
            # 处理有注释的数据
            for split in ["train", "test", "valid"]:
                if split not in subset_data or not subset_data[split]:
                    continue

                images = subset_data[split].get("images", [])
                annotations = subset_data[split].get("annotations", {})

                for i, image_path in enumerate(images):
                    if max_images and (start_count + len(converted_items)) >= max_images:
                        break

                    print(f"处理图像 {len(converted_items) + 1}: {image_path.name}")

                    # 获取对应的注释
                    image_annotations = self._get_image_annotations(
                        image_path.name, annotations
                    )

                    # 生成文本描述
                    description = self.generate_text_description(
                        str(image_path), image_annotations
                    )

                    # 添加额外信息
                    description.update({
                        "image_id": image_path.stem,
                        "image_path": str(image_path),
                        "subset_type": subset_type,
                        "split": split,
                        "hazard_category": subset_type
                    })

                    converted_items.append(description)

        elif subset_type == "combined":  # CRIBHD-C 危机场景
            images = subset_data.get("images", [])
            metadata = subset_data.get("metadata", {})
            annotations = subset_data.get("annotations", {})

            print(f"处理CRIBHD-C危机场景数据集:")
            print(f"  - 图像数量: {len(images)}")
            print(f"  - 预期数量: 120张")
            print(f"  - 特殊特征: {', '.join(metadata.get('special_features', []))}")

            for i, image_path in enumerate(images):
                if max_images and (start_count + len(converted_items)) >= max_images:
                    break

                print(f"处理危机场景图像 {len(converted_items) + 1}/{len(images)}: {image_path.name}")

                # 为CRIBHD-C生成特殊的注释信息
                cribhd_c_annotations = self._prepare_cribhd_c_annotations(
                    image_path.name, annotations, metadata
                )

                # 生成文本描述，传入特殊注释
                description = self.generate_text_description(
                    str(image_path), cribhd_c_annotations
                )

                # 添加CRIBHD-C特有的信息
                description.update({
                    "image_id": image_path.stem,
                    "image_path": str(image_path),
                    "subset_type": "crisis_scene",  # 更具体的类型
                    "split": "crisis",
                    "hazard_category": "high_risk",  # CRIBHD-C都是高风险场景
                    "scene_category": "simulated_crisis",
                    "expected_risks": ["面部遮挡", "四肢被困", "玩具靠近口鼻", "窒息风险"],
                    "dataset_notes": "使用仿真玩偶和道具构建的模拟危机场景"
                })

                converted_items.append(description)

        else:  # 其他未知类型
            print(f"警告: 未知的子集类型 {subset_type}")
            images = subset_data.get("images", [])
            for i, image_path in enumerate(images):
                if max_images and (start_count + len(converted_items)) >= max_images:
                    break

                print(f"处理图像 {len(converted_items) + 1}: {image_path.name}")

                description = self.generate_text_description(str(image_path))
                description.update({
                    "image_id": image_path.stem,
                    "image_path": str(image_path),
                    "subset_type": subset_type,
                    "split": "unknown",
                    "hazard_category": "unknown"
                })

                converted_items.append(description)

        return converted_items

    def _prepare_cribhd_c_annotations(self, image_name: str, annotations: Dict, metadata: Dict) -> Dict:
        """
        为CRIBHD-C图像准备特殊的注释信息
        CRIBHD-C是模拟危机场景，需要特殊处理
        """
        cribhd_c_annotations = {
            "image_name": image_name,
            "dataset_type": "CRIBHD-C",
            "scene_type": "simulated_crisis",
            "risk_level": "high",
            "special_instructions": {
                "focus_areas": [
                    "识别面部是否被遮挡",
                    "检查四肢活动是否受限",
                    "评估玩具与口鼻的距离",
                    "分析整体安全风险"
                ],
                "analysis_points": [
                    "这是使用仿真玩偶构建的模拟场景",
                    "重点关注各种潜在的安全风险",
                    "提供针对性的安全预防建议",
                    "评估真实场景中的风险等级"
                ]
            }
        }

        # 如果有具体的注释数据，合并进来
        if annotations and isinstance(annotations, dict):
            # 查找对应图像的注释
            if "images" in annotations:
                for img_info in annotations["images"]:
                    if img_info.get("file_name") == image_name:
                        cribhd_c_annotations["image_info"] = img_info
                        break

            # 添加类别信息
            if "categories" in annotations:
                cribhd_c_annotations["categories"] = annotations["categories"]

            # 添加具体的注释
            if "annotations" in annotations:
                image_annotations = []
                for ann in annotations["annotations"]:
                    if ann.get("image_id") and "image_info" in cribhd_c_annotations:
                        if ann["image_id"] == cribhd_c_annotations["image_info"].get("id"):
                            image_annotations.append(ann)

                if image_annotations:
                    cribhd_c_annotations["object_annotations"] = image_annotations

        # 添加元数据信息
        if metadata:
            cribhd_c_annotations["metadata"] = metadata

        return cribhd_c_annotations

    def _get_image_annotations(self, image_name: str, annotations: Dict) -> Dict:
        """获取特定图像的注释信息"""
        if not annotations:
            return {}

        # 查找对应的图像ID
        image_id = None
        for img in annotations.get("images", []):
            if img["file_name"] == image_name:
                image_id = img["id"]
                break

        if image_id is None:
            return {}

        # 获取该图像的所有注释
        image_annotations = []
        for ann in annotations.get("annotations", []):
            if ann["image_id"] == image_id:
                image_annotations.append(ann)

        return {
            "image_info": next((img for img in annotations.get("images", [])
                              if img["id"] == image_id), {}),
            "annotations": image_annotations,
            "categories": annotations.get("categories", [])
        }

    def _generate_statistics_report(self, converted_data: Dict, output_path: Path) -> None:
        """生成统计报告"""
        stats = {
            "总体统计": {
                "总图像数": converted_data["metadata"]["total_images"],
                "转换时间": converted_data["metadata"]["conversion_time"],
                "使用模型": converted_data["metadata"]["model_used"]
            },
            "子集分布": {},
            "年龄分布": {},
            "运动技能分布": {},
            "安全风险分布": {}
        }

        # 统计各子集数量
        for item in converted_data["data"]:
            subset_type = item.get("subset_type", "unknown")
            stats["子集分布"][subset_type] = stats["子集分布"].get(subset_type, 0) + 1

            # 年龄分布
            age = item.get("estimated_age_months", 0)
            age_group = f"{age//3*3}-{age//3*3+3}个月"
            stats["年龄分布"][age_group] = stats["年龄分布"].get(age_group, 0) + 1

            # 运动技能分布
            skills = item.get("motor_skills", [])
            for skill in skills:
                stats["运动技能分布"][skill] = stats["运动技能分布"].get(skill, 0) + 1

            # 安全风险分布
            risks = item.get("safety_assessment", {}).get("risks_detected", [])
            for risk in risks:
                stats["安全风险分布"][risk] = stats["安全风险分布"].get(risk, 0) + 1

        # 保存统计报告
        stats_file = output_path / "conversion_statistics.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)

        print(f"统计报告已保存至: {stats_file}")

    def export_to_formats(self, json_file: str, output_dir: str) -> None:
        """导出为多种格式"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # 加载JSON数据
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 导出为CSV格式
        self._export_to_csv(data, output_dir / "cribhd_text_dataset.csv")

        # 导出为训练格式
        self._export_for_training(data, output_dir)

        print(f"多格式导出完成，文件保存在: {output_dir}")

    def _export_to_csv(self, data: Dict, csv_file: Path) -> None:
        """导出为CSV格式"""
        import pandas as pd

        rows = []
        for item in data["data"]:
            row = {
                "image_id": item.get("image_id", ""),
                "image_path": item.get("image_path", ""),
                "subset_type": item.get("subset_type", ""),
                "estimated_age_months": item.get("estimated_age_months", 0),
                "motor_skills": "|".join(item.get("motor_skills", [])),
                "development_level": item.get("development_level", ""),
                "hazard_category": item.get("hazard_category", ""),
                "risks_detected": "|".join(item.get("safety_assessment", {}).get("risks_detected", [])),
                "raw_description": item.get("raw_description", "")
            }
            rows.append(row)

        df = pd.DataFrame(rows)
        df.to_csv(csv_file, index=False, encoding='utf-8')
        print(f"CSV文件已保存: {csv_file}")

    def _export_for_training(self, data: Dict, output_dir: Path) -> None:
        """导出为机器学习训练格式"""
        training_dir = output_dir / "training_format"
        training_dir.mkdir(exist_ok=True)

        # 按split分割数据
        splits = {"train": [], "test": [], "valid": []}

        for item in data["data"]:
            split = item.get("split", "train")
            if split in splits:
                training_item = {
                    "text": item.get("raw_description", ""),
                    "labels": {
                        "age_months": item.get("estimated_age_months", 0),
                        "motor_skills": item.get("motor_skills", []),
                        "safety_risks": item.get("safety_assessment", {}).get("risks_detected", []),
                        "hazard_category": item.get("hazard_category", "")
                    }
                }
                splits[split].append(training_item)

        # 保存各个split
        for split_name, split_data in splits.items():
            if split_data:
                split_file = training_dir / f"{split_name}.json"
                with open(split_file, 'w', encoding='utf-8') as f:
                    json.dump(split_data, f, ensure_ascii=False, indent=2)
                print(f"{split_name}数据已保存: {split_file}")


def load_config(config_path: str) -> Dict[str, Any]:
    """加载配置文件"""
    try:
        import yaml
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        return config
    except ImportError:
        print("错误: 需要安装PyYAML库来支持配置文件")
        print("请运行: pip install PyYAML")
        return {}
    except Exception as e:
        print(f"错误: 加载配置文件失败 {config_path}: {str(e)}")
        return {}

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="CribNet图像数据集转文本数据集")
    parser.add_argument("--config", help="配置文件路径 (YAML格式)")
    parser.add_argument("--dataset_path", help="CribHD数据集路径")
    parser.add_argument("--output_path", help="输出文本数据集路径")
    parser.add_argument("--model_type", default="qwen",
                       choices=["qwen", "gpt4v", "gemini", "local"], help="使用的模型类型")
    parser.add_argument("--api_key", help="API密钥")
    parser.add_argument("--subset", default="all",
                       choices=["all", "cribhd_b", "cribhd_t", "cribhd_c"],
                       help="要转换的子集")
    parser.add_argument("--max_images", type=int, help="最大处理图像数量")
    parser.add_argument("--export_formats", action="store_true",
                       help="导出多种格式")
    parser.add_argument("--use_llm_guidance", action="store_true",
                       help="使用LLM增强发育指导建议")

    args = parser.parse_args()

    # 处理配置文件
    config = {}
    if args.config:
        config = load_config(args.config)
        if not config:
            print("配置文件加载失败，退出程序")
            return

    # 合并命令行参数和配置文件参数（命令行参数优先）
    dataset_path = args.dataset_path or config.get('dataset', {}).get('source_path')
    output_path = args.output_path or config.get('dataset', {}).get('output_path')
    model_type = args.model_type
    if not args.model_type and config.get('model', {}).get('type'):
        model_type = config.get('model', {}).get('type')

    api_key = args.api_key or config.get('model', {}).get('api_key')
    subset = args.subset or config.get('dataset', {}).get('subset', 'all')
    max_images = args.max_images or config.get('dataset', {}).get('max_images')
    export_formats = args.export_formats or config.get('dataset', {}).get('export_formats', False)
    use_llm_guidance = args.use_llm_guidance or config.get('text_generation', {}).get('include_development_assessment', False)

    # 验证必需参数
    if not dataset_path:
        print("错误: 必须指定数据集路径 (--dataset_path 或在配置文件中设置)")
        return

    if not output_path:
        print("错误: 必须指定输出路径 (--output_path 或在配置文件中设置)")
        return

    # 显示配置信息
    print("运行配置:")
    print(f"  数据集路径: {dataset_path}")
    print(f"  输出路径: {output_path}")
    print(f"  模型类型: {model_type}")
    print(f"  API密钥: {'已设置' if api_key else '未设置'}")
    print(f"  处理子集: {subset}")
    print(f"  最大图像数: {max_images or '无限制'}")
    print(f"  LLM增强指导: {'是' if use_llm_guidance else '否'}")
    print(f"  导出多格式: {'是' if export_formats else '否'}")

    if config:
        print(f"  配置来源: 配置文件 {args.config}")
    else:
        print(f"  配置来源: 命令行参数")

    # 创建转换器
    converter = CribNetTextConverter(
        model_type=model_type,
        api_key=api_key,
        use_llm_guidance=use_llm_guidance,
        config=config
    )

    # 转换数据集
    converter.convert_dataset(
        dataset_path=dataset_path,
        output_path=output_path,
        subset=subset,
        max_images=max_images
    )

    # 导出多种格式
    if export_formats:
        json_file = Path(output_path) / "cribhd_text_dataset.json"
        converter.export_to_formats(str(json_file), output_path)


if __name__ == "__main__":
    main()
