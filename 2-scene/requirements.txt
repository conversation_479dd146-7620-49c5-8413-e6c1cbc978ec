# CribNet图像转文本数据集转换器依赖包

# 基础依赖
numpy>=1.21.0
opencv-python>=4.5.0
pandas>=1.3.0
Pillow>=8.3.0
pathlib2>=2.3.6

# 配置文件支持
PyYAML>=6.0

# 可选依赖 - GPT-4V支持
openai>=1.0.0

# 可选依赖 - Gemini支持
google-generativeai>=0.3.0

# 可选依赖 - Qwen支持
dashscope>=1.14.0
requests>=2.28.0

# 可选依赖 - 图像处理增强
scikit-image>=0.18.0
matplotlib>=3.4.0

# 可选依赖 - 本地模型支持
torch>=1.9.0
torchvision>=0.10.0
transformers>=4.20.0

# 可选依赖 - BLIP模型支持
# git+https://github.com/salesforce/BLIP.git

# 可选依赖 - CLIP模型支持
# git+https://github.com/openai/CLIP.git

# 开发和测试依赖
pytest>=6.2.0
pytest-cov>=2.12.0
black>=21.0.0
flake8>=3.9.0

# 数据处理
tqdm>=4.62.0
jsonlines>=2.0.0

# 统计分析
scipy>=1.7.0
seaborn>=0.11.0

# 文档生成
sphinx>=4.0.0
sphinx-rtd-theme>=0.5.0
