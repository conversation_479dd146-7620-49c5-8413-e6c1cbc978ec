#!/usr/bin/env python3
"""
CribHD + Qwen API 快速开始脚本
专门用于婴幼儿家庭场景运动监测文本数据集生成
"""

import os
import sys
import json
from pathlib import Path
from cribnet_to_text_converter import CribNetTextConverter

def check_qwen_dependencies():
    """检查Qwen相关依赖"""
    print("检查Qwen依赖包...")
    
    required_packages = [
        ("dashscope", "dashscope"),
        ("requests", "requests"),
        ("cv2", "opencv-python"),
        ("numpy", "numpy"),
        ("pandas", "pandas")
    ]
    
    missing_packages = []
    
    for package, pip_name in required_packages:
        try:
            __import__(package)
            print(f"✓ {pip_name}")
        except ImportError:
            print(f"✗ {pip_name}")
            missing_packages.append(pip_name)
    
    if missing_packages:
        print(f"\n缺少依赖包，请运行:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    print("✓ 所有依赖包已安装")
    return True

def setup_qwen_credentials():
    """设置Qwen API凭证"""
    print("\n=== Qwen API 设置 ===")
    
    # 检查环境变量
    api_key = os.getenv("DASHSCOPE_API_KEY")
    
    if not api_key:
        print("未找到DASHSCOPE_API_KEY环境变量")
        print("\n获取API密钥步骤:")
        print("1. 访问: https://dashscope.console.aliyun.com/")
        print("2. 登录阿里云账号")
        print("3. 开通DashScope服务")
        print("4. 创建API密钥")
        
        # 交互式输入API密钥
        api_key = input("\n请输入您的DashScope API密钥: ").strip()
        
        if not api_key:
            print("API密钥不能为空")
            return None
        
        # 询问是否保存到环境变量
        save_env = input("是否保存到环境变量? (y/n): ").strip().lower()
        if save_env == 'y':
            print(f"\n请运行以下命令设置环境变量:")
            print(f"export DASHSCOPE_API_KEY='{api_key}'")
            print("或将其添加到 ~/.bashrc 或 ~/.zshrc 文件中")
    
    print(f"✓ API密钥已设置: {api_key[:8]}...")
    return api_key

def interactive_cribhd_setup():
    """交互式CribHD数据集设置"""
    print("\n=== CribHD数据集设置 ===")
    
    # 数据集路径
    print("CribHD数据集下载地址: https://coe.northeastern.edu/Research/AClab/CribHD/")
    dataset_path = input("请输入CribHD数据集路径: ").strip()
    
    if not dataset_path:
        dataset_path = "./data/CribHD"
        print(f"使用默认路径: {dataset_path}")
    
    # 检查数据集是否存在
    if not os.path.exists(dataset_path):
        print(f"⚠️  数据集路径不存在: {dataset_path}")
        create_demo = input("是否创建演示数据? (y/n): ").strip().lower()
        if create_demo == 'y':
            create_demo_dataset(dataset_path)
        else:
            print("请下载CribHD数据集后重新运行")
            return None
    
    # 选择子集
    print("\nCribHD子集说明:")
    print("- CribHD-T: 玩具子集 (1,000张图像)")
    print("- CribHD-B: 毯子子集 (500张图像)")
    print("- CribHD-C: 模拟危机场景 (120张图像)")
    
    print("\n选择要处理的子集:")
    print("1. CribHD-T (玩具)")
    print("2. CribHD-B (毯子)")
    print("3. CribHD-C (危机场景)")
    print("4. 全部子集")
    
    subset_choice = input("请选择 (1-4): ").strip()
    subset_map = {
        "1": ("cribhd_t", "玩具"),
        "2": ("cribhd_b", "毯子"),
        "3": ("cribhd_c", "危机场景"),
        "4": ("all", "全部")
    }
    
    subset, subset_name = subset_map.get(subset_choice, ("cribhd_t", "玩具"))
    
    # 处理数量限制
    print(f"\n选择了: {subset_name}子集")
    max_images = input("最大处理图像数量 (建议10-50，避免API费用过高): ").strip()
    max_images = int(max_images) if max_images.isdigit() else 10
    
    # 输出路径
    output_path = f"./output/qwen_cribhd_{subset}_dataset"
    
    return {
        "dataset_path": dataset_path,
        "subset": subset,
        "subset_name": subset_name,
        "max_images": max_images,
        "output_path": output_path
    }

def create_demo_dataset(dataset_path):
    """创建演示数据集结构"""
    print(f"创建演示数据集结构: {dataset_path}")
    
    # 创建目录结构
    directories = [
        "CRIBHD-T/COCO/train/images",
        "CRIBHD-T/COCO/test/images", 
        "CRIBHD-T/COCO/valid/images",
        "CRIBHD-B/COCO/train/images",
        "CRIBHD-B/COCO/test/images",
        "CRIBHD-B/COCO/valid/images",
        "CRIBHD-C/images"
    ]
    
    for directory in directories:
        Path(dataset_path) / directory).mkdir(parents=True, exist_ok=True)
    
    # 创建示例注释文件
    sample_annotation = {
        "images": [],
        "annotations": [],
        "categories": [
            {"id": 1, "name": "toy"},
            {"id": 2, "name": "blanket"}
        ]
    }
    
    annotation_files = [
        "CRIBHD-T/COCO/train/_annotations.coco.json",
        "CRIBHD-T/COCO/test/_annotations.coco.json",
        "CRIBHD-T/COCO/valid/_annotations.coco.json",
        "CRIBHD-B/COCO/train/_annotations.coco.json",
        "CRIBHD-B/COCO/test/_annotations.coco.json",
        "CRIBHD-B/COCO/valid/_annotations.coco.json"
    ]
    
    for ann_file in annotation_files:
        ann_path = Path(dataset_path) / ann_file
        with open(ann_path, 'w', encoding='utf-8') as f:
            json.dump(sample_annotation, f, indent=2)
    
    # 创建说明文件
    readme_content = """
# CribHD演示数据集

这是一个演示用的数据集结构。要使用真实数据，请：

1. 访问 https://coe.northeastern.edu/Research/AClab/CribHD/
2. 下载完整的CribHD数据集
3. 将图像文件放入对应的images目录
4. 确保注释文件格式正确

## 数据集结构
- CRIBHD-T: 玩具检测数据集
- CRIBHD-B: 毯子检测数据集  
- CRIBHD-C: 模拟危机场景数据集
"""
    
    readme_path = Path(dataset_path) / "README.md"
    with open(readme_path, 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✓ 演示数据集结构已创建")
    print("⚠️  请添加真实的图像文件以进行实际处理")

def run_qwen_conversion(api_key, config):
    """运行Qwen转换"""
    print(f"\n=== 开始Qwen转换 ===")
    print(f"数据集: {config['dataset_path']}")
    print(f"子集: {config['subset_name']}")
    print(f"最大图像数: {config['max_images']}")
    print(f"输出路径: {config['output_path']}")
    
    try:
        # 创建转换器
        converter = CribNetTextConverter(model_type="qwen", api_key=api_key)
        
        # 执行转换
        converter.convert_dataset(
            dataset_path=config['dataset_path'],
            output_path=config['output_path'],
            subset=config['subset'],
            max_images=config['max_images']
        )
        
        print(f"\n✓ 转换完成！")
        return True
        
    except Exception as e:
        print(f"转换失败: {str(e)}")
        return False

def show_results_summary(output_path):
    """显示结果摘要"""
    print(f"\n=== 结果摘要 ===")
    
    json_file = Path(output_path) / "cribhd_text_dataset.json"
    if not json_file.exists():
        print("结果文件不存在")
        return
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    total_images = len(data.get("data", []))
    print(f"处理图像总数: {total_images}")
    
    # 显示一个示例
    if data.get("data"):
        sample = data["data"][0]
        print(f"\n示例结果:")
        print(f"图像ID: {sample.get('image_id', 'N/A')}")
        print(f"年龄估计: {sample.get('estimated_age_months', 0)}个月")
        print(f"风险等级: {sample.get('risk_assessment', {}).get('risk_level', 'N/A')}")
        print(f"描述片段: {sample.get('raw_description', '')[:100]}...")
    
    # 显示文件列表
    print(f"\n生成的文件:")
    output_dir = Path(output_path)
    for file_path in output_dir.rglob("*"):
        if file_path.is_file():
            size = file_path.stat().st_size
            print(f"  {file_path.name} ({size} bytes)")

def main():
    """主函数"""
    print("CribHD + Qwen API 婴幼儿监测文本数据集生成工具")
    print("=" * 60)
    print("专门用于婴幼儿家庭场景运动监测和育幼健康指导")
    
    # 1. 检查依赖
    if not check_qwen_dependencies():
        sys.exit(1)
    
    # 2. 设置API凭证
    api_key = setup_qwen_credentials()
    if not api_key:
        sys.exit(1)
    
    # 3. 配置数据集
    config = interactive_cribhd_setup()
    if not config:
        sys.exit(1)
    
    # 4. 确认配置
    print(f"\n=== 配置确认 ===")
    print(f"模型: Qwen VL Plus")
    print(f"数据集: {config['dataset_path']}")
    print(f"子集: {config['subset_name']}")
    print(f"最大图像数: {config['max_images']}")
    print(f"输出路径: {config['output_path']}")
    
    confirm = input("\n确认开始处理? (y/n): ").strip().lower()
    if confirm != 'y':
        print("已取消")
        sys.exit(0)
    
    # 5. 执行转换
    success = run_qwen_conversion(api_key, config)
    
    if success:
        # 6. 显示结果
        show_results_summary(config['output_path'])
        
        print(f"\n🎉 处理完成！")
        print(f"结果保存在: {config['output_path']}")
        print(f"\n下一步建议:")
        print(f"1. 查看生成的文本描述质量")
        print(f"2. 使用生成的数据进行LLM微调")
        print(f"3. 评估模型在婴幼儿监测任务上的表现")
    else:
        print(f"\n❌ 处理失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
