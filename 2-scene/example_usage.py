#!/usr/bin/env python3
"""
CribNet图像转文本数据集使用示例
演示如何使用转换器处理CribHD数据集
"""

import os
import json
from pathlib import Path
from cribnet_to_text_converter import CribNetTextConverter

def example_single_image_conversion():
    """示例：转换单张图像"""
    print("=== 单张图像转换示例 ===")
    
    # 创建转换器（使用本地模型）
    converter = CribNetTextConverter(model_type="local")
    
    # 假设有一张测试图像
    image_path = "test_image.jpg"
    
    if os.path.exists(image_path):
        # 生成文本描述
        description = converter.generate_text_description(image_path)
        
        print("生成的文本描述：")
        print(json.dumps(description, ensure_ascii=False, indent=2))
    else:
        print(f"测试图像不存在: {image_path}")
        print("请提供一张婴儿床场景的图像进行测试")

def example_dataset_conversion():
    """示例：转换整个数据集"""
    print("\n=== 数据集批量转换示例 ===")
    
    # 数据集路径（请根据实际情况修改）
    dataset_path = "/path/to/CribHD"
    output_path = "./output/cribhd_text_dataset"
    
    # 检查数据集是否存在
    if not os.path.exists(dataset_path):
        print(f"数据集路径不存在: {dataset_path}")
        print("请下载CribHD数据集并修改路径")
        return
    
    # 创建转换器
    converter = CribNetTextConverter(model_type="local")
    
    # 转换数据集（限制处理5张图像作为示例）
    converter.convert_dataset(
        dataset_path=dataset_path,
        output_path=output_path,
        subset="cribhd_b",  # 只处理毯子数据作为示例
        max_images=5
    )
    
    print(f"转换完成，结果保存在: {output_path}")

def example_with_gpt4v():
    """示例：使用GPT-4V进行转换"""
    print("\n=== GPT-4V转换示例 ===")
    
    # 需要设置OpenAI API密钥
    api_key = os.getenv("OPENAI_API_KEY")
    
    if not api_key:
        print("请设置OPENAI_API_KEY环境变量")
        print("export OPENAI_API_KEY='your_api_key_here'")
        return
    
    # 创建转换器
    converter = CribNetTextConverter(model_type="gpt4v", api_key=api_key)
    
    # 转换单张图像
    image_path = "test_image.jpg"
    if os.path.exists(image_path):
        description = converter.generate_text_description(image_path)
        print("GPT-4V生成的描述：")
        print(description.get("raw_description", ""))
    else:
        print("请提供测试图像")

def example_custom_prompt():
    """示例：自定义提示词"""
    print("\n=== 自定义提示词示例 ===")
    
    class CustomConverter(CribNetTextConverter):
        def _build_prompt(self, annotations=None):
            """自定义提示词"""
            return """
请用中文详细描述这张婴儿床图像，重点关注：

1. 婴儿的年龄估计和发育状态
2. 婴儿的当前动作和姿势
3. 婴儿床内的物品（玩具、毯子等）
4. 潜在的安全风险
5. 环境光线和整体安全性

请提供专业、准确的描述。
"""
    
    # 使用自定义转换器
    converter = CustomConverter(model_type="local")
    
    image_path = "test_image.jpg"
    if os.path.exists(image_path):
        description = converter.generate_text_description(image_path)
        print("自定义提示词生成的描述：")
        print(description.get("raw_description", ""))

def example_data_analysis():
    """示例：分析转换结果"""
    print("\n=== 数据分析示例 ===")
    
    # 假设已经有转换结果
    json_file = "./output/cribhd_text_dataset/cribhd_text_dataset.json"
    
    if not os.path.exists(json_file):
        print(f"转换结果文件不存在: {json_file}")
        print("请先运行数据集转换")
        return
    
    # 加载数据
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 基础统计
    total_images = len(data.get("data", []))
    print(f"总图像数: {total_images}")
    
    # 年龄分布统计
    age_distribution = {}
    motor_skills_count = {}
    safety_risks_count = {}
    
    for item in data.get("data", []):
        # 年龄分布
        age = item.get("estimated_age_months", 0)
        age_group = f"{age//3*3}-{age//3*3+3}个月"
        age_distribution[age_group] = age_distribution.get(age_group, 0) + 1
        
        # 运动技能统计
        skills = item.get("motor_skills", [])
        for skill in skills:
            motor_skills_count[skill] = motor_skills_count.get(skill, 0) + 1
        
        # 安全风险统计
        risks = item.get("safety_assessment", {}).get("risks_detected", [])
        for risk in risks:
            safety_risks_count[risk] = safety_risks_count.get(risk, 0) + 1
    
    print("\n年龄分布:")
    for age_group, count in sorted(age_distribution.items()):
        print(f"  {age_group}: {count}")
    
    print("\n运动技能频次:")
    for skill, count in sorted(motor_skills_count.items(), key=lambda x: x[1], reverse=True):
        print(f"  {skill}: {count}")
    
    print("\n安全风险频次:")
    for risk, count in sorted(safety_risks_count.items(), key=lambda x: x[1], reverse=True):
        print(f"  {risk}: {count}")

def example_export_formats():
    """示例：导出多种格式"""
    print("\n=== 多格式导出示例 ===")
    
    json_file = "./output/cribhd_text_dataset/cribhd_text_dataset.json"
    output_dir = "./output/exported_formats"
    
    if not os.path.exists(json_file):
        print(f"源文件不存在: {json_file}")
        return
    
    # 创建转换器
    converter = CribNetTextConverter(model_type="local")
    
    # 导出多种格式
    converter.export_to_formats(json_file, output_dir)
    
    print(f"多格式文件已导出到: {output_dir}")
    
    # 列出导出的文件
    output_path = Path(output_dir)
    if output_path.exists():
        print("导出的文件:")
        for file_path in output_path.rglob("*"):
            if file_path.is_file():
                print(f"  {file_path}")

def create_sample_data():
    """创建示例数据用于测试"""
    print("\n=== 创建示例数据 ===")
    
    # 创建示例JSON数据
    sample_data = {
        "metadata": {
            "source_dataset": "CribHD",
            "conversion_time": "2024-01-01T12:00:00",
            "total_images": 3
        },
        "data": [
            {
                "image_id": "sample001",
                "estimated_age_months": 8,
                "motor_skills": ["坐立", "抓握"],
                "development_level": "normal",
                "safety_assessment": {
                    "risks_detected": ["小玩具"],
                    "recommendations": ["移除小物品"]
                },
                "raw_description": "8个月大的婴儿独立坐在婴儿床中，能够稳定保持坐姿。婴儿正在用双手抓握一个彩色玩具，展现了良好的手眼协调能力。",
                "subset_type": "toy",
                "split": "train"
            },
            {
                "image_id": "sample002",
                "estimated_age_months": 12,
                "motor_skills": ["站立", "扶站"],
                "development_level": "normal",
                "safety_assessment": {
                    "risks_detected": [],
                    "recommendations": []
                },
                "raw_description": "12个月大的婴儿在婴儿床中扶着栏杆站立，显示出良好的下肢力量和平衡能力。环境安全，无明显风险。",
                "subset_type": "blanket",
                "split": "valid"
            },
            {
                "image_id": "sample003",
                "estimated_age_months": 6,
                "motor_skills": ["翻身", "俯卧抬头"],
                "development_level": "normal",
                "safety_assessment": {
                    "risks_detected": ["毯子覆盖"],
                    "recommendations": ["调整毯子位置"]
                },
                "raw_description": "6个月大的婴儿俯卧在婴儿床中，能够抬头并用前臂支撑。毯子部分覆盖身体，需要注意不要覆盖面部。",
                "subset_type": "combined",
                "split": "test"
            }
        ]
    }
    
    # 保存示例数据
    output_dir = Path("./output/sample_data")
    output_dir.mkdir(parents=True, exist_ok=True)
    
    sample_file = output_dir / "sample_dataset.json"
    with open(sample_file, 'w', encoding='utf-8') as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    print(f"示例数据已创建: {sample_file}")
    return str(sample_file)

def main():
    """主函数 - 运行所有示例"""
    print("CribNet图像转文本数据集转换器使用示例")
    print("=" * 50)
    
    # 创建示例数据
    sample_file = create_sample_data()
    
    # 运行各种示例
    example_single_image_conversion()
    example_dataset_conversion()
    example_with_gpt4v()
    example_custom_prompt()
    
    # 使用示例数据进行分析
    if sample_file:
        # 临时修改路径用于演示
        original_path = "./output/cribhd_text_dataset/cribhd_text_dataset.json"
        demo_path = sample_file
        
        # 创建符号链接或复制文件用于演示
        import shutil
        os.makedirs("./output/cribhd_text_dataset", exist_ok=True)
        shutil.copy2(demo_path, original_path)
        
        example_data_analysis()
        example_export_formats()
    
    print("\n所有示例运行完成！")
    print("请根据实际需求修改路径和参数。")

if __name__ == "__main__":
    main()
