#!/usr/bin/env python3
"""
使用Qwen API处理CribHD数据集的示例
专门针对婴幼儿家庭场景运动监测和育幼健康指导
"""

import os
import json
from pathlib import Path
from cribnet_to_text_converter import CribNetTextConverter

def setup_qwen_api():
    """设置Qwen API"""
    print("=== Qwen API 设置 ===")
    
    # 检查API密钥
    api_key = os.getenv("DASHSCOPE_API_KEY")
    if not api_key:
        print("请设置DASHSCOPE_API_KEY环境变量")
        print("获取API密钥：https://dashscope.console.aliyun.com/")
        print("设置方法：export DASHSCOPE_API_KEY='your_api_key_here'")
        return None
    
    print(f"✓ API密钥已设置: {api_key[:8]}...")
    return api_key

def test_qwen_single_image():
    """测试Qwen处理单张图像"""
    print("\n=== 单张图像测试 ===")
    
    api_key = setup_qwen_api()
    if not api_key:
        return
    
    # 创建转换器
    converter = CribNetTextConverter(model_type="qwen", api_key=api_key)
    
    # 测试图像路径（请替换为实际的CribHD图像路径）
    test_image = "test_crib_image.jpg"
    
    if not os.path.exists(test_image):
        print(f"测试图像不存在: {test_image}")
        print("请提供一张CribHD数据集中的图像进行测试")
        return
    
    print(f"处理图像: {test_image}")
    
    # 生成描述
    try:
        description = converter.generate_text_description(test_image)
        
        print("\n生成的结构化描述:")
        print("=" * 50)
        print(json.dumps(description, ensure_ascii=False, indent=2))
        
        # 保存结果
        output_file = "qwen_single_result.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(description, f, ensure_ascii=False, indent=2)
        print(f"\n结果已保存到: {output_file}")
        
    except Exception as e:
        print(f"处理失败: {str(e)}")

def process_cribhd_subset():
    """处理CribHD子集"""
    print("\n=== CribHD子集处理 ===")
    
    api_key = setup_qwen_api()
    if not api_key:
        return
    
    # 数据集路径配置
    dataset_path = input("请输入CribHD数据集路径: ").strip()
    if not dataset_path:
        dataset_path = "/path/to/CribHD"
    
    if not os.path.exists(dataset_path):
        print(f"数据集路径不存在: {dataset_path}")
        return
    
    # 选择子集
    print("\n选择要处理的子集:")
    print("1. CribHD-T (玩具子集, 1000张)")
    print("2. CribHD-B (毯子子集, 500张)")
    print("3. CribHD-C (模拟危机场景, 120张)")
    print("4. 全部子集")
    
    choice = input("请选择 (1-4): ").strip()
    subset_map = {
        "1": "cribhd_t",
        "2": "cribhd_b", 
        "3": "cribhd_c",
        "4": "all"
    }
    
    subset = subset_map.get(choice, "cribhd_t")
    
    # 限制处理数量（避免API费用过高）
    max_images = int(input("最大处理图像数量 (建议10-50): ") or "10")
    
    # 输出路径
    output_path = f"./output/qwen_cribhd_{subset}_text_dataset"
    
    print(f"\n开始处理:")
    print(f"- 数据集: {dataset_path}")
    print(f"- 子集: {subset}")
    print(f"- 最大图像数: {max_images}")
    print(f"- 输出路径: {output_path}")
    
    # 创建转换器
    converter = CribNetTextConverter(model_type="qwen", api_key=api_key)
    
    try:
        # 执行转换
        converter.convert_dataset(
            dataset_path=dataset_path,
            output_path=output_path,
            subset=subset,
            max_images=max_images
        )
        
        print(f"\n✓ 处理完成！结果保存在: {output_path}")
        
        # 显示统计信息
        show_conversion_stats(output_path)
        
    except Exception as e:
        print(f"处理失败: {str(e)}")

def show_conversion_stats(output_path):
    """显示转换统计信息"""
    print("\n=== 转换统计 ===")
    
    # 读取主要结果文件
    json_file = Path(output_path) / "cribhd_text_dataset.json"
    if not json_file.exists():
        print("结果文件不存在")
        return
    
    with open(json_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    total_images = len(data.get("data", []))
    print(f"总处理图像数: {total_images}")
    
    # 统计各类信息
    age_dist = {}
    risk_dist = {}
    toy_materials = {}
    blanket_coverage = {}
    
    for item in data.get("data", []):
        # 年龄分布
        age = item.get("estimated_age_months", 0)
        age_group = f"{age//3*3}-{age//3*3+3}个月"
        age_dist[age_group] = age_dist.get(age_group, 0) + 1
        
        # 风险分布
        risk_level = item.get("risk_assessment", {}).get("risk_level", "无风险")
        risk_dist[risk_level] = risk_dist.get(risk_level, 0) + 1
        
        # 玩具材质分布
        toy_material = item.get("toy_properties", {}).get("material", "")
        if toy_material:
            toy_materials[toy_material] = toy_materials.get(toy_material, 0) + 1
        
        # 毯子覆盖分布
        blanket_cov = item.get("blanket_properties", {}).get("coverage", "")
        if blanket_cov:
            blanket_coverage[blanket_cov] = blanket_coverage.get(blanket_cov, 0) + 1
    
    print(f"\n年龄分布:")
    for age_group, count in sorted(age_dist.items()):
        print(f"  {age_group}: {count}")
    
    print(f"\n风险等级分布:")
    for risk, count in sorted(risk_dist.items()):
        print(f"  {risk}: {count}")
    
    if toy_materials:
        print(f"\n玩具材质分布:")
        for material, count in toy_materials.items():
            print(f"  {material}: {count}")
    
    if blanket_coverage:
        print(f"\n毯子覆盖情况:")
        for coverage, count in blanket_coverage.items():
            print(f"  {coverage}: {count}")

def create_training_dataset():
    """创建用于LLM微调的训练数据集"""
    print("\n=== 创建LLM训练数据集 ===")
    
    # 输入文件
    input_file = input("请输入转换结果JSON文件路径: ").strip()
    if not input_file or not os.path.exists(input_file):
        print("文件不存在，使用默认路径")
        input_file = "./output/qwen_cribhd_text_dataset/cribhd_text_dataset.json"
    
    if not os.path.exists(input_file):
        print(f"文件不存在: {input_file}")
        return
    
    # 读取数据
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 创建训练格式
    training_data = []
    
    for item in data.get("data", []):
        # 构建训练样本
        system_prompt = "你是一位专业的婴幼儿发育评估和家庭安全监测专家，能够分析婴儿床场景并提供专业的评估和指导建议。"
        
        # 用户输入（简化的场景描述）
        user_input = f"请分析这个婴儿床场景：{item.get('raw_description', '')[:200]}..."
        
        # 助手回复（结构化的专业分析）
        assistant_response = generate_structured_response(item)
        
        training_sample = {
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_input},
                {"role": "assistant", "content": assistant_response}
            ]
        }
        
        training_data.append(training_sample)
    
    # 保存训练数据
    output_file = "cribhd_llm_training_data.jsonl"
    with open(output_file, 'w', encoding='utf-8') as f:
        for sample in training_data:
            f.write(json.dumps(sample, ensure_ascii=False) + '\n')
    
    print(f"✓ 训练数据集已创建: {output_file}")
    print(f"总样本数: {len(training_data)}")

def generate_structured_response(item):
    """生成结构化的专业回复"""
    age_months = item.get("estimated_age_months", 0)
    age_group = f"{age_months//3*3}-{age_months//3*3+3}个月"
    
    motor_skills = item.get("motor_skills", [])
    risk_assessment = item.get("risk_assessment", {})
    development_guidance = item.get("development_guidance", {})
    
    response = f"""## 专业分析报告

### 基础评估
- **年龄估计**: {age_group}
- **发育水平**: {item.get('development_level', '正常')}
- **运动技能**: {', '.join(motor_skills) if motor_skills else '需进一步观察'}

### 安全风险评估
- **风险等级**: {risk_assessment.get('risk_level', '无风险')}
- **具体风险**: {', '.join(risk_assessment.get('specific_risks', [])) if risk_assessment.get('specific_risks') else '无明显风险'}

### 育儿指导建议
- **适龄活动**: {', '.join(development_guidance.get('age_appropriate_activities', [])[:2])}
- **安全建议**: {', '.join(development_guidance.get('safety_recommendations', [])[:2])}
- **下阶段目标**: {', '.join(development_guidance.get('next_milestones', [])[:2])}

### 专业建议
基于当前观察，建议家长重点关注安全防护，同时通过适当的互动活动促进婴儿的健康发育。如有疑虑，建议咨询儿科医生。"""
    
    return response

def main():
    """主函数"""
    print("CribHD数据集 + Qwen API 婴幼儿监测文本数据集生成工具")
    print("=" * 60)
    
    while True:
        print("\n请选择操作:")
        print("1. 测试单张图像处理")
        print("2. 批量处理CribHD子集")
        print("3. 创建LLM训练数据集")
        print("4. 退出")
        
        choice = input("请选择 (1-4): ").strip()
        
        if choice == "1":
            test_qwen_single_image()
        elif choice == "2":
            process_cribhd_subset()
        elif choice == "3":
            create_training_dataset()
        elif choice == "4":
            print("再见！")
            break
        else:
            print("无效选择，请重试")

if __name__ == "__main__":
    main()
