"""
运行小规模的LLM增强数据生成
避免超时问题，生成少量高质量数据
"""

from llm_enhanced_data_generator import LLMEnhancedDataGenerator

def main():
    print("🚀 开始小规模LLM增强数据生成...")
    
    generator = LLMEnhancedDataGenerator()
    
    # 使用较小的参数避免超时
    qa_pairs = generator.generate_complete_llm_dataset(
        milestone_count=10,      # 减少到10个
        literature_count=8,      # 减少到8个
        scenario_count=12,       # 减少到12个
        comparative_count=6      # 减少到6个
    )
    
    print(f"\n🎉 小规模LLM增强数据生成完成！")
    print(f"📊 总计生成: {len(qa_pairs)} 条高质量数据")
    
    # 显示一些统计信息
    if qa_pairs:
        avg_q_len = sum(len(qa['question']) for qa in qa_pairs) / len(qa_pairs)
        avg_a_len = sum(len(qa['answer']) for qa in qa_pairs) / len(qa_pairs)
        
        print(f"📏 平均问题长度: {avg_q_len:.1f} 字符")
        print(f"📏 平均回答长度: {avg_a_len:.1f} 字符")
        
        # 显示前3个问答对的摘要
        print(f"\n📝 生成内容预览:")
        for i, qa in enumerate(qa_pairs[:3]):
            print(f"  {i+1}. Q: {qa['question'][:60]}...")
            print(f"     A: {qa['answer'][:80]}...")
            print()

if __name__ == "__main__":
    main()
