"""
综合数据生成管理器
整合基础生成、文献提取和LLM增强三种数据生成方式
"""

import json
import random
from typing import List, Dict
from data_generator import MotorDevelopmentDataGenerator
from literature_data_generator import LiteratureDataGenerator
from llm_enhanced_data_generator import LLMEnhancedDataGenerator
from quality_control import DatasetQualityController

class ComprehensiveDataGenerator:
    """综合数据生成管理器"""
    
    def __init__(self):
        self.base_generator = MotorDevelopmentDataGenerator()
        self.literature_generator = LiteratureDataGenerator()
        self.llm_generator = LLMEnhancedDataGenerator()
        self.quality_controller = DatasetQualityController()
        
    def generate_multi_source_dataset(self, 
                                    base_count: int = 200,
                                    literature_count: int = 100,
                                    llm_count: int = 150) -> List[Dict]:
        """生成多源融合数据集"""
        print("🚀 开始生成多源融合数据集...")
        
        all_data = []
        
        # 1. 生成基础数据
        print("\n📊 生成基础规则数据...")
        self.base_generator.generate_complete_dataset(
            assessment_count=base_count//3,
            guidance_count=base_count//3,
            milestone_count=base_count//3
        )
        
        # 加载基础数据
        try:
            with open("gross_motor_dataset_generated.jsonl", 'r', encoding='utf-8') as f:
                base_data = [json.loads(line) for line in f]
            print(f"✅ 基础数据: {len(base_data)} 条")
            all_data.extend(self._standardize_data(base_data, "base_generated"))
        except Exception as e:
            print(f"❌ 加载基础数据失败: {e}")
        
        # 2. 生成文献数据
        print("\n📚 生成文献提取数据...")
        try:
            self.literature_generator.generate_complete_dataset()
            
            with open("literature_based_dataset.jsonl", 'r', encoding='utf-8') as f:
                literature_data = [json.loads(line) for line in f]
            print(f"✅ 文献数据: {len(literature_data)} 条")
            all_data.extend(self._standardize_data(literature_data[:literature_count], "literature_extracted"))
        except Exception as e:
            print(f"❌ 生成文献数据失败: {e}")
        
        # 3. 生成LLM增强数据
        print("\n🤖 生成LLM增强数据...")
        try:
            llm_qa_pairs = self.llm_generator.generate_complete_llm_dataset(
                milestone_count=llm_count//4,
                literature_count=llm_count//4,
                scenario_count=llm_count//3,
                comparative_count=llm_count//6
            )
            
            llm_data = []
            for qa in llm_qa_pairs:
                llm_data.append({
                    "instruction": qa['question'],
                    "output": qa['answer'],
                    "source": "llm_enhanced",
                    "category": "llm_generated"
                })
            
            print(f"✅ LLM增强数据: {len(llm_data)} 条")
            all_data.extend(self._standardize_data(llm_data, "llm_enhanced"))
            
        except Exception as e:
            print(f"❌ 生成LLM数据失败: {e}")
        
        # 4. 数据清理和去重
        print("\n🧹 数据清理和去重...")
        cleaned_data = self._clean_and_deduplicate(all_data)
        
        # 5. 数据平衡
        print("⚖️ 数据平衡处理...")
        balanced_data = self._balance_dataset(cleaned_data)
        
        # 6. 保存最终数据集
        print("💾 保存最终数据集...")
        self._save_comprehensive_dataset(balanced_data)
        
        # 7. 质量检查
        print("🔍 执行质量检查...")
        self._perform_quality_check(balanced_data)
        
        print(f"\n🎉 多源融合数据集生成完成！")
        print(f"📊 最终数据量: {len(balanced_data)} 条")
        
        return balanced_data
    
    def _standardize_data(self, data: List[Dict], source: str) -> List[Dict]:
        """标准化数据格式"""
        standardized = []
        
        for item in data:
            standardized_item = {
                "instruction": item.get("instruction", ""),
                "output": item.get("output", ""),
                "source": source,
                "category": item.get("category", "general"),
                "age_group": item.get("age_group", ""),
                "metadata": item.get("metadata", {})
            }
            
            # 确保必要字段不为空
            if standardized_item["instruction"] and standardized_item["output"]:
                standardized.append(standardized_item)
        
        return standardized
    
    def _clean_and_deduplicate(self, data: List[Dict]) -> List[Dict]:
        """清理和去重数据"""
        seen = set()
        cleaned_data = []
        
        for item in data:
            # 创建唯一标识
            key = (item["instruction"][:100], item["output"][:100])
            
            if key not in seen:
                seen.add(key)
                
                # 清理文本
                item["instruction"] = self._clean_text(item["instruction"])
                item["output"] = self._clean_text(item["output"])
                
                # 过滤质量不佳的数据
                if self._is_quality_data(item):
                    cleaned_data.append(item)
        
        print(f"🔄 去重清理: {len(data)} -> {len(cleaned_data)} 条")
        return cleaned_data
    
    def _clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = ' '.join(text.split())
        
        # 移除特殊字符
        text = text.replace('\r', '').replace('\t', ' ')
        
        # 确保句子结构完整
        if text and not text.endswith(('。', '！', '？', '.', '!', '?')):
            if '建议' in text or '注意' in text:
                text += '。'
        
        return text.strip()
    
    def _is_quality_data(self, item: Dict) -> bool:
        """判断数据质量"""
        instruction = item["instruction"]
        output = item["output"]
        
        # 长度检查
        if len(instruction) < 10 or len(instruction) > 200:
            return False
        if len(output) < 20 or len(output) > 1000:
            return False
        
        # 内容相关性检查
        motor_keywords = ['宝宝', '个月', '发育', '运动', '抬头', '翻身', '坐', '爬', '站', '走']
        if not any(keyword in instruction + output for keyword in motor_keywords):
            return False
        
        # 避免明显错误的内容
        problematic_patterns = [
            '时该反射',
            '因为获得新的运动能力必须首先抑制原始反射',
            '头和躯干的分离运动对称性紧张性颈反射'
        ]
        
        for pattern in problematic_patterns:
            if pattern in instruction:
                return False
        
        return True
    
    def _balance_dataset(self, data: List[Dict]) -> List[Dict]:
        """平衡数据集"""
        # 按来源分组
        source_groups = {}
        for item in data:
            source = item["source"]
            if source not in source_groups:
                source_groups[source] = []
            source_groups[source].append(item)
        
        print("📊 数据来源分布:")
        for source, items in source_groups.items():
            print(f"  {source}: {len(items)} 条")
        
        # 平衡各来源数据量
        max_per_source = 300  # 每个来源最多300条
        balanced_data = []
        
        for source, items in source_groups.items():
            if len(items) > max_per_source:
                sampled_items = random.sample(items, max_per_source)
                balanced_data.extend(sampled_items)
                print(f"  {source}: 采样 {len(sampled_items)} 条")
            else:
                balanced_data.extend(items)
                print(f"  {source}: 保留 {len(items)} 条")
        
        # 随机打乱
        random.shuffle(balanced_data)
        
        return balanced_data
    
    def _save_comprehensive_dataset(self, data: List[Dict]) -> None:
        """保存综合数据集"""
        # JSONL格式
        with open("comprehensive_motor_dataset.jsonl", 'w', encoding='utf-8') as f:
            for item in data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        # Alpaca格式
        alpaca_data = []
        for item in data:
            alpaca_data.append({
                "instruction": item["instruction"],
                "input": "",
                "output": item["output"]
            })
        
        with open("comprehensive_motor_alpaca.json", 'w', encoding='utf-8') as f:
            json.dump(alpaca_data, f, ensure_ascii=False, indent=2)
        
        # ChatML格式
        with open("comprehensive_motor_chatml.jsonl", 'w', encoding='utf-8') as f:
            for item in data:
                chatml_item = {
                    "messages": [
                        {"role": "user", "content": item["instruction"]},
                        {"role": "assistant", "content": item["output"]}
                    ]
                }
                f.write(json.dumps(chatml_item, ensure_ascii=False) + '\n')
        
        print(f"💾 综合数据集已保存:")
        print(f"  - JSONL格式: comprehensive_motor_dataset.jsonl")
        print(f"  - Alpaca格式: comprehensive_motor_alpaca.json")
        print(f"  - ChatML格式: comprehensive_motor_chatml.jsonl")
    
    def _perform_quality_check(self, data: List[Dict]) -> None:
        """执行质量检查"""
        # 临时保存数据用于质量检查
        temp_file = "temp_comprehensive_dataset.jsonl"
        with open(temp_file, 'w', encoding='utf-8') as f:
            for item in data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        # 执行质量检查
        report = self.quality_controller.generate_quality_report(temp_file)
        self.quality_controller.print_quality_report(report)
        
        # 保存质量报告
        with open("comprehensive_dataset_quality_report.json", 'w', encoding='utf-8') as f:
            serializable_report = self._make_serializable(report)
            json.dump(serializable_report, f, ensure_ascii=False, indent=2)
        
        # 删除临时文件
        import os
        os.remove(temp_file)
        
        print("📋 质量报告已保存: comprehensive_dataset_quality_report.json")
    
    def _make_serializable(self, obj):
        """使对象可序列化"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, set) or isinstance(obj, frozenset):
            return list(obj)
        else:
            return obj
    
    def generate_dataset_summary(self, data: List[Dict]) -> None:
        """生成数据集总结"""
        print("\n" + "="*60)
        print("📊 综合数据集总结")
        print("="*60)
        
        # 统计各来源数量
        sources = {}
        categories = {}
        
        for item in data:
            source = item.get('source', 'unknown')
            sources[source] = sources.get(source, 0) + 1
            
            category = item.get('category', 'unknown')
            categories[category] = categories.get(category, 0) + 1
        
        print(f"\n📈 总体规模: {len(data)} 条训练数据")
        
        print(f"\n📚 按数据来源分布:")
        for source, count in sorted(sources.items()):
            percentage = count / len(data) * 100
            print(f"  {source}: {count} 条 ({percentage:.1f}%)")
        
        print(f"\n🏷️  按类别分布:")
        for category, count in sorted(categories.items()):
            percentage = count / len(data) * 100
            print(f"  {category}: {count} 条 ({percentage:.1f}%)")
        
        # 数据质量统计
        instruction_lengths = [len(item['instruction']) for item in data]
        output_lengths = [len(item['output']) for item in data]
        
        print(f"\n📏 文本长度统计:")
        print(f"  平均问题长度: {sum(instruction_lengths)/len(instruction_lengths):.1f} 字符")
        print(f"  平均回答长度: {sum(output_lengths)/len(output_lengths):.1f} 字符")

if __name__ == "__main__":
    generator = ComprehensiveDataGenerator()
    dataset = generator.generate_multi_source_dataset(
        base_count=250,
        literature_count=150,
        llm_count=200
    )
    generator.generate_dataset_summary(dataset)
