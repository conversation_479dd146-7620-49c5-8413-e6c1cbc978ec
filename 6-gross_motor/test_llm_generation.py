"""
测试LLM数据生成
简化版本，用于测试LLM连接和生成功能
"""

import json
from qwen_api_client import QwenAPIClient, QwenConfig

def test_llm_connection():
    """测试LLM连接"""
    print("🔧 测试LLM连接...")
    
    try:
        # 配置LLM客户端
        config = QwenConfig(api_key="sk-5eba46fbcff649d5bf28313bc865de10")
        llm_client = QwenAPIClient(config)
        
        # 简单测试
        system_prompt = "你是一位专业的儿童发育专家。"
        test_prompt = "请简单介绍一下3个月宝宝的粗大运动发育特点。"
        
        print("📤 发送测试请求...")
        response = llm_client.generate_text(test_prompt, system_prompt)
        
        if response:
            print("✅ LLM连接成功！")
            print(f"📝 响应内容: {response[:200]}...")
            return True
        else:
            print("❌ LLM响应为空")
            return False
            
    except Exception as e:
        print(f"❌ LLM连接失败: {e}")
        return False

def test_simple_generation():
    """测试简单的数据生成"""
    print("\n🎯 测试简单数据生成...")
    
    try:
        config = QwenConfig(api_key="sk-5eba46fbcff649d5bf28313bc865de10")
        llm_client = QwenAPIClient(config)
        
        system_prompt = """你是一位专业的儿童发育专家。请生成一个关于婴幼儿粗大运动发育的问答对。

要求：
1. 问题要贴近家长的实际关切
2. 回答要专业准确，包含具体指导
3. 格式：问题：xxx 回答：xxx"""
        
        prompt = "请生成一个关于6个月宝宝坐立发育的问答对。"
        
        print("📤 生成问答对...")
        response = llm_client.generate_text(prompt, system_prompt)
        
        if response:
            print("✅ 生成成功！")
            print(f"📝 生成内容:\n{response}")
            
            # 尝试解析
            qa_pairs = parse_simple_qa(response)
            print(f"🔍 解析结果: {len(qa_pairs)} 个问答对")
            
            for i, qa in enumerate(qa_pairs):
                print(f"  {i+1}. Q: {qa.get('question', 'N/A')[:50]}...")
                print(f"     A: {qa.get('answer', 'N/A')[:50]}...")
            
            return qa_pairs
        else:
            print("❌ 生成失败")
            return []
            
    except Exception as e:
        print(f"❌ 生成过程出错: {e}")
        return []

def parse_simple_qa(response):
    """简单解析问答对"""
    qa_pairs = []
    
    import re
    
    # 尝试多种解析模式
    patterns = [
        r'问题?\s*[:：]\s*(.*?)\s*回?答案?\s*[:：]\s*(.*?)(?=问题?[:：]|$)',
        r'Q\s*[:：]\s*(.*?)\s*A\s*[:：]\s*(.*?)(?=Q[:：]|$)',
        r'问：\s*(.*?)\s*答：\s*(.*?)(?=问：|$)'
    ]
    
    for pattern in patterns:
        matches = re.findall(pattern, response, re.DOTALL | re.IGNORECASE)
        if matches:
            for match in matches:
                if len(match) == 2:
                    question, answer = match
                    question = question.strip()
                    answer = answer.strip()
                    
                    if len(question) > 5 and len(answer) > 10:
                        qa_pairs.append({
                            'question': question,
                            'answer': answer
                        })
            break
    
    # 如果没有匹配到，尝试简单分割
    if not qa_pairs and len(response) > 20:
        lines = [line.strip() for line in response.split('\n') if line.strip()]
        if len(lines) >= 2:
            # 假设第一行是问题，其余是答案
            question = lines[0]
            answer = ' '.join(lines[1:])
            
            qa_pairs.append({
                'question': question,
                'answer': answer
            })
    
    return qa_pairs

def test_batch_generation():
    """测试批量生成"""
    print("\n🚀 测试批量生成...")
    
    try:
        config = QwenConfig(api_key="sk-5eba46fbcff649d5bf28313bc865de10")
        llm_client = QwenAPIClient(config)
        
        scenarios = [
            "3个月宝宝的头部控制发育",
            "6个月宝宝的坐立能力",
            "9个月宝宝的爬行发育",
            "12个月宝宝的站立行走"
        ]
        
        all_qa_pairs = []
        
        for i, scenario in enumerate(scenarios):
            print(f"📊 生成场景 {i+1}/{len(scenarios)}: {scenario}")
            
            system_prompt = """你是一位专业的儿童发育专家。请生成一个关于婴幼儿粗大运动发育的问答对。

要求：
1. 问题要贴近家长的实际关切
2. 回答要专业准确，包含具体指导
3. 格式：问题：xxx 回答：xxx"""
            
            prompt = f"请生成一个关于{scenario}的问答对。"
            
            try:
                response = llm_client.generate_text(prompt, system_prompt)
                if response:
                    qa_pairs = parse_simple_qa(response)
                    all_qa_pairs.extend(qa_pairs)
                    print(f"  ✅ 生成了 {len(qa_pairs)} 个问答对")
                else:
                    print(f"  ❌ 生成失败")
            except Exception as e:
                print(f"  ❌ 生成出错: {e}")
        
        print(f"\n🎉 批量生成完成！总计: {len(all_qa_pairs)} 个问答对")
        
        # 保存结果
        if all_qa_pairs:
            with open("test_llm_generated.json", 'w', encoding='utf-8') as f:
                json.dump(all_qa_pairs, f, ensure_ascii=False, indent=2)
            print("💾 结果已保存到: test_llm_generated.json")
        
        return all_qa_pairs
        
    except Exception as e:
        print(f"❌ 批量生成失败: {e}")
        return []

def main():
    """主函数"""
    print("🧪 LLM数据生成测试")
    print("=" * 50)
    
    # 1. 测试连接
    if not test_llm_connection():
        print("❌ LLM连接失败，无法继续测试")
        return
    
    # 2. 测试简单生成
    simple_qa = test_simple_generation()
    if not simple_qa:
        print("❌ 简单生成失败，无法继续测试")
        return
    
    # 3. 测试批量生成
    batch_qa = test_batch_generation()
    
    print(f"\n📊 测试总结:")
    print(f"  - 简单生成: {len(simple_qa)} 个问答对")
    print(f"  - 批量生成: {len(batch_qa)} 个问答对")
    print(f"  - 总计: {len(simple_qa) + len(batch_qa)} 个问答对")
    
    if len(simple_qa) + len(batch_qa) > 0:
        print("✅ LLM数据生成测试成功！")
    else:
        print("❌ LLM数据生成测试失败！")

if __name__ == "__main__":
    main()
