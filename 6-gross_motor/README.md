# 婴幼儿粗大运动发展指导数据集

## 项目概述

本项目旨在构建一个专门针对0-3岁婴幼儿粗大运动发展指导的高质量文本数据集，用于微调支持育幼健康监测和指导功能的大语言模型(LLM)。

## 数据集特点

### 🎯 专业性
- 基于中华人民共和国卫生行业标准 (WS/T 580—2017)
- 涵盖0-3岁婴幼儿粗大运动发育里程碑
- 包含专业的评估方法和指导建议

### 📊 数据结构
- **评估类数据**: 发育状况评估和建议
- **指导类数据**: 具体的训练方法和注意事项  
- **里程碑数据**: 发育里程碑检查和标准

### 🌈 多样性
- 涵盖8个年龄组：0-1月、1-3月、4-6月、7-9月、10-12月、13-18月、19-24月、25-36月
- 包含6大运动技能类别：头部控制、翻身、坐位、爬行、站立、行走等
- 支持多种交互场景：评估、指导、里程碑检查、问题回应等

## 文件结构

```
├── dataset_schema.py          # 数据集架构定义
├── data_generator.py          # 基础数据生成器
├── qwen_api_client.py         # Qwen API客户端
├── quality_control.py         # 质量控制模块
├── main_dataset_builder.py    # 主构建程序
├── README.md                  # 使用说明
└── 生成的数据集文件/
    ├── gross_motor_dataset_generated.jsonl      # 主数据集(JSONL格式)
    ├── gross_motor_alpaca_generated.json        # Alpaca格式
    ├── gross_motor_dataset_generated_chatml.jsonl # ChatML格式
    └── gross_motor_dataset_generated_quality_report.json # 质量报告
```

## 快速开始

### 1. 环境准备

```bash
pip install pandas requests PyPDF2 pdfplumber
```

### 2. 基础数据集生成

```bash
# 生成基础数据集（不使用API）
python main_dataset_builder.py --assessment-count 200 --guidance-count 200 --milestone-count 100
```

### 3. 使用Qwen API增强（可选）

```bash
# 设置API密钥
export QWEN_API_KEY="your-api-key-here"

# 生成增强数据集
python main_dataset_builder.py --use-qwen --enhancement-count 100
```

### 4. 单独运行组件

```bash
# 仅生成基础数据
python data_generator.py

# 仅进行质量检查
python quality_control.py

# 仅使用API生成数据
python qwen_api_client.py
```

## 数据集格式

### JSONL格式 (推荐用于训练)
```json
{
  "instruction": "我的宝宝6个月了，还不会翻身，正常吗？",
  "output": "6个月宝宝不会翻身需要关注...",
  "age_group": "4-6月",
  "interaction_type": "评估",
  "metadata": {}
}
```

### Alpaca格式
```json
{
  "instruction": "我的宝宝6个月了，还不会翻身，正常吗？",
  "input": "",
  "output": "6个月宝宝不会翻身需要关注..."
}
```

### ChatML格式
```json
{
  "messages": [
    {"role": "user", "content": "我的宝宝6个月了，还不会翻身，正常吗？"},
    {"role": "assistant", "content": "6个月宝宝不会翻身需要关注..."}
  ]
}
```

## 质量控制

数据集包含完整的质量控制机制：

### 📋 完整性检查
- 必需字段完整性
- 空值检测
- 格式一致性

### 📝 文本质量
- 长度合理性检查
- 编码问题检测
- 内容相关性验证

### 🎯 专业性验证
- 粗大运动关键词覆盖
- 年龄相关性检查
- 医学术语准确性

### 🌈 多样性评估
- 数据去重
- 表达多样性
- 场景覆盖度

## 数据集统计

当前版本数据集包含：
- **总条目数**: 334条
- **质量评分**: 100/100
- **关键词覆盖率**: 100%
- **数据多样性**: 94.9%

### 按交互类型分布
- 评估类: 150条 (45%)
- 指导类: 150条 (45%)  
- 里程碑检查: 34条 (10%)

### 按年龄组分布
- 0-1月: 13条
- 1-3月: 17条
- 4-6月: 24条
- 7-9月: 32条
- 10-12月: 36条
- 13-18月: 48条
- 19-24月: 52条
- 25-36月: 112条

## 使用建议

### 🎯 适用场景
- 婴幼儿发育评估系统
- 育儿指导聊天机器人
- 儿科医疗辅助工具
- 家长教育平台

### 💡 微调建议
- 建议使用LoRA或QLoRA进行高效微调
- 推荐batch_size: 4-8
- 学习率: 1e-4 到 5e-4
- 训练轮数: 3-5 epochs

### ⚠️ 注意事项
- 本数据集仅供研究和教育用途
- 实际应用需结合专业医疗建议
- 建议定期更新以反映最新医学标准

## API配置

### Qwen API设置
```python
# 方法1: 环境变量
export QWEN_API_KEY="your-api-key"

# 方法2: 代码配置
config = QwenConfig(
    api_key="your-api-key",
    model="qwen-turbo",
    temperature=0.7
)
```

## 扩展开发

### 添加新的数据类型
1. 在`dataset_schema.py`中定义新的数据结构
2. 在`data_generator.py`中实现生成逻辑
3. 更新质量控制规则

### 集成其他API
1. 参考`qwen_api_client.py`的实现
2. 实现对应的客户端类
3. 在主程序中集成

## 贡献指南

欢迎提交Issue和Pull Request来改进数据集质量：

1. 报告数据质量问题
2. 建议新的数据类型
3. 优化生成算法
4. 完善文档说明

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**免责声明**: 本数据集仅供研究和教育用途，不能替代专业医疗建议。在实际应用中，请务必咨询专业的儿科医生或发育专家。
