"""
数据集分析器 - 分析婴幼儿粗大运动发展数据集
"""

import json
import os
from collections import defaultdict, Counter
from typing import Dict, List, Any

class DatasetAnalyzer:
    """数据集分析器"""
    
    def __init__(self, dataset_dir: str = "infant_gross_motor_datasets"):
        self.dataset_dir = dataset_dir
        self.analysis_results = {}
    
    def analyze_jsonl_file(self, filepath: str) -> Dict[str, Any]:
        """分析JSONL文件"""
        stats = {
            'total_entries': 0,
            'sources': Counter(),
            'categories': Counter(),
            'age_groups': Counter(),
            'avg_instruction_length': 0,
            'avg_output_length': 0,
            'sample_entries': []
        }
        
        instruction_lengths = []
        output_lengths = []
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if line.strip():
                        try:
                            entry = json.loads(line.strip())
                            stats['total_entries'] += 1
                            
                            # 统计来源
                            if 'source' in entry:
                                stats['sources'][entry['source']] += 1
                            
                            # 统计类别
                            if 'category' in entry:
                                stats['categories'][entry['category']] += 1
                            
                            # 统计年龄组
                            if 'age' in entry:
                                stats['age_groups'][str(entry['age'])] += 1
                            elif 'age_group' in entry:
                                stats['age_groups'][entry['age_group']] += 1
                            
                            # 统计长度
                            if 'instruction' in entry:
                                instruction_lengths.append(len(entry['instruction']))
                            if 'output' in entry:
                                output_lengths.append(len(entry['output']))
                            
                            # 收集样本
                            if len(stats['sample_entries']) < 3:
                                stats['sample_entries'].append({
                                    'instruction': entry.get('instruction', '')[:100] + '...',
                                    'output': entry.get('output', '')[:150] + '...',
                                    'source': entry.get('source', 'unknown')
                                })
                        
                        except json.JSONDecodeError as e:
                            print(f"JSON解析错误在文件 {filepath} 第 {line_num} 行: {e}")
                            continue
        
        except FileNotFoundError:
            print(f"文件未找到: {filepath}")
            return stats
        
        # 计算平均长度
        if instruction_lengths:
            stats['avg_instruction_length'] = sum(instruction_lengths) / len(instruction_lengths)
        if output_lengths:
            stats['avg_output_length'] = sum(output_lengths) / len(output_lengths)
        
        return stats
    
    def analyze_json_file(self, filepath: str) -> Dict[str, Any]:
        """分析JSON文件（Alpaca格式）"""
        stats = {
            'total_entries': 0,
            'avg_instruction_length': 0,
            'avg_input_length': 0,
            'avg_output_length': 0,
            'sample_entries': []
        }
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
                if isinstance(data, list):
                    stats['total_entries'] = len(data)
                    
                    instruction_lengths = []
                    input_lengths = []
                    output_lengths = []
                    
                    for entry in data:
                        if 'instruction' in entry:
                            instruction_lengths.append(len(entry['instruction']))
                        if 'input' in entry:
                            input_lengths.append(len(entry['input']))
                        if 'output' in entry:
                            output_lengths.append(len(entry['output']))
                        
                        # 收集样本
                        if len(stats['sample_entries']) < 3:
                            stats['sample_entries'].append({
                                'instruction': entry.get('instruction', '')[:100] + '...',
                                'input': entry.get('input', ''),
                                'output': entry.get('output', '')[:150] + '...'
                            })
                    
                    # 计算平均长度
                    if instruction_lengths:
                        stats['avg_instruction_length'] = sum(instruction_lengths) / len(instruction_lengths)
                    if input_lengths:
                        stats['avg_input_length'] = sum(input_lengths) / len(input_lengths)
                    if output_lengths:
                        stats['avg_output_length'] = sum(output_lengths) / len(output_lengths)
        
        except (FileNotFoundError, json.JSONDecodeError) as e:
            print(f"分析文件 {filepath} 时出错: {e}")
        
        return stats
    
    def analyze_all_datasets(self) -> Dict[str, Any]:
        """分析所有数据集"""
        results = {}
        
        if not os.path.exists(self.dataset_dir):
            print(f"数据集目录不存在: {self.dataset_dir}")
            return results
        
        for filename in os.listdir(self.dataset_dir):
            filepath = os.path.join(self.dataset_dir, filename)
            
            if filename.endswith('.jsonl'):
                print(f"分析JSONL文件: {filename}")
                results[filename] = self.analyze_jsonl_file(filepath)
                results[filename]['format'] = 'JSONL'
            
            elif filename.endswith('.json') and 'alpaca' in filename:
                print(f"分析Alpaca JSON文件: {filename}")
                results[filename] = self.analyze_json_file(filepath)
                results[filename]['format'] = 'Alpaca JSON'
        
        return results
    
    def generate_summary(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """生成总结报告"""
        summary = {
            'total_datasets': len(results),
            'total_entries': 0,
            'datasets_by_type': defaultdict(list),
            'main_datasets': [],
            'enhanced_datasets': []
        }
        
        for filename, stats in results.items():
            summary['total_entries'] += stats.get('total_entries', 0)
            
            # 按类型分类
            if 'final' in filename:
                summary['main_datasets'].append(filename)
            elif any(keyword in filename for keyword in ['enhanced', 'literature', 'llm']):
                summary['enhanced_datasets'].append(filename)
            
            # 按格式分类
            format_type = stats.get('format', 'unknown')
            summary['datasets_by_type'][format_type].append(filename)
        
        return summary

if __name__ == "__main__":
    analyzer = DatasetAnalyzer()
    results = analyzer.analyze_all_datasets()
    summary = analyzer.generate_summary(results)
    
    # 保存分析结果
    with open('dataset_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump({
            'detailed_results': results,
            'summary': summary
        }, f, ensure_ascii=False, indent=2)
    
    print("数据集分析完成！结果已保存到 dataset_analysis_results.json")
