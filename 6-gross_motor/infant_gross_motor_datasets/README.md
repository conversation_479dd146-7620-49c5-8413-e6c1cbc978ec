# 婴幼儿粗大运动发展指导数据集

## 数据集概述

这是一个专门用于训练AI模型进行婴幼儿粗大运动发展指导的综合数据集。数据集包含了从多个权威来源整合的高质量问答对，涵盖了0-36个月婴幼儿的粗大运动发展里程碑、评估标准、训练指导和异常识别等内容。

## 📊 数据集统计

- **总数据集数量**: 17个
- **总条目数**: 4,586条
- **支持格式**: JSONL, Alpaca JSON, ChatML
- **覆盖年龄**: 0-36个月
- **语言**: 中文

## 🗂️ 数据集结构

### 主要数据集 (推荐使用)

| 文件名 | 格式 | 条目数 | 描述 |
|--------|------|--------|------|
| `final_gross_motor_dataset.jsonl` | JSONL | 614 | **主要综合数据集** - 整合了所有高质量数据源 |
| `final_gross_motor_alpaca.json` | Alpaca | 614 | 主要数据集的Alpaca格式版本 |
| `final_gross_motor_chatml.jsonl` | ChatML | 614 | 主要数据集的ChatML格式版本 |

### 增强数据集

| 文件名 | 格式 | 条目数 | 数据来源 | 特点 |
|--------|------|--------|----------|------|
| `guidance_enhanced_dataset.jsonl` | JSONL | 104 | Excel指导文件 | 基于权威发育指导标准 |
| `literature_based_dataset.jsonl` | JSONL | 186 | 医学文献 | 专业医学知识提取 |
| `llm_enhanced_dataset.jsonl` | JSONL | 26 | LLM生成 | AI增强的专业问答 |
| `comprehensive_motor_dataset.jsonl` | JSONL | 758 | 多源整合 | 最全面的数据集版本 |

### 基础生成数据集

| 文件名 | 格式 | 条目数 | 描述 |
|--------|------|--------|------|
| `gross_motor_dataset_generated.jsonl` | JSONL | 534 | 规则生成的基础数据 |
| `gross_motor_dataset_generated_alpaca.json` | Alpaca | 534 | 基础数据的Alpaca格式 |

## 📋 数据格式说明

### JSONL 格式
```json
{
  "instruction": "我的宝宝3个月了，这个阶段的粗大运动发育应该是什么样的？",
  "output": "3个月宝宝粗大运动发育指导：\n\n• 发育特点：俯卧能持久抬头至45°，扶着坐时头向前倾，头稍晃、不稳\n• 观察要点：注意宝宝动作的协调性和稳定性\n• 发育意义：为后续更复杂的运动技能奠定基础\n• 个体差异：每个宝宝发育进度略有不同，保持耐心很重要\n\n⚠️ 如有发育异常或疑虑，请及时咨询专业医生。",
  "source": "guidance_enhanced",
  "age": 3,
  "category": "guidance_enhanced"
}
```

### Alpaca 格式
```json
{
  "instruction": "我的宝宝3个月了，这个阶段的粗大运动发育应该是什么样的？",
  "input": "",
  "output": "3个月宝宝粗大运动发育指导：..."
}
```

### ChatML 格式
```json
{
  "messages": [
    {
      "role": "user",
      "content": "我的宝宝3个月了，这个阶段的粗大运动发育应该是什么样的？"
    },
    {
      "role": "assistant", 
      "content": "3个月宝宝粗大运动发育指导：..."
    }
  ]
}
```

## 🎯 数据集特点

### 1. 多源数据整合
- **权威医学文献**: 从4本专业医学教材中提取知识
- **标准化量表**: 基于0-6岁儿童发育行为评估量表
- **专业指导文件**: 整合婴幼儿粗大动作指导Excel数据
- **LLM增强**: 使用通义千问API进行智能增强

### 2. 全面的年龄覆盖
- **0-6个月**: 基础反射和头部控制
- **6-12个月**: 坐立、爬行、站立
- **12-24个月**: 行走、跑步、跳跃
- **24-36个月**: 复杂运动技能

### 3. 丰富的问答类型
- **发育评估**: "我的宝宝X个月了，这样的表现正常吗？"
- **训练指导**: "如何帮助宝宝练习某项技能？"
- **异常识别**: "什么情况下需要担心发育问题？"
- **对比分析**: "不同月龄的发育差异是什么？"

### 4. 高质量保证
- **专业审核**: 基于权威医学标准
- **格式统一**: 标准化的数据结构
- **质量控制**: 多维度质量评估
- **去重处理**: 避免数据重复

## 🚀 使用建议

### 推荐数据集选择

1. **通用训练**: 使用 `final_gross_motor_dataset.jsonl` (614条)
2. **大规模训练**: 使用 `comprehensive_motor_dataset.jsonl` (758条)
3. **特定增强**: 根据需要选择对应的增强数据集

### 训练框架适配

- **LLaMA/Alpaca**: 使用 `*_alpaca.json` 格式
- **ChatGLM/Qwen**: 使用 `*_chatml.jsonl` 格式
- **通用框架**: 使用 `*.jsonl` 格式

## 📚 数据来源

### 医学文献来源
1. 《人体发育学粗大运动》第2版 - 李晓捷主编
2. 《人体发育学粗大运动》第2版 - 江钟立主编  
3. 《人体发育学粗大运动》- 左天香、徐冬晨主编
4. 《人体发育学学习指导及习题集》粗大运动 - 陈翔主编

### 评估工具
- 0岁～6岁儿童发育行为评估量表
- 婴幼儿粗大动作指导标准

## ⚠️ 使用注意事项

1. **医学免责声明**: 本数据集仅用于AI模型训练，不能替代专业医学诊断
2. **个体差异**: 每个婴幼儿发育进度不同，数据仅供参考
3. **及时就医**: 如有发育异常疑虑，应及时咨询专业医生
4. **数据更新**: 医学知识在不断发展，建议定期更新数据集

## 📄 许可证

本数据集仅供学术研究和AI模型训练使用。使用时请遵循相关医学伦理和数据使用规范。

## 🤝 贡献

如果您发现数据质量问题或有改进建议，欢迎提出Issue或Pull Request。

## 📞 联系方式

如有任何问题或建议，请通过以下方式联系：
- 项目维护者: [您的联系方式]
- 技术支持: [技术支持邮箱]

---

**最后更新**: 2024年12月
**版本**: v1.0
**数据集大小**: 约15MB (所有格式)
