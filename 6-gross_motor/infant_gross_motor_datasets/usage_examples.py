"""
婴幼儿粗大运动发展指导数据集使用示例
"""

import json
import pandas as pd
from typing import List, Dict, Any

class DatasetLoader:
    """数据集加载器"""
    
    def __init__(self, dataset_dir: str = "./"):
        self.dataset_dir = dataset_dir
    
    def load_jsonl(self, filename: str) -> List[Dict[str, Any]]:
        """加载JSONL格式数据"""
        data = []
        filepath = f"{self.dataset_dir}/{filename}"
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        data.append(json.loads(line.strip()))
        except FileNotFoundError:
            print(f"文件未找到: {filepath}")
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
        
        return data
    
    def load_alpaca_json(self, filename: str) -> List[Dict[str, Any]]:
        """加载Alpaca JSON格式数据"""
        filepath = f"{self.dataset_dir}/{filename}"
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"文件未找到: {filepath}")
            return []
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            return []

def example_1_basic_loading():
    """示例1: 基础数据加载"""
    print("=== 示例1: 基础数据加载 ===")
    
    loader = DatasetLoader()
    
    # 加载主要数据集
    main_data = loader.load_jsonl("final_gross_motor_dataset.jsonl")
    print(f"主要数据集条目数: {len(main_data)}")
    
    # 查看第一条数据
    if main_data:
        print("第一条数据示例:")
        print(f"问题: {main_data[0]['instruction'][:50]}...")
        print(f"回答: {main_data[0]['output'][:100]}...")
        print(f"来源: {main_data[0].get('source', 'unknown')}")
        print()

def example_2_age_filtering():
    """示例2: 按年龄筛选数据"""
    print("=== 示例2: 按年龄筛选数据 ===")
    
    loader = DatasetLoader()
    data = loader.load_jsonl("final_gross_motor_dataset.jsonl")
    
    # 筛选3个月的数据
    age_3_data = [item for item in data if item.get('age') == 3]
    print(f"3个月相关数据: {len(age_3_data)} 条")
    
    # 筛选6-12个月的数据
    age_6_12_data = [item for item in data 
                     if isinstance(item.get('age'), int) and 6 <= item.get('age') <= 12]
    print(f"6-12个月数据: {len(age_6_12_data)} 条")
    print()

def example_3_source_analysis():
    """示例3: 数据来源分析"""
    print("=== 示例3: 数据来源分析 ===")
    
    loader = DatasetLoader()
    data = loader.load_jsonl("final_gross_motor_dataset.jsonl")
    
    # 统计数据来源
    sources = {}
    for item in data:
        source = item.get('source', 'unknown')
        sources[source] = sources.get(source, 0) + 1
    
    print("数据来源分布:")
    for source, count in sources.items():
        print(f"  {source}: {count} 条")
    print()

def example_4_convert_to_training_format():
    """示例4: 转换为训练格式"""
    print("=== 示例4: 转换为训练格式 ===")
    
    loader = DatasetLoader()
    data = loader.load_jsonl("final_gross_motor_dataset.jsonl")
    
    # 转换为简单的问答对格式
    qa_pairs = []
    for item in data:
        qa_pairs.append({
            "question": item.get('instruction', ''),
            "answer": item.get('output', ''),
            "metadata": {
                "age": item.get('age'),
                "source": item.get('source'),
                "category": item.get('category')
            }
        })
    
    print(f"转换后的问答对数量: {len(qa_pairs)}")
    
    # 保存为新格式
    with open('training_qa_pairs.json', 'w', encoding='utf-8') as f:
        json.dump(qa_pairs, f, ensure_ascii=False, indent=2)
    
    print("已保存为 training_qa_pairs.json")
    print()

def example_5_pandas_analysis():
    """示例5: 使用Pandas进行数据分析"""
    print("=== 示例5: 使用Pandas进行数据分析 ===")
    
    loader = DatasetLoader()
    data = loader.load_jsonl("final_gross_motor_dataset.jsonl")
    
    # 转换为DataFrame
    df = pd.DataFrame(data)
    
    print("数据集基本信息:")
    print(f"总条目数: {len(df)}")
    print(f"列名: {list(df.columns)}")
    
    # 分析问题长度分布
    if 'instruction' in df.columns:
        df['question_length'] = df['instruction'].str.len()
        print(f"问题平均长度: {df['question_length'].mean():.1f} 字符")
        print(f"问题长度范围: {df['question_length'].min()} - {df['question_length'].max()}")
    
    # 分析年龄分布
    if 'age' in df.columns:
        age_dist = df['age'].value_counts().sort_index()
        print("年龄分布:")
        for age, count in age_dist.head(10).items():
            print(f"  {age}个月: {count} 条")
    print()

def example_6_quality_check():
    """示例6: 数据质量检查"""
    print("=== 示例6: 数据质量检查 ===")
    
    loader = DatasetLoader()
    data = loader.load_jsonl("final_gross_motor_dataset.jsonl")
    
    # 检查必要字段
    required_fields = ['instruction', 'output']
    missing_fields = []
    
    for i, item in enumerate(data):
        for field in required_fields:
            if field not in item or not item[field]:
                missing_fields.append(f"条目 {i}: 缺少 {field}")
    
    if missing_fields:
        print("发现数据质量问题:")
        for issue in missing_fields[:5]:  # 只显示前5个
            print(f"  {issue}")
    else:
        print("✅ 数据质量检查通过")
    
    # 检查重复数据
    instructions = [item.get('instruction', '') for item in data]
    unique_instructions = set(instructions)
    
    if len(instructions) != len(unique_instructions):
        duplicates = len(instructions) - len(unique_instructions)
        print(f"⚠️ 发现 {duplicates} 条重复问题")
    else:
        print("✅ 无重复数据")
    print()

def example_7_create_subset():
    """示例7: 创建数据子集"""
    print("=== 示例7: 创建数据子集 ===")
    
    loader = DatasetLoader()
    data = loader.load_jsonl("final_gross_motor_dataset.jsonl")
    
    # 创建早期发育数据子集 (0-6个月)
    early_development = [
        item for item in data 
        if isinstance(item.get('age'), int) and 0 <= item.get('age') <= 6
    ]
    
    print(f"早期发育数据子集: {len(early_development)} 条")
    
    # 保存子集
    with open('early_development_subset.jsonl', 'w', encoding='utf-8') as f:
        for item in early_development:
            f.write(json.dumps(item, ensure_ascii=False) + '\n')
    
    print("已保存为 early_development_subset.jsonl")
    print()

def example_8_format_conversion():
    """示例8: 格式转换"""
    print("=== 示例8: 格式转换 ===")
    
    loader = DatasetLoader()
    
    # 从JSONL转换为Alpaca格式
    jsonl_data = loader.load_jsonl("final_gross_motor_dataset.jsonl")
    
    alpaca_format = []
    for item in jsonl_data:
        alpaca_format.append({
            "instruction": item.get('instruction', ''),
            "input": "",
            "output": item.get('output', '')
        })
    
    # 保存为Alpaca格式
    with open('converted_alpaca.json', 'w', encoding='utf-8') as f:
        json.dump(alpaca_format, f, ensure_ascii=False, indent=2)
    
    print(f"已转换 {len(alpaca_format)} 条数据为Alpaca格式")
    print("已保存为 converted_alpaca.json")
    print()

if __name__ == "__main__":
    print("婴幼儿粗大运动发展指导数据集使用示例\n")
    
    # 运行所有示例
    example_1_basic_loading()
    example_2_age_filtering()
    example_3_source_analysis()
    example_4_convert_to_training_format()
    
    # 需要pandas的示例
    try:
        example_5_pandas_analysis()
    except ImportError:
        print("=== 示例5: 需要安装pandas库 ===")
        print("请运行: pip install pandas")
        print()
    
    example_6_quality_check()
    example_7_create_subset()
    example_8_format_conversion()
    
    print("所有示例运行完成！")
