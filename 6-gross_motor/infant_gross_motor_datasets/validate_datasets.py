"""
数据集验证脚本 - 验证婴幼儿粗大运动发展数据集的完整性和质量
"""

import json
import os
from typing import Dict, List, Any, Tuple

class DatasetValidator:
    """数据集验证器"""
    
    def __init__(self, dataset_dir: str = "./"):
        self.dataset_dir = dataset_dir
        self.validation_results = {}
    
    def validate_jsonl_format(self, filepath: str) -> Dict[str, Any]:
        """验证JSONL格式文件"""
        results = {
            "file_exists": False,
            "valid_json": True,
            "total_lines": 0,
            "valid_lines": 0,
            "errors": [],
            "required_fields": ["instruction", "output"],
            "missing_fields": [],
            "empty_fields": []
        }
        
        if not os.path.exists(filepath):
            results["errors"].append(f"文件不存在: {filepath}")
            return results
        
        results["file_exists"] = True
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    results["total_lines"] += 1
                    
                    if not line.strip():
                        continue
                    
                    try:
                        entry = json.loads(line.strip())
                        results["valid_lines"] += 1
                        
                        # 检查必需字段
                        for field in results["required_fields"]:
                            if field not in entry:
                                results["missing_fields"].append(f"第{line_num}行缺少字段: {field}")
                            elif not entry[field] or entry[field].strip() == "":
                                results["empty_fields"].append(f"第{line_num}行字段为空: {field}")
                    
                    except json.JSONDecodeError as e:
                        results["valid_json"] = False
                        results["errors"].append(f"第{line_num}行JSON解析错误: {e}")
        
        except Exception as e:
            results["errors"].append(f"读取文件错误: {e}")
        
        return results
    
    def validate_alpaca_format(self, filepath: str) -> Dict[str, Any]:
        """验证Alpaca JSON格式文件"""
        results = {
            "file_exists": False,
            "valid_json": True,
            "total_entries": 0,
            "errors": [],
            "required_fields": ["instruction", "input", "output"],
            "missing_fields": [],
            "empty_fields": []
        }
        
        if not os.path.exists(filepath):
            results["errors"].append(f"文件不存在: {filepath}")
            return results
        
        results["file_exists"] = True
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
                if not isinstance(data, list):
                    results["errors"].append("Alpaca格式应该是JSON数组")
                    return results
                
                results["total_entries"] = len(data)
                
                for i, entry in enumerate(data):
                    # 检查必需字段
                    for field in results["required_fields"]:
                        if field not in entry:
                            results["missing_fields"].append(f"第{i+1}条缺少字段: {field}")
                        elif field != "input" and (not entry[field] or entry[field].strip() == ""):
                            results["empty_fields"].append(f"第{i+1}条字段为空: {field}")
        
        except json.JSONDecodeError as e:
            results["valid_json"] = False
            results["errors"].append(f"JSON解析错误: {e}")
        except Exception as e:
            results["errors"].append(f"读取文件错误: {e}")
        
        return results
    
    def validate_chatml_format(self, filepath: str) -> Dict[str, Any]:
        """验证ChatML格式文件"""
        results = {
            "file_exists": False,
            "valid_json": True,
            "total_lines": 0,
            "valid_lines": 0,
            "errors": [],
            "required_structure": ["messages"],
            "invalid_structure": []
        }
        
        if not os.path.exists(filepath):
            results["errors"].append(f"文件不存在: {filepath}")
            return results
        
        results["file_exists"] = True
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    results["total_lines"] += 1
                    
                    if not line.strip():
                        continue
                    
                    try:
                        entry = json.loads(line.strip())
                        results["valid_lines"] += 1
                        
                        # 检查ChatML结构
                        if "messages" not in entry:
                            results["invalid_structure"].append(f"第{line_num}行缺少messages字段")
                        elif not isinstance(entry["messages"], list):
                            results["invalid_structure"].append(f"第{line_num}行messages不是数组")
                        else:
                            # 检查消息格式
                            for msg_i, msg in enumerate(entry["messages"]):
                                if not isinstance(msg, dict):
                                    results["invalid_structure"].append(f"第{line_num}行消息{msg_i+1}不是对象")
                                elif "role" not in msg or "content" not in msg:
                                    results["invalid_structure"].append(f"第{line_num}行消息{msg_i+1}缺少role或content")
                    
                    except json.JSONDecodeError as e:
                        results["valid_json"] = False
                        results["errors"].append(f"第{line_num}行JSON解析错误: {e}")
        
        except Exception as e:
            results["errors"].append(f"读取文件错误: {e}")
        
        return results
    
    def validate_all_datasets(self) -> Dict[str, Any]:
        """验证所有数据集"""
        validation_results = {}
        
        # 获取所有数据集文件
        dataset_files = []
        for filename in os.listdir(self.dataset_dir):
            if filename.endswith(('.jsonl', '.json')):
                dataset_files.append(filename)
        
        print(f"发现 {len(dataset_files)} 个数据集文件")
        
        for filename in dataset_files:
            filepath = os.path.join(self.dataset_dir, filename)
            print(f"验证文件: {filename}")
            
            if filename.endswith('.jsonl'):
                if 'chatml' in filename:
                    validation_results[filename] = self.validate_chatml_format(filepath)
                else:
                    validation_results[filename] = self.validate_jsonl_format(filepath)
            elif filename.endswith('.json'):
                if 'alpaca' in filename:
                    validation_results[filename] = self.validate_alpaca_format(filepath)
                else:
                    # 尝试作为通用JSON验证
                    validation_results[filename] = self.validate_alpaca_format(filepath)
        
        return validation_results
    
    def generate_validation_report(self, results: Dict[str, Any]) -> str:
        """生成验证报告"""
        report = ["=" * 60]
        report.append("数据集验证报告")
        report.append("=" * 60)
        report.append("")
        
        total_files = len(results)
        valid_files = 0
        total_entries = 0
        
        for filename, result in results.items():
            report.append(f"📁 {filename}")
            report.append("-" * 40)
            
            if result.get("file_exists", False):
                if result.get("valid_json", True) and not result.get("errors", []):
                    report.append("✅ 验证通过")
                    valid_files += 1
                else:
                    report.append("❌ 验证失败")
                
                # 统计信息
                if "total_entries" in result:
                    entries = result["total_entries"]
                    total_entries += entries
                    report.append(f"📊 条目数: {entries}")
                elif "valid_lines" in result:
                    entries = result["valid_lines"]
                    total_entries += entries
                    report.append(f"📊 有效行数: {entries}")
                
                # 错误信息
                if result.get("errors"):
                    report.append("🚨 错误:")
                    for error in result["errors"][:3]:  # 只显示前3个错误
                        report.append(f"   • {error}")
                    if len(result["errors"]) > 3:
                        report.append(f"   • ... 还有 {len(result['errors']) - 3} 个错误")
                
                # 警告信息
                warnings = []
                if result.get("missing_fields"):
                    warnings.extend(result["missing_fields"][:2])
                if result.get("empty_fields"):
                    warnings.extend(result["empty_fields"][:2])
                if result.get("invalid_structure"):
                    warnings.extend(result["invalid_structure"][:2])
                
                if warnings:
                    report.append("⚠️ 警告:")
                    for warning in warnings:
                        report.append(f"   • {warning}")
            else:
                report.append("❌ 文件不存在")
            
            report.append("")
        
        # 总结
        report.append("=" * 60)
        report.append("验证总结")
        report.append("=" * 60)
        report.append(f"📁 总文件数: {total_files}")
        report.append(f"✅ 有效文件: {valid_files}")
        report.append(f"❌ 无效文件: {total_files - valid_files}")
        report.append(f"📊 总条目数: {total_entries}")
        
        if valid_files == total_files:
            report.append("")
            report.append("🎉 所有数据集验证通过！")
        else:
            report.append("")
            report.append("⚠️ 部分数据集存在问题，请检查上述错误信息")
        
        return "\n".join(report)

def main():
    """主函数"""
    print("开始验证婴幼儿粗大运动发展数据集...")
    print()
    
    validator = DatasetValidator()
    results = validator.validate_all_datasets()
    
    # 生成报告
    report = validator.generate_validation_report(results)
    print(report)
    
    # 保存报告
    with open("validation_report.txt", 'w', encoding='utf-8') as f:
        f.write(report)
    
    print()
    print("验证完成！详细报告已保存到 validation_report.txt")

if __name__ == "__main__":
    main()
