"""
基于LLM的增强数据生成器
结合文献知识和XLSX表格数据，使用LLM生成高质量的训练数据
"""

import json
import pandas as pd
import random
from typing import List, Dict, Any
from qwen_api_client import QwenAPIClient, QwenConfig
from dataset_schema import DatasetSchema

class LLMEnhancedDataGenerator:
    """基于LLM的增强数据生成器"""
    
    def __init__(self):
        self.schema = DatasetSchema()
        
        # 配置LLM客户端
        config = QwenConfig(api_key="sk-5eba46fbcff649d5bf28313bc865de10")
        self.llm_client = QwenAPIClient(config)
        
        # 加载基础数据
        self.load_base_data()
        self.load_literature_knowledge()
        
    def load_base_data(self):
        """加载XLSX表格数据"""
        try:
            # 加载发育评估量表
            df = pd.read_excel("0 岁～6 岁儿童发育行为评估量表.xlsx")
            
            # 提取里程碑数据
            self.milestones_data = {}
            for _, row in df.iterrows():
                if pd.notna(row.get('月龄')) and pd.notna(row.get('粗大运动')):
                    age = int(row['月龄'])
                    skill = str(row['粗大运动']).strip()
                    if age not in self.milestones_data:
                        self.milestones_data[age] = []
                    self.milestones_data[age].append(skill)
            
            print(f"✅ 从XLSX加载了{len(self.milestones_data)}个年龄段的里程碑数据")
            
        except Exception as e:
            print(f"❌ 加载XLSX数据失败: {e}")
            # 使用备用数据
            self.milestones_data = {
                1: ["抬肩坐起头竖直片刻", "俯卧头部翘动"],
                2: ["拉腕坐起头竖直短时", "俯卧头抬离床面"],
                3: ["抱直头稳", "俯卧抬头45°"],
                6: ["仰卧翻身", "独坐直"],
                12: ["独走自如", "牵一手可走"],
                24: ["双足跳离地面", "独脚站2s"],
                36: ["双脚交替跳", "单脚跳"]
            }
    
    def load_literature_knowledge(self):
        """加载文献提取的知识库"""
        try:
            with open("literature_knowledge_base.json", 'r', encoding='utf-8') as f:
                self.literature_kb = json.load(f)
            print(f"✅ 加载文献知识库成功")
        except Exception as e:
            print(f"❌ 加载文献知识库失败: {e}")
            self.literature_kb = {"reflexes": {}, "milestones": {}}
    
    def generate_milestone_based_qa(self, count: int = 50) -> List[Dict]:
        """基于里程碑数据生成问答对"""
        qa_pairs = []
        
        system_prompt = """你是一位专业的儿童发育专家。基于提供的发育里程碑信息，生成实用的问答对，用于训练婴幼儿发育指导AI。

要求：
1. 问题要贴近家长的实际关切，语言自然
2. 回答要专业准确，包含具体的观察要点和指导建议
3. 每个回答都要包含安全提醒和个体差异说明
4. 生成3种不同类型的问答：评估类、指导类、咨询类"""
        
        for age_months, skills in self.milestones_data.items():
            if not skills:
                continue
                
            skills_text = "、".join(skills[:3])  # 取前3个技能
            
            prompt = f"""基于以下{age_months}个月的发育里程碑，生成3个不同类型的问答对：

里程碑技能：{skills_text}

请生成：
1. 一个评估类问答（家长描述宝宝行为，请求评估）
2. 一个指导类问答（家长询问如何促进某项技能发育）
3. 一个咨询类问答（家长询问该年龄段的发育标准）

每个问答都要包含：
- 自然的问题表述
- 专业的回答内容
- 具体的指导建议
- 安全提醒和个体差异说明"""
            
            try:
                response = self.llm_client.generate_text(prompt, system_prompt)
                if response:
                    parsed_qa = self._parse_llm_qa_response(response, age_months)
                    qa_pairs.extend(parsed_qa)
                    print(f"✅ 为{age_months}个月生成了{len(parsed_qa)}个问答对")
            except Exception as e:
                print(f"❌ 生成{age_months}个月问答对失败: {e}")
        
        return qa_pairs[:count]
    
    def generate_literature_enhanced_qa(self, count: int = 30) -> List[Dict]:
        """基于文献知识生成增强问答对"""
        qa_pairs = []
        
        system_prompt = """你是一位专业的儿童发育专家。基于提供的专业文献知识，生成高质量的问答对。

要求：
1. 问题要体现专业深度，同时保持家长可理解性
2. 回答要准确引用专业知识，提供实用指导
3. 涵盖反射检查、发育评估、异常识别等专业内容
4. 每个回答都要强调安全性和专业咨询的重要性"""
        
        # 基于反射知识生成问答
        reflexes = self.literature_kb.get("reflexes", {})
        for reflex_name, reflex_info in list(reflexes.items())[:10]:  # 取前10个反射
            if len(reflex_name) > 15:  # 跳过过长的反射名称
                continue
                
            time_period = reflex_info.get("time_period", "")
            context = reflex_info.get("context", "")[:200]  # 限制上下文长度
            
            prompt = f"""基于以下反射信息生成2个专业问答对：

反射名称：{reflex_name}
存在时期：{time_period}
专业描述：{context}

请生成：
1. 一个关于反射检查方法的问答
2. 一个关于反射异常意义的问答

要求回答专业准确，同时便于家长理解。"""
            
            try:
                response = self.llm_client.generate_text(prompt, system_prompt)
                if response:
                    parsed_qa = self._parse_llm_qa_response(response)
                    qa_pairs.extend(parsed_qa)
            except Exception as e:
                print(f"❌ 生成{reflex_name}问答对失败: {e}")
        
        return qa_pairs[:count]
    
    def generate_scenario_based_qa(self, count: int = 40) -> List[Dict]:
        """生成基于真实场景的问答对"""
        qa_pairs = []
        
        system_prompt = """你是一位经验丰富的儿科医生和发育专家。基于真实的临床场景，生成实用的问答对。

要求：
1. 问题要模拟真实的家长咨询场景
2. 回答要专业、实用、易懂
3. 包含具体的观察要点、指导方法和注意事项
4. 强调个体差异和专业咨询的重要性"""
        
        # 定义常见场景
        scenarios = [
            {
                "age_range": "0-6个月",
                "concerns": ["头部控制", "翻身发育", "肌张力", "原始反射"],
                "context": "新生儿期到半岁是基础运动能力建立的关键期"
            },
            {
                "age_range": "7-12个月", 
                "concerns": ["坐立发育", "爬行能力", "站立准备", "平衡发展"],
                "context": "这是从被动运动向主动运动转换的重要阶段"
            },
            {
                "age_range": "13-24个月",
                "concerns": ["独立行走", "跑跳发育", "协调性", "安全意识"],
                "context": "幼儿期运动技能快速发展，探索欲强"
            },
            {
                "age_range": "25-36个月",
                "concerns": ["精细协调", "复杂动作", "运动规划", "社交运动"],
                "context": "学龄前期运动技能精细化，为入园做准备"
            }
        ]
        
        for scenario in scenarios:
            age_range = scenario["age_range"]
            concerns = scenario["concerns"]
            context = scenario["context"]
            
            prompt = f"""基于以下发育阶段生成5个真实场景的问答对：

年龄段：{age_range}
主要关注点：{', '.join(concerns)}
发育特点：{context}

请生成5个不同的问答对，涵盖：
- 家长对发育进度的担忧
- 如何促进特定技能发育
- 异常情况的识别和处理
- 日常护理和训练建议
- 何时需要专业咨询

每个问答都要贴近真实场景，语言自然。"""
            
            try:
                response = self.llm_client.generate_text(prompt, system_prompt)
                if response:
                    parsed_qa = self._parse_llm_qa_response(response)
                    qa_pairs.extend(parsed_qa)
                    print(f"✅ 为{age_range}生成了{len(parsed_qa)}个场景问答对")
            except Exception as e:
                print(f"❌ 生成{age_range}场景问答对失败: {e}")
        
        return qa_pairs[:count]
    
    def generate_comparative_qa(self, count: int = 20) -> List[Dict]:
        """生成对比分析类问答对"""
        qa_pairs = []
        
        system_prompt = """你是一位专业的儿童发育专家。生成对比分析类的问答对，帮助家长理解发育的复杂性。

要求：
1. 对比正常与异常发育表现
2. 解释个体差异的合理范围
3. 提供判断标准和观察要点
4. 强调专业评估的重要性"""
        
        comparison_topics = [
            "正常发育延迟 vs 病理性发育迟缓",
            "早产儿 vs 足月儿的发育差异",
            "男孩 vs 女孩的运动发育特点",
            "不同环境因素对发育的影响",
            "遗传因素 vs 环境因素的作用"
        ]
        
        for topic in comparison_topics:
            prompt = f"""围绕"{topic}"这个主题，生成4个对比分析的问答对。

要求：
1. 问题要体现家长的实际困惑
2. 回答要客观分析差异和原因
3. 提供具体的判断标准
4. 给出实用的指导建议

请确保内容专业准确，同时便于家长理解。"""
            
            try:
                response = self.llm_client.generate_text(prompt, system_prompt)
                if response:
                    parsed_qa = self._parse_llm_qa_response(response)
                    qa_pairs.extend(parsed_qa)
            except Exception as e:
                print(f"❌ 生成{topic}对比问答对失败: {e}")
        
        return qa_pairs[:count]
    
    def _parse_llm_qa_response(self, response: str, age_months: int = None) -> List[Dict]:
        """解析LLM生成的问答对"""
        qa_pairs = []
        
        # 多种解析模式
        patterns = [
            r'问题?\s*[:：]\s*(.*?)\s*回?答案?\s*[:：]\s*(.*?)(?=问题?[:：]|$)',
            r'Q\s*[:：]\s*(.*?)\s*A\s*[:：]\s*(.*?)(?=Q[:：]|$)',
            r'(\d+)\.\s*问题?\s*[:：]?\s*(.*?)\s*回?答案?\s*[:：]?\s*(.*?)(?=\d+\.|$)',
            r'【问题】\s*(.*?)\s*【回答】\s*(.*?)(?=【问题】|$)',
            r'问：\s*(.*?)\s*答：\s*(.*?)(?=问：|$)'
        ]
        
        import re
        
        for pattern in patterns:
            matches = re.findall(pattern, response, re.DOTALL | re.IGNORECASE)
            if matches:
                for match in matches:
                    if len(match) == 2:
                        question, answer = match
                    elif len(match) == 3:
                        _, question, answer = match
                    else:
                        continue
                    
                    question = question.strip()
                    answer = answer.strip()
                    
                    # 过滤太短或太长的内容
                    if 10 <= len(question) <= 200 and 20 <= len(answer) <= 1000:
                        qa_pair = {
                            'question': question,
                            'answer': answer,
                            'source': 'llm_generated'
                        }
                        
                        if age_months:
                            qa_pair['age_months'] = age_months
                            
                        qa_pairs.append(qa_pair)
                break
        
        # 如果没有匹配到标准格式，尝试按段落分割
        if not qa_pairs and len(response) > 50:
            paragraphs = [p.strip() for p in response.split('\n\n') if p.strip()]
            if len(paragraphs) >= 2:
                for i in range(0, len(paragraphs)-1, 2):
                    question = paragraphs[i]
                    answer = paragraphs[i+1] if i+1 < len(paragraphs) else ""
                    
                    if 10 <= len(question) <= 200 and 20 <= len(answer) <= 1000:
                        qa_pairs.append({
                            'question': question,
                            'answer': answer,
                            'source': 'llm_generated'
                        })
        
        return qa_pairs
    
    def generate_complete_llm_dataset(self, 
                                    milestone_count: int = 50,
                                    literature_count: int = 30,
                                    scenario_count: int = 40,
                                    comparative_count: int = 20) -> None:
        """生成完整的LLM增强数据集"""
        print("🚀 开始生成LLM增强数据集...")
        
        all_qa_pairs = []
        
        # 1. 基于里程碑生成
        print("📊 基于里程碑数据生成问答对...")
        milestone_qa = self.generate_milestone_based_qa(milestone_count)
        all_qa_pairs.extend(milestone_qa)
        print(f"✅ 生成里程碑问答对: {len(milestone_qa)} 个")
        
        # 2. 基于文献知识生成
        print("📚 基于文献知识生成问答对...")
        literature_qa = self.generate_literature_enhanced_qa(literature_count)
        all_qa_pairs.extend(literature_qa)
        print(f"✅ 生成文献问答对: {len(literature_qa)} 个")
        
        # 3. 基于场景生成
        print("🎭 基于真实场景生成问答对...")
        scenario_qa = self.generate_scenario_based_qa(scenario_count)
        all_qa_pairs.extend(scenario_qa)
        print(f"✅ 生成场景问答对: {len(scenario_qa)} 个")
        
        # 4. 基于对比分析生成
        print("🔍 生成对比分析问答对...")
        comparative_qa = self.generate_comparative_qa(comparative_count)
        all_qa_pairs.extend(comparative_qa)
        print(f"✅ 生成对比问答对: {len(comparative_qa)} 个")
        
        # 5. 转换为数据集格式
        print("🔄 转换数据格式...")
        dataset_entries = []
        for qa in all_qa_pairs:
            entry = self.schema.create_generic_entry(
                input_text=qa['question'],
                output_text=qa['answer'],
                interaction_type="guidance"
            )
            dataset_entries.append(entry)
        
        # 6. 保存数据集
        self._save_llm_dataset(all_qa_pairs)
        
        print(f"🎉 LLM增强数据集生成完成！")
        print(f"📊 总计: {len(all_qa_pairs)} 条高质量数据")
        
        return all_qa_pairs
    
    def _save_llm_dataset(self, qa_pairs: List[Dict]) -> None:
        """保存LLM生成的数据集"""
        # JSONL格式
        with open("llm_enhanced_dataset.jsonl", 'w', encoding='utf-8') as f:
            for qa in qa_pairs:
                jsonl_entry = {
                    "instruction": qa['question'],
                    "output": qa['answer'],
                    "source": qa.get('source', 'llm_generated'),
                    "age_months": qa.get('age_months', ''),
                    "category": "llm_enhanced"
                }
                f.write(json.dumps(jsonl_entry, ensure_ascii=False) + '\n')
        
        # Alpaca格式
        alpaca_data = []
        for qa in qa_pairs:
            alpaca_data.append({
                "instruction": qa['question'],
                "input": "",
                "output": qa['answer']
            })
        
        with open("llm_enhanced_alpaca.json", 'w', encoding='utf-8') as f:
            json.dump(alpaca_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 LLM增强数据集已保存:")
        print(f"  - JSONL格式: llm_enhanced_dataset.jsonl")
        print(f"  - Alpaca格式: llm_enhanced_alpaca.json")

if __name__ == "__main__":
    generator = LLMEnhancedDataGenerator()
    generator.generate_complete_llm_dataset(
        milestone_count=60,
        literature_count=40,
        scenario_count=50,
        comparative_count=30
    )
