"""
Qwen API客户端
用于调用通义千问API生成婴幼儿粗大运动发展指导数据
"""

import requests
import json
import time
import random
from typing import List, Dict, Optional
from dataclasses import dataclass
import os
from dataset_schema import DatasetSchema, MotorSkillCategory, AgeGroup

@dataclass
class QwenConfig:
    """Qwen API配置"""
    api_key: str = "sk-5eba46fbcff649d5bf28313bc865de10"
    base_url: str = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
    model: str = "qwen-plus-2025-01-25"
    max_tokens: int = 2000
    temperature: float = 0.7
    top_p: float = 0.8

class QwenAPIClient:
    """Qwen API客户端"""
    
    def __init__(self, config: QwenConfig):
        self.config = config
        self.headers = {
            "Authorization": f"Bearer {config.api_key}",
            "Content-Type": "application/json"
        }
    
    def generate_text(self, prompt: str, system_prompt: str = None) -> str:
        """生成文本"""
        messages = []
        
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        
        messages.append({"role": "user", "content": prompt})
        
        payload = {
            "model": self.config.model,
            "input": {
                "messages": messages
            },
            "parameters": {
                "max_tokens": self.config.max_tokens,
                "temperature": self.config.temperature,
                "top_p": self.config.top_p
            }
        }
        
        try:
            response = requests.post(
                self.config.base_url,
                headers=self.headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                return result["output"]["text"]
            else:
                print(f"API调用失败: {response.status_code}, {response.text}")
                return ""
                
        except Exception as e:
            print(f"API调用异常: {e}")
            return ""
    
    def batch_generate(self, prompts: List[str], system_prompt: str = None, 
                      delay: float = 1.0) -> List[str]:
        """批量生成文本"""
        results = []
        for i, prompt in enumerate(prompts):
            print(f"正在生成第 {i+1}/{len(prompts)} 条数据...")
            result = self.generate_text(prompt, system_prompt)
            results.append(result)
            
            # 添加延迟避免API限流
            if i < len(prompts) - 1:
                time.sleep(delay)
        
        return results

class DatasetGenerator:
    """数据集生成器"""
    
    def __init__(self, qwen_client: QwenAPIClient):
        self.qwen_client = qwen_client
        self.schema = DatasetSchema()
        
        # 系统提示词
        self.system_prompt = """你是一位专业的儿童发育专家和康复治疗师，专门从事0-3岁婴幼儿粗大运动发育评估和指导。
你的回答应该：
1. 基于循证医学和发育里程碑
2. 语言专业但易懂，适合家长理解
3. 提供具体可操作的建议
4. 注重安全性和个体差异
5. 鼓励家长观察和记录
6. 必要时建议寻求专业帮助"""
    
    def generate_assessment_scenarios(self, count: int = 50) -> List[str]:
        """生成评估场景提示词"""
        age_ranges = [
            (1, 3, "新生儿期"),
            (4, 6, "婴儿早期"),
            (7, 9, "婴儿中期"),
            (10, 12, "婴儿晚期"),
            (13, 18, "幼儿早期"),
            (19, 24, "幼儿中期"),
            (25, 36, "幼儿晚期")
        ]
        
        scenarios = []
        for _ in range(count):
            min_age, max_age, period = random.choice(age_ranges)
            age = random.randint(min_age, max_age)
            
            # 随机选择发育状况
            dev_status = random.choice([
                "发育正常",
                "稍有延迟",
                "明显延迟",
                "超前发育",
                "不确定"
            ])
            
            prompt = f"""请生成一个{age}个月婴幼儿粗大运动发育评估的对话场景。
场景设定：
- 婴儿年龄：{age}个月（{period}）
- 发育状况：{dev_status}
- 家长关注的问题和观察到的行为
- 需要专业的评估和建议

请以家长咨询的形式提出问题，然后给出专业的评估回复。
格式：
问题：[家长的具体咨询]
回答：[专业的评估和建议]"""
            
            scenarios.append(prompt)
        
        return scenarios
    
    def generate_guidance_scenarios(self, count: int = 50) -> List[str]:
        """生成指导场景提示词"""
        skills = [
            ("头部控制", "抬头、转头、头部稳定"),
            ("翻身", "从仰卧到俯卧、从俯卧到仰卧"),
            ("坐位", "独立坐、坐位平衡"),
            ("爬行", "腹爬、四肢爬行"),
            ("站立", "扶站、独站"),
            ("行走", "扶走、独走、稳定行走"),
            ("跳跃", "原地跳、向前跳"),
            ("平衡", "单脚站立、走直线"),
            ("协调性", "上下楼梯、踢球、接球")
        ]
        
        scenarios = []
        for _ in range(count):
            skill_name, skill_desc = random.choice(skills)
            age = random.randint(1, 36)
            
            concern_types = [
                f"宝宝{age}个月了，{skill_desc}还不会",
                f"{age}个月的宝宝{skill_desc}不稳定",
                f"如何帮助{age}个月宝宝练习{skill_desc}",
                f"宝宝{age}个月，{skill_desc}比同龄孩子慢"
            ]
            
            concern = random.choice(concern_types)
            
            prompt = f"""请生成一个关于{skill_name}发育指导的对话场景。
场景设定：
- 目标技能：{skill_name}（{skill_desc}）
- 家长关注：{concern}
- 需要具体的练习方法和注意事项

请以家长咨询的形式提出问题，然后给出详细的指导建议。
格式：
问题：[家长的具体咨询]
回答：[详细的指导方法和建议]"""
            
            scenarios.append(prompt)
        
        return scenarios
    
    def generate_milestone_scenarios(self, count: int = 30) -> List[str]:
        """生成里程碑检查场景提示词"""
        scenarios = []
        
        for milestone in self.schema.milestones[:count]:
            age = milestone.age_months
            skill = milestone.skill_name
            
            prompt = f"""请生成一个关于{age}个月里程碑"{skill}"的检查场景。
场景设定：
- 里程碑：{skill}
- 预期年龄：{age}个月
- 检查方法和判断标准
- 如果未达到的应对建议

请以家长询问的形式提出问题，然后给出专业的里程碑检查指导。
格式：
问题：[家长关于里程碑的询问]
回答：[里程碑检查方法和建议]"""
            
            scenarios.append(prompt)
        
        return scenarios
    
    def generate_dataset(self, assessment_count: int = 50, guidance_count: int = 50, 
                        milestone_count: int = 30) -> None:
        """生成完整数据集"""
        print("开始生成婴幼儿粗大运动发展指导数据集...")
        
        # 生成评估场景
        print("\n1. 生成评估场景...")
        assessment_prompts = self.generate_assessment_scenarios(assessment_count)
        assessment_results = self.qwen_client.batch_generate(
            assessment_prompts, self.system_prompt
        )
        
        # 生成指导场景
        print("\n2. 生成指导场景...")
        guidance_prompts = self.generate_guidance_scenarios(guidance_count)
        guidance_results = self.qwen_client.batch_generate(
            guidance_prompts, self.system_prompt
        )
        
        # 生成里程碑场景
        print("\n3. 生成里程碑场景...")
        milestone_prompts = self.generate_milestone_scenarios(milestone_count)
        milestone_results = self.qwen_client.batch_generate(
            milestone_prompts, self.system_prompt
        )
        
        # 处理和保存结果
        print("\n4. 处理和保存数据...")
        self._process_and_save_results(
            assessment_results, guidance_results, milestone_results
        )
        
        print(f"\n数据集生成完成！共生成 {len(self.schema.entries)} 条数据")
    
    def _process_and_save_results(self, assessment_results: List[str], 
                                 guidance_results: List[str], 
                                 milestone_results: List[str]) -> None:
        """处理和保存生成结果"""
        all_results = [
            ("assessment", assessment_results),
            ("guidance", guidance_results),
            ("milestone", milestone_results)
        ]
        
        for result_type, results in all_results:
            for result in results:
                if result and "问题：" in result and "回答：" in result:
                    try:
                        parts = result.split("回答：", 1)
                        if len(parts) == 2:
                            question = parts[0].replace("问题：", "").strip()
                            answer = parts[1].strip()
                            
                            # 创建数据条目
                            entry = self.schema.create_generic_entry(
                                input_text=question,
                                output_text=answer,
                                interaction_type=result_type
                            )
                            self.schema.entries.append(entry)
                    except Exception as e:
                        print(f"处理结果时出错: {e}")
        
        # 导出数据集
        self.schema.export_to_jsonl("gross_motor_dataset.jsonl")
        self.schema.export_to_alpaca_format("gross_motor_alpaca.json")

# 使用示例
if __name__ == "__main__":
    # 配置API（需要替换为实际的API Key）
    config = QwenConfig(
        api_key=os.getenv("QWEN_API_KEY", "your-api-key-here")
    )
    
    # 创建客户端和生成器
    client = QwenAPIClient(config)
    generator = DatasetGenerator(client)
    
    # 生成数据集
    generator.generate_dataset(
        assessment_count=20,  # 减少数量用于测试
        guidance_count=20,
        milestone_count=10
    )
