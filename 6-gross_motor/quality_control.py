"""
数据集质量控制和验证模块
确保生成的数据集质量符合LLM微调要求
"""

import json
import re
from typing import List, Dict, Tuple, Set
from collections import Counter
import pandas as pd

class DatasetQualityController:
    """数据集质量控制器"""
    
    def __init__(self):
        self.quality_metrics = {}
        self.issues = []
        
    def load_dataset(self, file_path: str) -> List[Dict]:
        """加载数据集"""
        if file_path.endswith('.jsonl'):
            data = []
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    data.append(json.loads(line))
            return data
        elif file_path.endswith('.json'):
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        else:
            raise ValueError("不支持的文件格式")
    
    def check_data_completeness(self, data: List[Dict]) -> Dict:
        """检查数据完整性"""
        print("检查数据完整性...")
        
        completeness_metrics = {
            "total_entries": len(data),
            "missing_instruction": 0,
            "missing_output": 0,
            "empty_instruction": 0,
            "empty_output": 0
        }
        
        for i, entry in enumerate(data):
            # 检查必需字段
            if "instruction" not in entry:
                completeness_metrics["missing_instruction"] += 1
                self.issues.append(f"条目 {i}: 缺少 instruction 字段")
            elif not entry["instruction"].strip():
                completeness_metrics["empty_instruction"] += 1
                self.issues.append(f"条目 {i}: instruction 字段为空")
            
            if "output" not in entry:
                completeness_metrics["missing_output"] += 1
                self.issues.append(f"条目 {i}: 缺少 output 字段")
            elif not entry["output"].strip():
                completeness_metrics["empty_output"] += 1
                self.issues.append(f"条目 {i}: output 字段为空")
        
        return completeness_metrics
    
    def check_text_quality(self, data: List[Dict]) -> Dict:
        """检查文本质量"""
        print("检查文本质量...")
        
        quality_metrics = {
            "avg_instruction_length": 0,
            "avg_output_length": 0,
            "too_short_instructions": 0,
            "too_long_instructions": 0,
            "too_short_outputs": 0,
            "too_long_outputs": 0,
            "encoding_issues": 0
        }
        
        instruction_lengths = []
        output_lengths = []
        
        for i, entry in enumerate(data):
            if "instruction" in entry and entry["instruction"]:
                inst_len = len(entry["instruction"])
                instruction_lengths.append(inst_len)
                
                # 检查长度
                if inst_len < 10:
                    quality_metrics["too_short_instructions"] += 1
                    self.issues.append(f"条目 {i}: instruction 过短 ({inst_len} 字符)")
                elif inst_len > 500:
                    quality_metrics["too_long_instructions"] += 1
                    self.issues.append(f"条目 {i}: instruction 过长 ({inst_len} 字符)")
                
                # 检查编码问题
                try:
                    entry["instruction"].encode('utf-8')
                except UnicodeEncodeError:
                    quality_metrics["encoding_issues"] += 1
                    self.issues.append(f"条目 {i}: instruction 编码问题")
            
            if "output" in entry and entry["output"]:
                out_len = len(entry["output"])
                output_lengths.append(out_len)
                
                # 检查长度
                if out_len < 20:
                    quality_metrics["too_short_outputs"] += 1
                    self.issues.append(f"条目 {i}: output 过短 ({out_len} 字符)")
                elif out_len > 2000:
                    quality_metrics["too_long_outputs"] += 1
                    self.issues.append(f"条目 {i}: output 过长 ({out_len} 字符)")
                
                # 检查编码问题
                try:
                    entry["output"].encode('utf-8')
                except UnicodeEncodeError:
                    quality_metrics["encoding_issues"] += 1
                    self.issues.append(f"条目 {i}: output 编码问题")
        
        if instruction_lengths:
            quality_metrics["avg_instruction_length"] = sum(instruction_lengths) / len(instruction_lengths)
        if output_lengths:
            quality_metrics["avg_output_length"] = sum(output_lengths) / len(output_lengths)
        
        return quality_metrics
    
    def check_content_relevance(self, data: List[Dict]) -> Dict:
        """检查内容相关性"""
        print("检查内容相关性...")
        
        # 关键词列表
        motor_keywords = [
            "粗大运动", "大运动", "抬头", "翻身", "坐", "爬", "站", "走", "跑", "跳",
            "平衡", "协调", "发育", "里程碑", "月龄", "婴儿", "幼儿", "宝宝"
        ]
        
        age_keywords = [
            "个月", "月龄", "岁", "新生儿", "婴儿", "幼儿", "0-3岁"
        ]
        
        relevance_metrics = {
            "motor_keyword_coverage": 0,
            "age_keyword_coverage": 0,
            "irrelevant_entries": 0
        }
        
        motor_matches = 0
        age_matches = 0
        
        for i, entry in enumerate(data):
            text = (entry.get("instruction", "") + " " + entry.get("output", "")).lower()
            
            # 检查粗大运动相关关键词
            has_motor_keyword = any(keyword in text for keyword in motor_keywords)
            if has_motor_keyword:
                motor_matches += 1
            
            # 检查年龄相关关键词
            has_age_keyword = any(keyword in text for keyword in age_keywords)
            if has_age_keyword:
                age_matches += 1
            
            # 检查是否相关
            if not has_motor_keyword and not has_age_keyword:
                relevance_metrics["irrelevant_entries"] += 1
                self.issues.append(f"条目 {i}: 内容可能不相关")
        
        total_entries = len(data)
        if total_entries > 0:
            relevance_metrics["motor_keyword_coverage"] = motor_matches / total_entries
            relevance_metrics["age_keyword_coverage"] = age_matches / total_entries
        
        return relevance_metrics
    
    def check_diversity(self, data: List[Dict]) -> Dict:
        """检查数据多样性"""
        print("检查数据多样性...")
        
        instructions = [entry.get("instruction", "") for entry in data]
        outputs = [entry.get("output", "") for entry in data]
        
        # 检查重复
        instruction_counts = Counter(instructions)
        output_counts = Counter(outputs)
        
        diversity_metrics = {
            "unique_instructions": len(set(instructions)),
            "unique_outputs": len(set(outputs)),
            "duplicate_instructions": sum(1 for count in instruction_counts.values() if count > 1),
            "duplicate_outputs": sum(1 for count in output_counts.values() if count > 1),
            "instruction_diversity_ratio": 0,
            "output_diversity_ratio": 0
        }
        
        total_entries = len(data)
        if total_entries > 0:
            diversity_metrics["instruction_diversity_ratio"] = diversity_metrics["unique_instructions"] / total_entries
            diversity_metrics["output_diversity_ratio"] = diversity_metrics["unique_outputs"] / total_entries
        
        # 记录重复项
        for instruction, count in instruction_counts.items():
            if count > 1:
                self.issues.append(f"重复的 instruction (出现{count}次): {instruction[:50]}...")
        
        return diversity_metrics
    
    def check_format_consistency(self, data: List[Dict]) -> Dict:
        """检查格式一致性"""
        print("检查格式一致性...")
        
        format_metrics = {
            "consistent_fields": True,
            "field_variations": set(),
            "format_issues": 0
        }
        
        # 检查字段一致性
        expected_fields = {"instruction", "output"}
        
        for i, entry in enumerate(data):
            entry_fields = set(entry.keys())
            
            # 记录字段变化
            format_metrics["field_variations"].add(frozenset(entry_fields))
            
            # 检查必需字段
            missing_fields = expected_fields - entry_fields
            if missing_fields:
                format_metrics["format_issues"] += 1
                self.issues.append(f"条目 {i}: 缺少字段 {missing_fields}")
        
        # 检查是否所有条目字段一致
        if len(format_metrics["field_variations"]) > 1:
            format_metrics["consistent_fields"] = False
            self.issues.append("数据集字段不一致")
        
        format_metrics["field_variations"] = list(format_metrics["field_variations"])
        
        return format_metrics
    
    def generate_quality_report(self, file_path: str) -> Dict:
        """生成质量报告"""
        print(f"开始质量检查: {file_path}")
        print("=" * 50)
        
        # 加载数据
        data = self.load_dataset(file_path)
        
        # 执行各项检查
        completeness = self.check_data_completeness(data)
        text_quality = self.check_text_quality(data)
        relevance = self.check_content_relevance(data)
        diversity = self.check_diversity(data)
        format_consistency = self.check_format_consistency(data)
        
        # 汇总报告
        quality_report = {
            "file_path": file_path,
            "completeness": completeness,
            "text_quality": text_quality,
            "relevance": relevance,
            "diversity": diversity,
            "format_consistency": format_consistency,
            "issues": self.issues,
            "overall_score": self._calculate_overall_score(
                completeness, text_quality, relevance, diversity, format_consistency
            )
        }
        
        return quality_report
    
    def _calculate_overall_score(self, completeness: Dict, text_quality: Dict, 
                               relevance: Dict, diversity: Dict, format_consistency: Dict) -> float:
        """计算总体质量分数"""
        score = 100.0
        
        # 完整性扣分
        total_entries = completeness["total_entries"]
        if total_entries > 0:
            missing_ratio = (completeness["missing_instruction"] + completeness["missing_output"]) / (total_entries * 2)
            score -= missing_ratio * 30
            
            empty_ratio = (completeness["empty_instruction"] + completeness["empty_output"]) / (total_entries * 2)
            score -= empty_ratio * 20
        
        # 文本质量扣分
        if total_entries > 0:
            short_ratio = (text_quality["too_short_instructions"] + text_quality["too_short_outputs"]) / (total_entries * 2)
            score -= short_ratio * 15
            
            long_ratio = (text_quality["too_long_instructions"] + text_quality["too_long_outputs"]) / (total_entries * 2)
            score -= long_ratio * 10
        
        # 相关性扣分
        score += relevance["motor_keyword_coverage"] * 10
        score += relevance["age_keyword_coverage"] * 5
        
        # 多样性加分
        score += diversity["instruction_diversity_ratio"] * 10
        score += diversity["output_diversity_ratio"] * 10
        
        # 格式一致性
        if not format_consistency["consistent_fields"]:
            score -= 10
        
        return max(0, min(100, score))
    
    def print_quality_report(self, report: Dict):
        """打印质量报告"""
        print("\n" + "=" * 60)
        print("数据集质量报告")
        print("=" * 60)
        
        print(f"\n文件: {report['file_path']}")
        print(f"总体评分: {report['overall_score']:.1f}/100")
        
        print(f"\n📊 数据完整性:")
        comp = report['completeness']
        print(f"  总条目数: {comp['total_entries']}")
        print(f"  缺少instruction: {comp['missing_instruction']}")
        print(f"  缺少output: {comp['missing_output']}")
        print(f"  空instruction: {comp['empty_instruction']}")
        print(f"  空output: {comp['empty_output']}")
        
        print(f"\n📝 文本质量:")
        tq = report['text_quality']
        print(f"  平均instruction长度: {tq['avg_instruction_length']:.1f} 字符")
        print(f"  平均output长度: {tq['avg_output_length']:.1f} 字符")
        print(f"  过短instruction: {tq['too_short_instructions']}")
        print(f"  过长instruction: {tq['too_long_instructions']}")
        print(f"  过短output: {tq['too_short_outputs']}")
        print(f"  过长output: {tq['too_long_outputs']}")
        
        print(f"\n🎯 内容相关性:")
        rel = report['relevance']
        print(f"  粗大运动关键词覆盖率: {rel['motor_keyword_coverage']:.1%}")
        print(f"  年龄关键词覆盖率: {rel['age_keyword_coverage']:.1%}")
        print(f"  不相关条目: {rel['irrelevant_entries']}")
        
        print(f"\n🌈 数据多样性:")
        div = report['diversity']
        print(f"  唯一instruction: {div['unique_instructions']}")
        print(f"  唯一output: {div['unique_outputs']}")
        print(f"  instruction多样性比率: {div['instruction_diversity_ratio']:.1%}")
        print(f"  output多样性比率: {div['output_diversity_ratio']:.1%}")
        
        if report['issues']:
            print(f"\n⚠️  发现的问题 (前10个):")
            for issue in report['issues'][:10]:
                print(f"  • {issue}")
            if len(report['issues']) > 10:
                print(f"  ... 还有 {len(report['issues']) - 10} 个问题")

if __name__ == "__main__":
    qc = DatasetQualityController()
    
    # 检查生成的数据集
    files_to_check = [
        "gross_motor_dataset_generated.jsonl",
        "gross_motor_alpaca_generated.json"
    ]
    
    for file_path in files_to_check:
        try:
            report = qc.generate_quality_report(file_path)
            qc.print_quality_report(report)
            
            # 保存报告
            report_file = file_path.replace('.json', '_quality_report.json').replace('.jsonl', '_quality_report.json')
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"\n质量报告已保存到: {report_file}")
            
        except FileNotFoundError:
            print(f"文件不存在: {file_path}")
        except Exception as e:
            print(f"检查文件 {file_path} 时出错: {e}")
        
        print("\n" + "=" * 60 + "\n")
