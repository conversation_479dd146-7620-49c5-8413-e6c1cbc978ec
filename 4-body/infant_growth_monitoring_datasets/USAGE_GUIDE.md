# 数据集使用指南 (Usage Guide)

## 快速开始 (Quick Start)

### 1. 环境准备

```bash
# 安装必要的Python包
pip install pandas numpy json jsonlines transformers datasets
```

### 2. 数据加载示例

```python
import json
import pandas as pd

# 方法1: 加载主数据集 (推荐)
with open('growth_monitoring_400_samples.json', 'r', encoding='utf-8') as f:
    main_dataset = json.load(f)

print(f"主数据集包含 {len(main_dataset)} 个样本")

# 方法2: 加载训练分割数据
with open('train.jsonl', 'r', encoding='utf-8') as f:
    train_data = [json.loads(line) for line in f]

with open('validation.jsonl', 'r', encoding='utf-8') as f:
    val_data = [json.loads(line) for line in f]

with open('test.jsonl', 'r', encoding='utf-8') as f:
    test_data = [json.loads(line) for line in f]

print(f"训练集: {len(train_data)}, 验证集: {len(val_data)}, 测试集: {len(test_data)}")
```

### 3. 数据探索

```python
# 查看样本结构
sample = main_dataset[0]
print("样本结构:")
for key, value in sample.items():
    print(f"{key}: {value[:100]}..." if len(str(value)) > 100 else f"{key}: {value}")

# 统计头围相关样本
head_samples = [item for item in main_dataset if '头围' in item['instruction']]
print(f"\n包含头围的样本数: {len(head_samples)} ({len(head_samples)/len(main_dataset)*100:.1f}%)")

# 分析指令长度分布
instruction_lengths = [len(item['instruction']) for item in main_dataset]
print(f"\n指令长度统计:")
print(f"平均长度: {sum(instruction_lengths)/len(instruction_lengths):.1f}")
print(f"最短: {min(instruction_lengths)}, 最长: {max(instruction_lengths)}")
```

## 微调框架适配 (Framework Adaptation)

### 1. Transformers (Hugging Face)

```python
from datasets import Dataset
from transformers import AutoTokenizer, AutoModelForCausalLM, TrainingArguments, Trainer

# 加载数据
with open('growth_monitoring_400_samples.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

# 转换为Hugging Face Dataset格式
dataset = Dataset.from_list(data)

# 数据预处理
tokenizer = AutoTokenizer.from_pretrained("your-model-name")

def preprocess_function(examples):
    # 组合instruction和output
    texts = []
    for i in range(len(examples['instruction'])):
        text = f"指令: {examples['instruction'][i]}\n回答: {examples['output'][i]}"
        texts.append(text)
    
    return tokenizer(texts, truncation=True, padding=True, max_length=512)

tokenized_dataset = dataset.map(preprocess_function, batched=True)

# 分割数据集
train_dataset = tokenized_dataset.train_test_split(test_size=0.2)
```

### 2. ChatGLM格式

```python
# 使用ChatGLM格式数据
import jsonlines

with jsonlines.open('chatglm_train.jsonl') as reader:
    chatglm_data = list(reader)

# ChatGLM格式示例
for item in chatglm_data[:2]:
    print(json.dumps(item, ensure_ascii=False, indent=2))
```

### 3. Alpaca格式

```python
# 使用Alpaca格式数据
with open('alpaca_train.json', 'r', encoding='utf-8') as f:
    alpaca_data = json.load(f)

# Alpaca格式适合Stanford Alpaca微调
print(f"Alpaca格式训练数据: {len(alpaca_data)} 条")
```

## 数据质量分析 (Data Quality Analysis)

### 1. 基本统计

```python
import matplotlib.pyplot as plt
import seaborn as sns

# 分析指令类型分布
def analyze_instruction_types(dataset):
    types = {
        '头围专项': 0,
        '综合评估': 0,
        '体重评估': 0,
        '身高评估': 0,
        'BMI评估': 0,
        '多指标+头围': 0,
        '基础知识': 0,
        '其他': 0
    }
    
    for item in dataset:
        instruction = item['instruction']
        if '头围' in instruction:
            if '体重' in instruction and '身高' in instruction:
                types['多指标+头围'] += 1
            else:
                types['头围专项'] += 1
        elif '体重' in instruction and '身高' in instruction:
            types['综合评估'] += 1
        elif '体重' in instruction:
            types['体重评估'] += 1
        elif '身高' in instruction or '身长' in instruction:
            types['身高评估'] += 1
        elif 'BMI' in instruction:
            types['BMI评估'] += 1
        elif '如何' in instruction or '什么是' in instruction:
            types['基础知识'] += 1
        else:
            types['其他'] += 1
    
    return types

# 执行分析
types_count = analyze_instruction_types(main_dataset)
for type_name, count in types_count.items():
    percentage = count / len(main_dataset) * 100
    print(f"{type_name}: {count} 条 ({percentage:.1f}%)")
```

### 2. 文本质量检查

```python
def quality_check(dataset):
    issues = []
    
    for i, item in enumerate(dataset):
        # 检查必要字段
        if not item.get('instruction') or not item.get('output'):
            issues.append(f"样本 {i}: 缺少必要字段")
        
        # 检查文本长度
        if len(item.get('instruction', '')) < 10:
            issues.append(f"样本 {i}: 指令过短")
        
        if len(item.get('output', '')) < 50:
            issues.append(f"样本 {i}: 输出过短")
        
        # 检查医学术语
        medical_terms = ['评估', '标准', '百分位', '发育', '生长']
        if not any(term in item.get('output', '') for term in medical_terms):
            issues.append(f"样本 {i}: 可能缺少医学专业术语")
    
    return issues

# 执行质量检查
issues = quality_check(main_dataset)
print(f"发现 {len(issues)} 个潜在问题")
for issue in issues[:5]:  # 显示前5个问题
    print(f"- {issue}")
```

## 模型微调示例 (Fine-tuning Examples)

### 1. 基础微调脚本

```python
from transformers import (
    AutoTokenizer, 
    AutoModelForCausalLM, 
    TrainingArguments, 
    Trainer,
    DataCollatorForLanguageModeling
)

# 模型和分词器
model_name = "THUDM/chatglm3-6b"  # 或其他中文模型
tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
model = AutoModelForCausalLM.from_pretrained(model_name, trust_remote_code=True)

# 训练参数
training_args = TrainingArguments(
    output_dir="./infant_growth_model",
    overwrite_output_dir=True,
    num_train_epochs=3,
    per_device_train_batch_size=4,
    per_device_eval_batch_size=4,
    warmup_steps=100,
    logging_steps=10,
    save_steps=500,
    evaluation_strategy="steps",
    eval_steps=500,
    save_total_limit=2,
    prediction_loss_only=True,
    remove_unused_columns=False,
)

# 数据整理器
data_collator = DataCollatorForLanguageModeling(
    tokenizer=tokenizer,
    mlm=False,
)

# 训练器
trainer = Trainer(
    model=model,
    args=training_args,
    data_collator=data_collator,
    train_dataset=train_dataset,
    eval_dataset=val_dataset,
)

# 开始训练
trainer.train()
```

### 2. LoRA微调示例

```python
from peft import LoraConfig, get_peft_model, TaskType

# LoRA配置
lora_config = LoraConfig(
    task_type=TaskType.CAUSAL_LM,
    inference_mode=False,
    r=8,
    lora_alpha=32,
    lora_dropout=0.1,
    target_modules=["query_key_value"]  # 根据模型调整
)

# 应用LoRA
model = get_peft_model(model, lora_config)
model.print_trainable_parameters()

# 其余训练代码相同...
```

## 评估指标 (Evaluation Metrics)

### 1. 自动评估

```python
from rouge import Rouge
from bleu import sentence_bleu

def evaluate_model(model, tokenizer, test_data):
    rouge = Rouge()
    bleu_scores = []
    rouge_scores = []
    
    for item in test_data:
        # 生成回答
        input_text = f"指令: {item['instruction']}\n回答:"
        inputs = tokenizer(input_text, return_tensors="pt")
        
        with torch.no_grad():
            outputs = model.generate(**inputs, max_length=512, do_sample=True)
        
        generated = tokenizer.decode(outputs[0], skip_special_tokens=True)
        generated = generated.replace(input_text, "").strip()
        
        # 计算ROUGE
        rouge_score = rouge.get_scores(generated, item['output'])[0]
        rouge_scores.append(rouge_score)
        
        # 计算BLEU
        reference = [item['output'].split()]
        candidate = generated.split()
        bleu_score = sentence_bleu(reference, candidate)
        bleu_scores.append(bleu_score)
    
    return {
        'avg_bleu': sum(bleu_scores) / len(bleu_scores),
        'avg_rouge_l': sum(score['rouge-l']['f'] for score in rouge_scores) / len(rouge_scores)
    }
```

### 2. 人工评估标准

```python
# 人工评估维度
evaluation_criteria = {
    "医学准确性": "评估内容是否符合医学标准",
    "专业术语": "是否正确使用医学专业术语",
    "实用性": "建议是否具体可操作",
    "完整性": "是否涵盖所有相关评估维度",
    "安全性": "是否包含适当的医疗免责声明"
}

# 评分标准: 1-5分，5分最高
def manual_evaluation_template():
    return {
        "sample_id": "",
        "instruction": "",
        "generated_output": "",
        "reference_output": "",
        "scores": {
            "医学准确性": 0,
            "专业术语": 0,
            "实用性": 0,
            "完整性": 0,
            "安全性": 0
        },
        "comments": ""
    }
```

## 部署建议 (Deployment Recommendations)

### 1. 模型优化

```python
# 模型量化
from transformers import BitsAndBytesConfig

quantization_config = BitsAndBytesConfig(
    load_in_4bit=True,
    bnb_4bit_compute_dtype=torch.float16,
    bnb_4bit_use_double_quant=True,
    bnb_4bit_quant_type="nf4"
)

model = AutoModelForCausalLM.from_pretrained(
    model_name,
    quantization_config=quantization_config,
    device_map="auto"
)
```

### 2. 推理优化

```python
def optimized_inference(model, tokenizer, instruction, max_length=512):
    # 构建输入
    input_text = f"指令: {instruction}\n回答:"
    
    # 编码
    inputs = tokenizer(
        input_text, 
        return_tensors="pt", 
        truncation=True, 
        max_length=max_length
    )
    
    # 生成
    with torch.no_grad():
        outputs = model.generate(
            **inputs,
            max_new_tokens=400,
            do_sample=True,
            temperature=0.7,
            top_p=0.9,
            repetition_penalty=1.1,
            pad_token_id=tokenizer.eos_token_id
        )
    
    # 解码
    response = tokenizer.decode(outputs[0], skip_special_tokens=True)
    response = response.replace(input_text, "").strip()
    
    return response
```

## 注意事项 (Important Notes)

### 1. 医学伦理

- 本数据集仅供学习研究使用
- 不能替代专业医疗诊断
- 部署时需添加医疗免责声明
- 建议结合医学专家审核

### 2. 数据使用

- 遵循数据使用协议
- 注意保护用户隐私
- 定期更新医学标准
- 建立质量监控机制

### 3. 模型限制

- 模型可能存在偏见
- 需要持续监控输出质量
- 建议人工审核关键决策
- 保持模型更新迭代
