#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿体格生长监测数据集构建器
用于从卫健委标准数据构建LLM微调训练数据集
"""

import pandas as pd
import json
import random
import numpy as np
from typing import Dict, List, Tuple, Any
import os
from datetime import datetime

class GrowthDatasetBuilder:
    """婴幼儿生长发育数据集构建器"""
    
    def __init__(self, excel_file: str):
        """
        初始化数据集构建器
        
        Args:
            excel_file: Excel文件路径
        """
        self.excel_file = excel_file
        self.data_tables = {}
        self.evaluation_methods = {}
        self.dataset = []
        
    def load_excel_data(self):
        """加载Excel文件中的所有数据表"""
        print("正在加载Excel数据...")
        
        xl = pd.ExcelFile(self.excel_file)
        
        # 加载评价方法表
        for sheet_name in xl.sheet_names:
            if '评价方法' in sheet_name:
                df = pd.read_excel(self.excel_file, sheet_name=sheet_name)
                self.evaluation_methods[sheet_name] = df
                print(f"已加载评价方法表: {sheet_name}")
        
        # 加载标准数据表
        for sheet_name in xl.sheet_names:
            if sheet_name.startswith('表 A.') or sheet_name.startswith('表 B.'):
                df = pd.read_excel(self.excel_file, sheet_name=sheet_name)
                self.data_tables[sheet_name] = df
                print(f"已加载数据表: {sheet_name} - 形状: {df.shape}")
    
    def parse_age_column(self, age_str: str) -> Tuple[int, str]:
        """解析年龄列，返回数值和单位"""
        if pd.isna(age_str):
            return None, None

        age_str = str(age_str).strip()

        # 处理复合年龄格式，如"1岁3月"
        if '岁' in age_str and '月' in age_str:
            parts = age_str.split('岁')
            years = int(parts[0])
            months_part = parts[1].replace('月', '').strip()
            months = int(months_part) if months_part else 0
            total_months = years * 12 + months
            return total_months, '月'
        elif '月' in age_str:
            age_num = int(age_str.replace('月', ''))
            return age_num, '月'
        elif '岁' in age_str:
            age_num = float(age_str.replace('岁', ''))
            return int(age_num * 12), '月'  # 转换为月
        return None, None
    
    def get_growth_assessment(self, value: float, percentiles: Dict[str, float], 
                            assessment_type: str = 'percentile') -> str:
        """根据百分位数或标准差评估生长水平"""
        if assessment_type == 'percentile':
            if value >= percentiles.get('P97', float('inf')):
                return '上'
            elif value >= percentiles.get('P75', float('inf')):
                return '中上'
            elif value >= percentiles.get('P25', float('inf')):
                return '中'
            elif value >= percentiles.get('P10', float('inf')):
                return '中下'
            else:
                return '下'
        return '中'
    
    def generate_qa_pairs(self):
        """生成问答对数据"""
        print("正在生成问答对数据...")
        
        # 基础知识问答
        basic_qa = [
            {
                "instruction": "什么是儿童生长发育监测？",
                "input": "",
                "output": "儿童生长发育监测是通过定期测量儿童的体重、身长/身高、头围等体格指标，并与标准参考值进行比较，评估儿童生长发育状况的过程。这有助于及早发现生长发育异常，为健康干预提供依据。"
            },
            {
                "instruction": "儿童生长发育评价有哪些方法？",
                "input": "",
                "output": "主要有两种评价方法：1）百分位数法：将儿童的测量值与同年龄、同性别儿童的百分位数进行比较；2）标准差法：计算儿童测量值与参考人群均值的标准差数。两种方法都能有效评估儿童的生长水平。"
            },
            {
                "instruction": "什么是生长发育的百分位数？",
                "input": "",
                "output": "百分位数表示在同年龄、同性别的儿童中，有多少百分比的儿童的测量值低于该值。例如，P50表示中位数，P97表示有97%的儿童测量值低于此值。P3-P97之间被认为是正常范围。"
            }
        ]
        
        self.dataset.extend(basic_qa)
        
        # 基于实际数据生成具体评估问答
        for table_name, df in self.data_tables.items():
            if '男童' in table_name and '体重' in table_name and 'A.' in table_name:
                self._generate_weight_assessment_qa(df, '男', table_name)
            elif '女童' in table_name and '体重' in table_name and 'A.' in table_name:
                self._generate_weight_assessment_qa(df, '女', table_name)
    
    def _clean_numeric_value(self, value):
        """清理数值，处理字符串和NaN"""
        if pd.isna(value):
            return None
        if isinstance(value, str):
            # 处理类似"10. 1"这样的格式
            value = value.replace(' ', '').replace('.', '.')
            try:
                return float(value)
            except:
                return None
        return float(value)

    def _generate_weight_assessment_qa(self, df: pd.DataFrame, gender: str, table_name: str):
        """生成体重评估相关的问答对"""
        # 检查数据表是否有必要的列
        if '年龄' not in df.columns:
            print(f"警告：表 {table_name} 缺少'年龄'列，跳过")
            return

        # 清理数据，只选择有效的行
        valid_rows = []
        for idx, row in df.iterrows():
            # 检查所有百分位数值是否有效
            percentile_cols = ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']
            valid = True
            for col in percentile_cols:
                if col not in df.columns or self._clean_numeric_value(row[col]) is None:
                    valid = False
                    break
            if valid and pd.notna(row['年龄']):
                valid_rows.append(row)

        if len(valid_rows) == 0:
            print(f"警告：表 {table_name} 没有有效数据行")
            return

        # 随机选择几个年龄点生成问答
        sample_size = min(5, len(valid_rows))
        sample_indices = random.sample(range(len(valid_rows)), sample_size)

        for idx in sample_indices:
            row = valid_rows[idx]
            age = row['年龄']
            age_num, age_unit = self.parse_age_column(age)

            if age_num is None:
                continue

            # 生成评估问答，清理数值
            percentiles = {}
            for col in ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']:
                percentiles[col] = self._clean_numeric_value(row[col])

            # 生成不同体重值的评估
            test_weights = [
                percentiles['P10'] - 0.2,  # 偏低
                percentiles['P50'],        # 正常
                percentiles['P90'] + 0.3   # 偏高
            ]

            for weight in test_weights:
                if weight is None or weight <= 0:
                    continue

                assessment = self.get_growth_assessment(weight, percentiles)

                qa = {
                    "instruction": f"请评估一个{age}的{gender}童体重{weight:.1f}kg的生长水平",
                    "input": "",
                    "output": f"根据7岁以下儿童生长发育标准，{age}的{gender}童体重{weight:.1f}kg属于{assessment}水平。参考标准：P50为{percentiles['P50']:.1f}kg，P25-P75范围为{percentiles['P25']:.1f}-{percentiles['P75']:.1f}kg。"
                }
                self.dataset.append(qa)
    
    def generate_guidance_samples(self):
        """生成指导建议样本"""
        print("正在生成指导建议样本...")
        
        guidance_templates = [
            {
                "scenario": "体重偏低",
                "instruction": "一个6月龄男婴体重5.5kg，请给出喂养指导建议",
                "output": "该婴儿体重偏低，建议：1）增加喂养频次，确保充足的母乳或配方奶摄入；2）如已添加辅食，选择营养密度高的食物；3）定期监测体重增长；4）排除疾病因素，必要时就医检查；5）保证充足睡眠，促进生长激素分泌。"
            },
            {
                "scenario": "身高偏矮",
                "instruction": "一个2岁女童身高80cm，请给出生长促进建议",
                "output": "该女童身高偏矮，建议：1）保证均衡营养，充足的蛋白质、钙质和维生素D摄入；2）鼓励适量运动，如跑跳等有助长高的活动；3）保证充足睡眠，建议每天10-12小时；4）定期监测生长速度；5）如生长速度持续缓慢，建议儿科内分泌科就诊。"
            }
        ]
        
        self.dataset.extend(guidance_templates)
    
    def save_dataset(self, output_file: str = "growth_monitoring_dataset.jsonl"):
        """保存数据集为JSONL格式"""
        print(f"正在保存数据集到 {output_file}...")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            for item in self.dataset:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        print(f"数据集已保存，共 {len(self.dataset)} 条记录")
        
        # 同时保存为JSON格式便于查看
        json_file = output_file.replace('.jsonl', '.json')
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(self.dataset, f, ensure_ascii=False, indent=2)
        
        print(f"同时保存JSON格式到 {json_file}")
    
    def generate_statistics(self):
        """生成数据集统计信息"""
        print("\n=== 数据集统计信息 ===")
        print(f"总样本数: {len(self.dataset)}")
        
        # 按类型统计
        instruction_types = {}
        for item in self.dataset:
            instruction = item['instruction']
            if '评估' in instruction:
                key = '评估类'
            elif '指导' in instruction or '建议' in instruction:
                key = '指导类'
            elif '什么是' in instruction or '如何' in instruction:
                key = '知识类'
            else:
                key = '其他'
            
            instruction_types[key] = instruction_types.get(key, 0) + 1
        
        for type_name, count in instruction_types.items():
            print(f"{type_name}: {count} 条")
    
    def build_dataset(self):
        """构建完整数据集"""
        print("开始构建婴幼儿体格生长监测数据集...")
        
        # 加载数据
        self.load_excel_data()
        
        # 生成各类样本
        self.generate_qa_pairs()
        self.generate_guidance_samples()
        
        # 打乱数据集
        random.shuffle(self.dataset)
        
        # 生成统计信息
        self.generate_statistics()
        
        # 保存数据集
        self.save_dataset()
        
        print("数据集构建完成！")

def main():
    """主函数"""
    excel_file = "7岁以下儿童生长发育标准（卫健委.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"错误：找不到文件 {excel_file}")
        return
    
    builder = GrowthDatasetBuilder(excel_file)
    builder.build_dataset()

if __name__ == "__main__":
    main()
