#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境设置脚本 - 帮助用户快速配置LLM辅助数据集构建环境
"""

import os
import sys
import subprocess
import json

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要Python 3.7+")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    return True

def install_dependencies():
    """安装依赖包"""
    print("\n📦 检查并安装依赖包...")
    
    required_packages = [
        "pandas",
        "requests", 
        "openpyxl"
    ]
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📥 正在安装 {package}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError:
                print(f"❌ {package} 安装失败")
                return False
    
    return True

def check_excel_file():
    """检查Excel文件是否存在"""
    print("\n📊 检查Excel数据文件...")
    
    excel_file = "7岁以下儿童生长发育标准（卫健委.xlsx"
    
    if os.path.exists(excel_file):
        print(f"✅ 找到Excel文件: {excel_file}")
        return True
    else:
        print(f"❌ 未找到Excel文件: {excel_file}")
        print("请确保Excel文件在当前目录下")
        return False

def setup_api_key():
    """设置API密钥"""
    print("\n🔑 设置Qwen API密钥...")
    
    # 检查是否已设置环境变量
    existing_key = os.getenv("QWEN_API_KEY")
    if existing_key:
        print("✅ 检测到已设置的API密钥")
        choice = input("是否要更新API密钥？(y/N): ").lower()
        if choice != 'y':
            return True
    
    print("\n📝 请输入您的Qwen API密钥:")
    print("💡 获取方式：访问 https://bailian.console.aliyun.com/")
    
    api_key = input("API密钥: ").strip()
    
    if not api_key:
        print("❌ API密钥不能为空")
        return False
    
    # 设置环境变量
    os.environ["QWEN_API_KEY"] = api_key
    
    # 创建.env文件
    try:
        with open(".env", "w", encoding="utf-8") as f:
            f.write(f"QWEN_API_KEY={api_key}\n")
        print("✅ API密钥已保存到 .env 文件")
    except Exception as e:
        print(f"⚠️  保存.env文件失败: {e}")
    
    # 提示用户设置永久环境变量
    print("\n💡 建议设置永久环境变量:")
    if os.name == 'nt':  # Windows
        print(f"Windows: setx QWEN_API_KEY \"{api_key}\"")
    else:  # Unix/Linux/Mac
        print(f"Unix/Linux/Mac: echo 'export QWEN_API_KEY=\"{api_key}\"' >> ~/.bashrc")
    
    return True

def test_api_connection():
    """测试API连接"""
    print("\n🔗 测试API连接...")
    
    try:
        # 导入测试模块
        from quick_test import test_qwen_api
        
        if test_qwen_api():
            print("✅ API连接测试成功")
            return True
        else:
            print("❌ API连接测试失败")
            return False
            
    except ImportError:
        print("⚠️  无法导入测试模块，跳过API测试")
        return True
    except Exception as e:
        print(f"❌ API测试出错: {e}")
        return False

def create_config_file():
    """创建配置文件"""
    print("\n⚙️  检查配置文件...")
    
    if os.path.exists("config.py"):
        print("✅ 配置文件已存在")
        return True
    
    print("❌ 配置文件不存在，请确保config.py文件在当前目录")
    return False

def show_next_steps():
    """显示后续步骤"""
    print("\n" + "="*60)
    print("🎉 环境设置完成！")
    print("="*60)
    
    print("\n📋 后续步骤:")
    print("1. 运行快速测试: python quick_test.py")
    print("2. 开始构建数据集: python llm_assisted_dataset_builder.py")
    print("3. 查看使用说明: 阅读 LLM辅助数据集构建使用说明.md")
    
    print("\n📁 重要文件:")
    print("- llm_assisted_dataset_builder.py  # 主要构建器")
    print("- config.py                        # 配置文件")
    print("- quick_test.py                    # 快速测试")
    print("- .env                             # API密钥文件")
    
    print("\n⚠️  注意事项:")
    print("- 确保有足够的API调用额度")
    print("- 生成大量数据时注意成本控制")
    print("- 建议先小批量测试，确认质量后再大规模生成")

def main():
    """主函数"""
    print("🚀 LLM辅助数据集构建环境设置")
    print("="*60)
    
    success = True
    
    # 检查Python版本
    if not check_python_version():
        success = False
    
    # 安装依赖
    if success and not install_dependencies():
        success = False
    
    # 检查Excel文件
    if success and not check_excel_file():
        success = False
    
    # 检查配置文件
    if success and not create_config_file():
        success = False
    
    # 设置API密钥
    if success and not setup_api_key():
        success = False
    
    # 测试API连接
    if success and not test_api_connection():
        print("⚠️  API连接测试失败，但环境设置已完成")
        print("请检查API密钥是否正确，网络是否正常")
    
    if success:
        show_next_steps()
    else:
        print("\n❌ 环境设置未完全成功，请检查上述错误信息")
        print("解决问题后可重新运行此脚本")

if __name__ == "__main__":
    main()
