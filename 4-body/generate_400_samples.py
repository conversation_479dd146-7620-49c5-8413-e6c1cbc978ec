#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成400条样本的优化脚本
专门用于快速生成大量高质量的婴幼儿体格生长监测数据
"""

import os
import sys
import json
import time
import pandas as pd
import random

# 设置API密钥
os.environ["QWEN_API_KEY"] = "sk-5eba46fbcff649d5bf28313bc865de10"

from llm_assisted_dataset_builder import LLMAssistedDatasetBuilder
from config import Config

def generate_400_samples():
    """生成400条样本"""
    print("🚀 开始生成400条婴幼儿体格生长监测样本")
    print("=" * 60)
    
    # 临时修改配置
    original_target = Config.DATASET_CONFIG["target_samples"]
    original_interval = Config.API_CONFIG["request_interval"]
    
    # 优化配置以提高生成速度
    Config.DATASET_CONFIG["target_samples"] = 400
    Config.API_CONFIG["request_interval"] = 0.8  # 稍微减少间隔
    
    try:
        builder = LLMAssistedDatasetBuilder()
        builder.load_excel_data()
        
        print(f"📊 已加载 {len(builder.data_tables)} 个数据表")
        
        # 分阶段生成样本
        total_generated = 0
        
        # 第一阶段：生成包含头围的多指标样本（120个）
        print("\n🎯 第一阶段：生成包含头围的多指标样本...")
        multi_count = builder.generate_multi_indicator_samples(120)
        total_generated += multi_count
        print(f"✅ 第一阶段完成，生成了 {multi_count} 个多指标样本")
        
        # 第二阶段：生成综合评估样本（100个）
        print(f"\n🏥 第二阶段：生成综合评估样本...")
        comp_count = builder.generate_comprehensive_samples(100)
        total_generated += comp_count
        print(f"✅ 第二阶段完成，生成了 {comp_count} 个综合评估样本")
        
        # 第三阶段：生成头围专项样本（60个）
        print(f"\n👶 第三阶段：生成头围专项样本...")
        head_count = 0
        head_tables = {name: df for name, df in builder.data_tables.items() if '头围' in name}
        for table_name, df in head_tables.items():
            remaining = 60 - head_count
            if remaining <= 0:
                break
            count = builder.generate_head_circumference_samples(df, table_name, remaining // len(head_tables) + 10)
            head_count += count
            if head_count >= 60:
                break
        
        total_generated += head_count
        print(f"✅ 第三阶段完成，生成了 {head_count} 个头围专项样本")
        
        # 第四阶段：生成单项指标样本（填充到400个）
        remaining_needed = 400 - total_generated
        if remaining_needed > 0:
            print(f"\n📈 第四阶段：生成单项指标样本（还需 {remaining_needed} 个）...")
            
            # 生成体重、身高、BMI样本
            indicators = ['体重', '身高', 'BMI']
            samples_per_indicator = remaining_needed // len(indicators)
            
            for indicator in indicators:
                if total_generated >= 400:
                    break
                    
                # 找到对应的数据表
                tables = [name for name in builder.data_tables.keys() if indicator in name and '年龄别' in name]
                
                for table_name in tables[:2]:  # 男女各一个表
                    if total_generated >= 400:
                        break
                        
                    df = builder.data_tables[table_name]
                    gender = '男' if '男童' in table_name else '女'
                    
                    # 生成样本
                    count = generate_single_indicator_samples(
                        builder, df, table_name, indicator, gender, 
                        min(samples_per_indicator // 2, 400 - total_generated)
                    )
                    total_generated += count
                    
                    print(f"  生成了 {count} 个{indicator}样本 (总计: {total_generated}/400)")
        
        # 生成基础知识问答（补充到400个）
        if total_generated < 400:
            remaining = 400 - total_generated
            print(f"\n📚 第五阶段：生成基础知识问答（还需 {remaining} 个）...")
            builder.generate_knowledge_qa_extended(remaining)
            total_generated = len(builder.dataset)
        
        print(f"\n🎉 样本生成完成！总计生成 {len(builder.dataset)} 个样本")
        
        # 保存数据集
        output_file = "growth_monitoring_400_samples.jsonl"
        builder.save_dataset(output_file)
        
        # 生成统计信息
        generate_sample_statistics(builder.dataset)
        
        return True
        
    except Exception as e:
        print(f"❌ 生成过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 恢复原始配置
        Config.DATASET_CONFIG["target_samples"] = original_target
        Config.API_CONFIG["request_interval"] = original_interval

def generate_single_indicator_samples(builder, df, table_name, indicator, gender, max_samples):
    """生成单项指标样本"""
    generated_count = 0
    unit = 'kg' if indicator == '体重' else ('cm' if indicator == '身高' else '')
    
    # 选择有效数据行
    valid_rows = []
    for _, row in df.iterrows():
        percentile_cols = ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']
        valid = True
        for col in percentile_cols:
            if col not in df.columns or builder._clean_numeric_value(row[col]) is None:
                valid = False
                break
        if valid and pd.notna(row['年龄']):
            valid_rows.append(row)
    
    if len(valid_rows) == 0:
        return 0
    
    # 随机选择行生成样本
    import random
    sample_rows = random.sample(valid_rows, min(max_samples // 2, len(valid_rows)))
    
    for row in sample_rows:
        if generated_count >= max_samples:
            break
            
        age = row['年龄']
        
        # 清理百分位数数据
        percentiles = {}
        for col in ['P3', 'P10', 'P25', 'P50', 'P75', 'P90', 'P97']:
            percentiles[col] = builder._clean_numeric_value(row[col])
        
        # 生成不同的测试值
        test_values = [percentiles['P25'], percentiles['P50'], percentiles['P75']]
        
        for value in test_values:
            if generated_count >= max_samples:
                break
                
            if value is None or value <= 0:
                continue
            
            assessment_level = builder.get_growth_assessment(value, percentiles)
            
            # 构建场景信息
            scenario = {
                'age': age,
                'gender': gender,
                'indicator': indicator,
                'value': value,
                'unit': unit,
                'percentiles': percentiles,
                'assessment_level': assessment_level
            }
            
            # 生成问题
            questions = builder.generate_diverse_questions(scenario, count=1)
            
            for question in questions:
                if generated_count >= max_samples:
                    break
                    
                # 生成评估
                assessment = builder.generate_professional_assessment(scenario)
                
                sample = {
                    "instruction": question,
                    "input": "",
                    "output": assessment
                }
                
                builder.dataset.append(sample)
                generated_count += 1
                
                # 控制API调用频率
                time.sleep(builder.request_interval)
    
    return generated_count

def generate_sample_statistics(dataset):
    """生成样本统计信息"""
    print("\n📊 数据集统计信息")
    print("=" * 40)
    print(f"总样本数: {len(dataset)}")
    
    # 分析指令类型
    instruction_types = {}
    for item in dataset:
        instruction = item['instruction']
        if '头围' in instruction:
            if '体重' in instruction and '身高' in instruction:
                key = '多指标+头围'
            else:
                key = '头围专项'
        elif '体重' in instruction and '身高' in instruction:
            key = '综合评估'
        elif '体重' in instruction:
            key = '体重评估'
        elif '身高' in instruction or '身长' in instruction:
            key = '身高评估'
        elif 'BMI' in instruction:
            key = 'BMI评估'
        elif '如何' in instruction or '什么是' in instruction:
            key = '基础知识'
        else:
            key = '其他'
        
        instruction_types[key] = instruction_types.get(key, 0) + 1
    
    print("\n指令类型分布:")
    for type_name, count in sorted(instruction_types.items(), key=lambda x: x[1], reverse=True):
        percentage = count / len(dataset) * 100
        print(f"  {type_name}: {count} 条 ({percentage:.1f}%)")
    
    # 检查头围相关样本
    head_related = sum(1 for item in dataset if '头围' in item['instruction'])
    print(f"\n包含头围的样本: {head_related} 条 ({head_related/len(dataset)*100:.1f}%)")

def main():
    """主函数"""
    print("🎯 400样本生成器")
    print("目标：生成400条高质量的婴幼儿体格生长监测样本")
    print("特色：包含大量头围相关的多指标评估")
    
    success = generate_400_samples()
    
    if success:
        print("\n🎉 400条样本生成成功！")
        print("\n📁 输出文件:")
        print("- growth_monitoring_400_samples.jsonl")
        print("- growth_monitoring_400_samples.json")
        
        print("\n🚀 下一步:")
        print("可以使用 final_dataset_processor.py 处理这个数据集")
        print("python3 final_dataset_processor.py")
    else:
        print("\n❌ 生成失败，请检查错误信息")

if __name__ == "__main__":
    main()
