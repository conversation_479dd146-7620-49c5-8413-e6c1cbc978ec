# 婴幼儿体格生长监测数据集

## 数据集描述

本数据集专门用于微调大语言模型，使其具备婴幼儿体格生长监测和指导功能。数据集基于国家卫健委发布的《7岁以下儿童生长发育标准》构建，包含体重、身高、BMI、头围等多个生长指标的评估和指导内容。

## 数据集统计

- **总样本数**: 140 条
- **训练集**: 112 条
- **验证集**: 14 条  
- **测试集**: 14 条

## 数据格式

每个样本包含以下字段：
- `instruction`: 用户指令/问题
- `input`: 额外输入信息（通常为空）
- `output`: 模型应该生成的回答

## 文件说明

### 标准格式
- `train.jsonl`: 训练集（JSONL格式）
- `validation.jsonl`: 验证集（JSONL格式）
- `test.jsonl`: 测试集（JSONL格式）

### 特定框架格式
- `alpaca_*.json`: Alpaca微调格式
- `chatglm_*.jsonl`: ChatGLM微调格式
- `*.csv`: CSV格式（便于查看）

### 元数据
- `dataset_info.json`: 数据集详细信息
- `README.md`: 本文件

## 使用方法

### 1. Alpaca微调
```python
# 使用alpaca_train.json进行微调
```

### 2. ChatGLM微调  
```python
# 使用chatglm_train.jsonl进行微调
```

### 3. 通用微调
```python
# 使用train.jsonl进行微调
```

## 数据集特点

1. **专业性强**: 基于权威医学标准构建
2. **覆盖全面**: 包含多个生长指标和年龄段
3. **实用性高**: 直接面向实际应用场景
4. **质量可靠**: 经过多轮验证和清理

## 注意事项

1. 本数据集仅用于研究和教育目的
2. 实际医疗应用需要专业医生指导
3. 模型输出不能替代专业医疗建议

## 版本信息

- **版本**: 1.0.0
- **创建日期**: 2025-07-21
- **数据源**: 7岁以下儿童生长发育标准（卫健委）

## 联系信息

如有问题或建议，请联系数据集维护者。
