#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试头围功能脚本 - 专门测试包含头围的多指标评估
"""

import os
import sys
import json

# 设置API密钥
os.environ["QWEN_API_KEY"] = "sk-5eba46fbcff649d5bf28313bc865de10"

from llm_assisted_dataset_builder import LLMAssistedDatasetBuilder
from config import Config

def test_multi_indicator_questions():
    """测试包含头围的多指标问题生成"""
    print("🎯 测试包含头围的多指标问题生成...")
    
    builder = LLMAssistedDatasetBuilder()
    
    questions = builder.generate_multi_indicator_questions(
        "6月", "男", 7.2, 65.0, 42.0, 17.1
    )
    
    print("生成的多指标评估问题:")
    for i, q in enumerate(questions, 1):
        print(f"{i}. {q}")
    
    # 检查问题是否包含头围信息
    has_head_circumference = any("头围" in q for q in questions)
    assert has_head_circumference, "生成的问题中没有包含头围信息"
    
    print("✅ 多指标问题生成测试通过（包含头围）")

def test_multi_indicator_assessment():
    """测试包含头围的多指标评估生成"""
    print("\n🏥 测试包含头围的多指标评估生成...")
    
    builder = LLMAssistedDatasetBuilder()
    
    scenario = {
        'age': '6月',
        'gender': '男',
        'weight': 7.2,
        'height': 65.0,
        'head_circumference': 42.0,
        'bmi': 17.1
    }
    
    assessment = builder.generate_multi_indicator_assessment(scenario)
    
    print("生成的多指标评估:")
    print("-" * 60)
    print(assessment)
    print("-" * 60)
    
    # 检查评估是否包含所有指标
    assert len(assessment) > 100, "评估内容过短"
    assert "体重" in assessment, "评估中未包含体重信息"
    assert "身高" in assessment or "身长" in assessment, "评估中未包含身高信息"
    assert "头围" in assessment, "评估中未包含头围信息"
    assert "BMI" in assessment or "体重指数" in assessment, "评估中未包含BMI信息"
    assert "脑" in assessment or "大脑" in assessment or "神经" in assessment, "评估中未强调脑发育"
    
    print("✅ 多指标评估生成测试通过（包含所有指标）")

def generate_head_circumference_samples():
    """生成包含头围的示例数据集"""
    print("\n🚀 生成包含头围的示例数据集...")
    
    # 临时修改配置，生成少量样本用于测试
    original_target = Config.DATASET_CONFIG["target_samples"]
    Config.DATASET_CONFIG["target_samples"] = 20
    
    try:
        builder = LLMAssistedDatasetBuilder()
        builder.load_excel_data()
        
        # 生成包含头围的多指标样本
        multi_count = builder.generate_multi_indicator_samples(5)
        print(f"生成了 {multi_count} 个包含头围的多指标评估样本")
        
        # 保存示例数据集
        if builder.dataset:
            with open("head_circumference_sample_dataset.json", "w", encoding="utf-8") as f:
                json.dump(builder.dataset, f, ensure_ascii=False, indent=2)
            
            print(f"✅ 头围示例数据集已保存，共 {len(builder.dataset)} 个样本")
            
            # 显示样本
            print("\n📝 包含头围的样本预览:")
            for i, sample in enumerate(builder.dataset[:3], 1):
                print(f"\n样本 {i}:")
                print(f"问题: {sample['instruction']}")
                print(f"回答: {sample['output'][:150]}...")
                
                # 验证问题中是否包含头围
                if "头围" in sample['instruction']:
                    print("✅ 该样本包含头围信息")
                else:
                    print("⚠️  该样本未包含头围信息")
        
    finally:
        # 恢复原始配置
        Config.DATASET_CONFIG["target_samples"] = original_target

def analyze_head_circumference_data():
    """分析头围数据表"""
    print("\n📊 分析头围数据表...")
    
    builder = LLMAssistedDatasetBuilder()
    builder.load_excel_data()
    
    # 找到头围数据表
    head_tables = {name: df for name, df in builder.data_tables.items() if '头围' in name}
    
    print(f"找到 {len(head_tables)} 个头围数据表:")
    for table_name, df in head_tables.items():
        print(f"\n表名: {table_name}")
        print(f"数据维度: {df.shape}")
        print(f"年龄范围: {df['年龄'].iloc[0]} 到 {df['年龄'].iloc[-1]}")
        
        # 显示几个年龄点的数据
        sample_ages = df.sample(min(3, len(df)))
        print("样本数据:")
        for _, row in sample_ages.iterrows():
            age = row['年龄']
            p50 = row['P50']
            print(f"  {age}: P50 = {p50}cm")
    
    print("✅ 头围数据分析完成")

def create_instruction_examples():
    """创建包含头围的instruction示例"""
    print("\n📝 创建包含头围的instruction示例...")
    
    examples = [
        "请评估一个6月龄男婴体重7.2kg、身高65cm、头围42cm的综合发育情况",
        "1岁女童体重9.5kg、身长75cm、头围45cm，各项指标是否正常？",
        "体检报告显示：2岁男童体重12kg、身高85cm、头围48cm，请给出专业评估",
        "家长咨询：我家女宝18个月，体重10.5kg、身高80cm、头围46.5cm，发育如何？",
        "请分析一个9月龄男婴的生长发育情况：体重8.5kg、身长70cm、头围44cm"
    ]
    
    print("包含头围的instruction示例:")
    for i, example in enumerate(examples, 1):
        print(f"{i}. {example}")
    
    # 保存示例到文件
    with open("head_circumference_instructions.json", "w", encoding="utf-8") as f:
        json.dump(examples, f, ensure_ascii=False, indent=2)
    
    print("✅ instruction示例已保存到 head_circumference_instructions.json")

def main():
    """主函数"""
    print("🧪 头围功能测试套件")
    print("=" * 60)
    
    try:
        # 分析头围数据
        analyze_head_circumference_data()
        
        # 创建instruction示例
        create_instruction_examples()
        
        # LLM功能测试
        test_multi_indicator_questions()
        test_multi_indicator_assessment()
        
        # 生成示例数据集
        generate_head_circumference_samples()
        
        print("\n" + "=" * 60)
        print("🎉 头围功能测试全部通过！")
        print("\n📋 新功能总结:")
        print("✅ 包含头围的多指标问题生成")
        print("✅ 头围+体重+身高+BMI综合评估")
        print("✅ 强调脑发育重要性的专业评估")
        print("✅ 多样化的头围相关问题表述")
        
        print("\n🚀 现在instruction中会包含头围信息！")
        print("运行完整构建器: python llm_assisted_dataset_builder.py")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
