#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解析.doc文件并生成JSON格式的数据集
支持多种解析方法，尽可能提取有用的文本内容
"""

import os
import glob
import json
import re
import subprocess
from collections import defaultdict
import tempfile

def try_libreoffice_convert(doc_path):
    """尝试使用LibreOffice将.doc转换为.txt"""
    try:
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 使用LibreOffice转换
            cmd = [
                'libreoffice', '--headless', '--convert-to', 'txt',
                '--outdir', temp_dir, doc_path
            ]
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                # 查找生成的txt文件
                txt_files = glob.glob(os.path.join(temp_dir, '*.txt'))
                if txt_files:
                    with open(txt_files[0], 'r', encoding='utf-8') as f:
                        return f.read()
            return None
    except Exception as e:
        print(f"LibreOffice转换失败: {e}")
        return None

def try_antiword_extract(doc_path):
    """尝试使用antiword提取文本"""
    try:
        result = subprocess.run(['antiword', doc_path], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            return result.stdout
        return None
    except Exception as e:
        print(f"antiword提取失败: {e}")
        return None

def try_catdoc_extract(doc_path):
    """尝试使用catdoc提取文本"""
    try:
        result = subprocess.run(['catdoc', doc_path], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            return result.stdout
        return None
    except Exception as e:
        print(f"catdoc提取失败: {e}")
        return None

def extract_text_with_python_docx2txt():
    """尝试使用docx2txt库"""
    try:
        import docx2txt
        return True
    except ImportError:
        return False

def simple_binary_extract(doc_path):
    """简单的二进制文本提取"""
    try:
        with open(doc_path, 'rb') as f:
            content = f.read()
        
        # 尝试不同的编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'big5', 'utf-16le', 'utf-16be']
        
        best_text = ""
        best_score = 0
        
        for encoding in encodings:
            try:
                text = content.decode(encoding, errors='ignore')
                
                # 提取中文字符
                chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
                chinese_ratio = len(chinese_chars) / len(text) if text else 0
                
                # 评分：中文字符比例 + 可读性
                score = chinese_ratio * len(chinese_chars)
                
                if score > best_score:
                    best_score = score
                    best_text = text
                    
            except Exception:
                continue
        
        if best_text:
            # 清理文本，提取有意义的部分
            lines = []
            for line in best_text.split('\n'):
                # 只保留包含中文的行
                if re.search(r'[\u4e00-\u9fff]', line):
                    # 清理特殊字符
                    cleaned = re.sub(r'[^\u4e00-\u9fff\u3000-\u303f\uff00-\uffefa-zA-Z0-9\s\.\,\!\?\:\;\(\)\[\]（）、。，！？：；]', '', line)
                    if len(cleaned.strip()) > 2:
                        lines.append(cleaned.strip())
            
            return '\n'.join(lines) if lines else None
        
        return None
        
    except Exception as e:
        print(f"二进制提取失败: {e}")
        return None

def extract_text_from_doc(doc_path):
    """尝试多种方法提取.doc文件的文本"""
    print(f"正在解析: {os.path.basename(doc_path)}")
    
    methods = [
        ("LibreOffice", try_libreoffice_convert),
        ("antiword", try_antiword_extract),
        ("catdoc", try_catdoc_extract),
        ("二进制提取", simple_binary_extract)
    ]
    
    for method_name, method_func in methods:
        print(f"  尝试方法: {method_name}")
        try:
            text = method_func(doc_path)
            if text and len(text.strip()) > 100:  # 至少要有100个字符
                print(f"  ✅ {method_name} 成功提取 {len(text)} 字符")
                return text, method_name
            else:
                print(f"  ❌ {method_name} 提取失败或内容太少")
        except Exception as e:
            print(f"  ❌ {method_name} 出错: {e}")
    
    print(f"  ❌ 所有方法都失败")
    return None, None

def parse_assessment_content(text, filename):
    """解析评估表内容，提取结构化信息"""
    if not text:
        return None
    
    # 提取月龄信息
    age_match = re.search(r'(\d+(?:-\d+)?)月龄', filename)
    age_group = age_match.group(1) if age_match else "未知"
    
    # 分析文本结构
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    
    # 尝试识别评估项目
    assessment_items = []
    current_domain = None
    
    for line in lines:
        # 识别发展领域
        if any(domain in line for domain in ['大运动', '精细运动', '语言', '认知', '社会', '自理']):
            current_domain = line
        
        # 识别评估项目（通常以数字开头或包含"能够"、"会"等词）
        if re.match(r'^\d+[\.\、]', line) or '能够' in line or '会' in line or '可以' in line:
            assessment_items.append({
                'domain': current_domain,
                'item': line,
                'type': 'assessment_item'
            })
    
    # 提取关键词
    keywords = []
    for keyword_pattern in ['大运动', '精细运动', '语言', '认知', '社会', '情感', '自理', '发展', '能力', '评估']:
        if keyword_pattern in text:
            keywords.append(keyword_pattern)
    
    return {
        'filename': filename,
        'age_group': age_group,
        'text_length': len(text),
        'line_count': len(lines),
        'assessment_items': assessment_items,
        'keywords': keywords,
        'raw_text': text,
        'structured_content': {
            'lines': lines[:50],  # 只保存前50行作为预览
            'domains_found': list(set([item['domain'] for item in assessment_items if item['domain']]))
        }
    }

def parse_all_docs_to_json():
    """解析所有.doc文件并生成JSON数据集"""
    assessment_dir = "assessment_forms"
    
    # 查找所有.doc文件
    doc_files = glob.glob(os.path.join(assessment_dir, "*.doc"))
    doc_files.sort()
    
    print(f"🔍 找到 {len(doc_files)} 个.doc文件")
    print("=" * 60)
    
    dataset = {
        'metadata': {
            'dataset_name': '婴幼儿综合能力发展评估量表数据集',
            'source': '广东省残疾人康复中心',
            'total_files': len(doc_files),
            'extraction_date': '2025-01-25',
            'format_version': '1.0'
        },
        'documents': [],
        'extraction_summary': {
            'successful': 0,
            'failed': 0,
            'methods_used': defaultdict(int),
            'total_text_length': 0
        }
    }
    
    for i, doc_path in enumerate(doc_files, 1):
        filename = os.path.basename(doc_path)
        print(f"\n[{i}/{len(doc_files)}] 处理文件: {filename}")
        
        # 提取文本
        text, method = extract_text_from_doc(doc_path)
        
        if text:
            # 解析内容
            parsed_content = parse_assessment_content(text, filename)
            
            if parsed_content:
                parsed_content['extraction_method'] = method
                dataset['documents'].append(parsed_content)
                dataset['extraction_summary']['successful'] += 1
                dataset['extraction_summary']['methods_used'][method] += 1
                dataset['extraction_summary']['total_text_length'] += len(text)
                
                print(f"  ✅ 成功解析，提取 {len(text)} 字符")
                print(f"  📊 找到 {len(parsed_content['assessment_items'])} 个评估项目")
            else:
                dataset['extraction_summary']['failed'] += 1
                print(f"  ❌ 解析失败")
        else:
            dataset['extraction_summary']['failed'] += 1
            print(f"  ❌ 文本提取失败")
    
    # 保存结果
    output_file = 'assessment_dataset.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(dataset, f, ensure_ascii=False, indent=2)
    
    # 生成简化版本（不包含原始文本）
    simplified_dataset = {
        'metadata': dataset['metadata'],
        'documents': [],
        'extraction_summary': dataset['extraction_summary']
    }
    
    for doc in dataset['documents']:
        simplified_doc = {k: v for k, v in doc.items() if k != 'raw_text'}
        simplified_dataset['documents'].append(simplified_doc)
    
    simplified_output = 'assessment_dataset_simplified.json'
    with open(simplified_output, 'w', encoding='utf-8') as f:
        json.dump(simplified_dataset, f, ensure_ascii=False, indent=2)
    
    # 打印总结
    print("\n" + "=" * 60)
    print("📊 解析完成总结")
    print("=" * 60)
    print(f"✅ 成功解析: {dataset['extraction_summary']['successful']} 个文件")
    print(f"❌ 解析失败: {dataset['extraction_summary']['failed']} 个文件")
    print(f"📝 总文本长度: {dataset['extraction_summary']['total_text_length']:,} 字符")
    
    print(f"\n🔧 使用的提取方法:")
    for method, count in dataset['extraction_summary']['methods_used'].items():
        print(f"  {method}: {count} 个文件")
    
    print(f"\n📄 生成的文件:")
    print(f"  {output_file} - 完整数据集（包含原始文本）")
    print(f"  {simplified_output} - 简化数据集（不含原始文本）")
    
    return dataset

def main():
    """主函数"""
    print("🚀 开始解析.doc文件并生成JSON数据集")
    print("将尝试多种方法提取文本内容...")
    print()
    
    # 检查必要的工具
    tools_status = {}
    for tool in ['libreoffice', 'antiword', 'catdoc']:
        try:
            result = subprocess.run(['which', tool], capture_output=True)
            tools_status[tool] = result.returncode == 0
        except:
            tools_status[tool] = False
    
    print("🔧 工具可用性检查:")
    for tool, available in tools_status.items():
        status = "✅ 可用" if available else "❌ 不可用"
        print(f"  {tool}: {status}")
    print()
    
    if not any(tools_status.values()):
        print("⚠️  警告: 没有找到专业的文档转换工具")
        print("   将使用简单的二进制提取方法，效果可能有限")
        print("   建议安装: brew install antiword 或 apt-get install antiword")
        print()
    
    # 开始解析
    dataset = parse_all_docs_to_json()
    
    print(f"\n✅ 数据集生成完成！")
    print(f"📊 成功率: {dataset['extraction_summary']['successful']}/{dataset['metadata']['total_files']} ({dataset['extraction_summary']['successful']/dataset['metadata']['total_files']*100:.1f}%)")

if __name__ == "__main__":
    main()
