#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化但有效的.doc文件解析器
专门处理婴幼儿评估表
"""

import os
import glob
import json
import re
from collections import defaultdict

def extract_chinese_text_from_doc(file_path):
    """从.doc文件中提取中文文本"""
    try:
        with open(file_path, 'rb') as f:
            content = f.read()
        
        # 尝试不同的编码
        best_text = ""
        best_score = 0
        best_method = "无"
        
        encodings = [
            ('utf-8', 'UTF-8'),
            ('gbk', 'GBK'),
            ('gb2312', 'GB2312'),
            ('utf-16le', 'UTF-16LE'),
            ('utf-16be', 'UTF-16BE'),
            ('big5', 'Big5')
        ]
        
        for encoding, name in encodings:
            try:
                # 解码内容
                text = content.decode(encoding, errors='ignore')
                
                # 提取中文字符和常见标点
                chinese_chars = []
                for char in text:
                    if ('\u4e00' <= char <= '\u9fff' or  # 中文字符
                        '\u3000' <= char <= '\u303f' or  # 中文标点
                        '\uff00' <= char <= '\uffef' or  # 全角字符
                        char in '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ .,!?:;()[]（）、。，！？：；'):
                        chinese_chars.append(char)
                
                if not chinese_chars:
                    continue
                
                # 重新组合文本
                extracted_text = ''.join(chinese_chars)
                
                # 按行分割并清理
                lines = extracted_text.split('\n')
                clean_lines = []
                
                for line in lines:
                    line = line.strip()
                    if len(line) > 2:  # 至少3个字符
                        # 检查是否包含足够的中文字符
                        chinese_count = sum(1 for c in line if '\u4e00' <= c <= '\u9fff')
                        if chinese_count >= 2 or '宝宝' in line or '月龄' in line:
                            clean_lines.append(line)
                
                if not clean_lines:
                    continue
                
                final_text = '\n'.join(clean_lines)
                
                # 计算质量分数
                total_chars = len(final_text)
                chinese_count = sum(1 for c in final_text if '\u4e00' <= c <= '\u9fff')
                
                if total_chars == 0:
                    continue
                
                chinese_ratio = chinese_count / total_chars
                
                # 检查关键词
                keywords = ['宝宝', '能力', '发展', '评估', '月龄', '运动', '语言', '认知', '社会', '适应', '能', '不能']
                keyword_score = sum(10 for keyword in keywords if keyword in final_text)
                
                # 综合评分
                score = chinese_ratio * 100 + keyword_score + min(chinese_count, 500)
                
                if score > best_score:
                    best_score = score
                    best_text = final_text
                    best_method = name
                    
            except Exception as e:
                continue
        
        return best_text, best_method, best_score
        
    except Exception as e:
        print(f"处理文件 {file_path} 时出错: {e}")
        return None, None, 0

def analyze_assessment_content(text, filename):
    """分析评估表内容"""
    if not text:
        return None
    
    # 提取月龄信息
    age_match = re.search(r'(\d+(?:-\d+)?)月龄', filename)
    age_group = age_match.group(1) if age_match else "未知"
    
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    
    # 识别评估项目
    assessment_items = []
    current_section = None
    
    for line in lines:
        # 识别章节
        if any(keyword in line for keyword in ['身体运动', '探索与操作', '听觉言语', '社会适应', '大运动', '精细运动']):
            current_section = line
            continue
        
        # 识别评估题目
        if ('宝宝' in line and len(line) > 10) or (re.match(r'^\d+', line) and '能' in line):
            assessment_items.append({
                'section': current_section,
                'content': line,
                'has_ability_check': '能' in line and '不能' in line
            })
    
    # 识别发展领域
    domains = []
    domain_keywords = {
        '大运动发展': ['大运动', '身体运动', '控制', '抬头', '翻身', '坐', '爬', '站', '走'],
        '精细运动发展': ['精细运动', '探索', '操作', '抓握', '手指', '玩具'],
        '语言发展': ['听觉', '言语', '语言', '声音', '说话', '叫'],
        '认知发展': ['认知', '思维', '记忆', '注意'],
        '社会情感发展': ['社会', '适应', '微笑', '哭', '情感', '交往'],
        '自理能力': ['自理', '吃', '喝', '穿', '洗']
    }
    
    for domain, keywords in domain_keywords.items():
        if any(keyword in text for keyword in keywords):
            domains.append(domain)
    
    # 提取关键统计信息
    stats = {
        'total_lines': len(lines),
        'assessment_items_count': len(assessment_items),
        'has_baby_mentions': text.count('宝宝'),
        'has_ability_checks': sum(1 for item in assessment_items if item['has_ability_check']),
        'chinese_char_count': sum(1 for c in text if '\u4e00' <= c <= '\u9fff'),
        'total_char_count': len(text)
    }
    
    return {
        'filename': filename,
        'age_group': age_group,
        'text_length': len(text),
        'assessment_items': assessment_items,
        'domains_identified': domains,
        'statistics': stats,
        'sample_lines': lines[:10],  # 前10行作为样本
        'quality_indicators': {
            'chinese_ratio': stats['chinese_char_count'] / stats['total_char_count'] if stats['total_char_count'] > 0 else 0,
            'has_structured_content': len(assessment_items) > 0,
            'has_domain_coverage': len(domains) > 0
        }
    }

def create_simple_dataset():
    """创建简化的数据集"""
    assessment_dir = "assessment_forms"
    doc_files = glob.glob(os.path.join(assessment_dir, "*.doc"))
    doc_files.sort()
    
    print("🔍 简化.doc文件解析")
    print("=" * 50)
    
    dataset = {
        'metadata': {
            'dataset_name': '婴幼儿综合能力发展评估量表数据集（简化版）',
            'description': '使用简化但稳定的算法提取的评估表内容',
            'source': '广东省残疾人康复中心',
            'total_files': len(doc_files),
            'extraction_date': '2025-01-25',
            'format_version': '3.0'
        },
        'documents': [],
        'extraction_summary': {
            'successful': 0,
            'failed': 0,
            'total_text_length': 0,
            'total_assessment_items': 0,
            'methods_used': defaultdict(int),
            'quality_scores': []
        }
    }
    
    for i, doc_path in enumerate(doc_files, 1):
        filename = os.path.basename(doc_path)
        print(f"\n[{i}/{len(doc_files)}] 处理: {filename}")
        
        # 提取文本
        text, method, quality_score = extract_chinese_text_from_doc(doc_path)
        
        if text and quality_score > 20:  # 降低质量阈值
            # 分析内容
            analysis = analyze_assessment_content(text, filename)
            
            if analysis:
                analysis['extraction_method'] = method
                analysis['quality_score'] = quality_score
                
                dataset['documents'].append(analysis)
                dataset['extraction_summary']['successful'] += 1
                dataset['extraction_summary']['methods_used'][method] += 1
                dataset['extraction_summary']['total_text_length'] += len(text)
                dataset['extraction_summary']['total_assessment_items'] += len(analysis['assessment_items'])
                dataset['extraction_summary']['quality_scores'].append(quality_score)
                
                print(f"  ✅ 成功 - 方法: {method}")
                print(f"  📊 质量分数: {quality_score:.1f}")
                print(f"  📝 文本长度: {len(text)} 字符")
                print(f"  🎯 评估项目: {len(analysis['assessment_items'])} 个")
                print(f"  🧠 识别领域: {len(analysis['domains_identified'])} 个")
                
                # 显示样本
                if analysis['sample_lines']:
                    sample = analysis['sample_lines'][0] if analysis['sample_lines'] else ""
                    if len(sample) > 50:
                        sample = sample[:50] + "..."
                    print(f"  📄 样本: {sample}")
            else:
                dataset['extraction_summary']['failed'] += 1
                print(f"  ❌ 分析失败")
        else:
            dataset['extraction_summary']['failed'] += 1
            print(f"  ❌ 提取失败 - 质量分数: {quality_score:.1f}")
    
    # 计算统计信息
    if dataset['extraction_summary']['quality_scores']:
        dataset['extraction_summary']['average_quality_score'] = sum(dataset['extraction_summary']['quality_scores']) / len(dataset['extraction_summary']['quality_scores'])
        dataset['extraction_summary']['min_quality_score'] = min(dataset['extraction_summary']['quality_scores'])
        dataset['extraction_summary']['max_quality_score'] = max(dataset['extraction_summary']['quality_scores'])
    
    # 保存结果
    output_file = 'simple_assessment_dataset.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(dataset, f, ensure_ascii=False, indent=2)
    
    # 生成报告
    print("\n" + "=" * 50)
    print("📊 简化解析结果总结")
    print("=" * 50)
    print(f"✅ 成功解析: {dataset['extraction_summary']['successful']} 个文件")
    print(f"❌ 解析失败: {dataset['extraction_summary']['failed']} 个文件")
    print(f"📈 成功率: {dataset['extraction_summary']['successful']/len(doc_files)*100:.1f}%")
    print(f"📝 总文本长度: {dataset['extraction_summary']['total_text_length']:,} 字符")
    print(f"🎯 总评估项目: {dataset['extraction_summary']['total_assessment_items']} 个")
    
    if dataset['extraction_summary']['quality_scores']:
        print(f"⭐ 平均质量分数: {dataset['extraction_summary']['average_quality_score']:.1f}")
        print(f"📊 质量分数范围: {dataset['extraction_summary']['min_quality_score']:.1f} - {dataset['extraction_summary']['max_quality_score']:.1f}")
    
    print(f"\n🔧 使用的提取方法:")
    for method, count in dataset['extraction_summary']['methods_used'].items():
        print(f"  {method}: {count} 个文件")
    
    print(f"\n📄 生成文件: {output_file}")
    
    return dataset

def main():
    """主函数"""
    print("🚀 启动简化.doc文件解析器")
    print("专门针对婴幼儿评估表进行优化")
    print()
    
    dataset = create_simple_dataset()
    
    print(f"\n✅ 简化解析完成！")
    if dataset['extraction_summary']['successful'] > 0:
        print(f"🎯 成功提取了 {dataset['extraction_summary']['successful']} 个评估表的内容")
        print(f"📊 总共识别了 {dataset['extraction_summary']['total_assessment_items']} 个评估项目")
    else:
        print("⚠️  未能成功提取任何内容")

if __name__ == "__main__":
    main()
