#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿综合能力发展评估量表下载器
下载0-1月龄到34月龄的所有评估表文件
以及听觉、语言能力和构音障碍相关评估表
"""

import requests
import os
import time
from urllib.parse import urljoin
import re

# 基础URL
BASE_URL = "https://www.gddprc.org.cn"

# 要下载的文件列表 (0-1月龄到34月龄)
FILES_TO_DOWNLOAD = [
    {
        "name": "1、婴幼儿综合能力发展评估量表(0~1月龄)",
        "url": "/attachements/2021/04/08/e8afb6d93ec64bb84c59ab95bd41d9f1.doc",
        "filename": "01_婴幼儿综合能力发展评估量表(0-1月龄).doc"
    },
    {
        "name": "2、婴幼儿综合能力发展评估量表(2月龄)",
        "url": "/attachements/2021/04/08/2c9ae0a9f8607e3cb31aa2fbb41ea0bf.doc",
        "filename": "02_婴幼儿综合能力发展评估量表(2月龄).doc"
    },
    {
        "name": "3、婴幼儿综合能力发展评估量表(3月龄)",
        "url": "/attachements/2021/04/08/4936ac323c2b622f12c32c740aa4ae62.doc",
        "filename": "03_婴幼儿综合能力发展评估量表(3月龄).doc"
    },
    {
        "name": "4、婴幼儿综合能力发展评估量表(4月龄)",
        "url": "/attachements/2021/04/08/07da908ea69eebe4aa165ff148607acc.doc",
        "filename": "04_婴幼儿综合能力发展评估量表(4月龄).doc"
    },
    {
        "name": "5、婴幼儿综合能力发展评估量表(5月龄)",
        "url": "/attachements/2021/04/08/899ae32dc7519939c4857ec691e054c2.doc",
        "filename": "05_婴幼儿综合能力发展评估量表(5月龄).doc"
    },
    {
        "name": "6、婴幼儿综合能力发展评估量表(6月龄)",
        "url": "/attachements/2021/04/08/7383c3c59124697a12991f5140996362.doc",
        "filename": "06_婴幼儿综合能力发展评估量表(6月龄).doc"
    },
    {
        "name": "7、婴幼儿综合能力发展评估量表(7月龄)",
        "url": "/attachements/2021/04/08/d2f0d77e1f9e4bc7981844cb9f3dabba.doc",
        "filename": "07_婴幼儿综合能力发展评估量表(7月龄).doc"
    },
    {
        "name": "8、婴幼儿综合能力发展评估量表(8月龄)",
        "url": "/attachements/2021/04/08/ca8861868ea3ddadf0e32e3c81f25d19.doc",
        "filename": "08_婴幼儿综合能力发展评估量表(8月龄).doc"
    },
    {
        "name": "9、婴幼儿综合能力发展评估量表(9月龄)",
        "url": "/attachements/2021/04/08/09f10bf2522026987ea5a9a9e003f4e4.doc",
        "filename": "09_婴幼儿综合能力发展评估量表(9月龄).doc"
    },
    {
        "name": "10、婴幼儿综合能力发展评估量表(10月龄)",
        "url": "/attachements/2021/04/08/9a9c905833003d17f78bcc44cf348874.doc",
        "filename": "10_婴幼儿综合能力发展评估量表(10月龄).doc"
    },
    {
        "name": "11、婴幼儿综合能力发展评估量表(11月龄)",
        "url": "/attachements/2021/04/08/017924524a5226430da81387c0c5384c.doc",
        "filename": "11_婴幼儿综合能力发展评估量表(11月龄).doc"
    },
    {
        "name": "13、婴幼儿综合能力发展评估量表(13月龄)",
        "url": "/attachements/2021/04/08/08143220486b0b5d03b071efda39484c.doc",
        "filename": "13_婴幼儿综合能力发展评估量表(13月龄).doc"
    },
    {
        "name": "14、婴幼儿综合能力发展评估量表(15月龄)",
        "url": "/attachements/2021/04/08/2233d48d045d3f3678b3b54b5146c780.doc",
        "filename": "15_婴幼儿综合能力发展评估量表(15月龄).doc"
    },
    {
        "name": "15、婴幼儿综合能力发展评估量表(17月龄)",
        "url": "/attachements/2021/04/08/3548e1797d926c50b75a8aa1683bf59f.doc",
        "filename": "17_婴幼儿综合能力发展评估量表(17月龄).doc"
    },
    {
        "name": "16、婴幼儿综合能力发展评估量表(19月龄)",
        "url": "/attachements/2021/04/08/3fe518e50999226f27d8c3c5393beb7b.doc",
        "filename": "19_婴幼儿综合能力发展评估量表(19月龄).doc"
    },
    {
        "name": "17、婴幼儿综合能力发展评估量表(21月龄)",
        "url": "/attachements/2021/04/08/718305e918571ac35b2bced3b06e551d.doc",
        "filename": "21_婴幼儿综合能力发展评估量表(21月龄).doc"
    },
    {
        "name": "18、婴幼儿综合能力发展评估量表(23月龄)",
        "url": "/attachements/2021/04/08/f51192bc0be6f8b2529939d1700a587e.doc",
        "filename": "23_婴幼儿综合能力发展评估量表(23月龄).doc"
    },
    {
        "name": "19、婴幼儿综合能力发展评估量表(25月龄)",
        "url": "/attachements/2021/04/08/8a7ae958ba68a9b01054fbae1105e2b4.doc",
        "filename": "25_婴幼儿综合能力发展评估量表(25月龄).doc"
    },
    {
        "name": "20、婴幼儿综合能力发展评估量表(28月龄)",
        "url": "/attachements/2021/04/08/1d4d07fc2bd3335523a17474d9d8d788.doc",
        "filename": "28_婴幼儿综合能力发展评估量表(28月龄).doc"
    },
    {
        "name": "21、婴幼儿综合能力发展评估量表(31月龄)",
        "url": "/attachements/2021/04/08/60b10620b7400ba95fc519337c14e9e8.doc",
        "filename": "31_婴幼儿综合能力发展评估量表(31月龄).doc"
    },
    {
        "name": "22、婴幼儿综合能力发展评估量表(34月龄)",
        "url": "/attachements/2021/04/08/74c188a1a5dc38c59f672ee744011521.doc",
        "filename": "34_婴幼儿综合能力发展评估量表(34月龄).doc"
    },
    # 听觉和语言能力评估表
    {
        "name": "25、听觉能力评估记录表",
        "url": "/attachements/2021/04/08/21ba07674068cbb9f1c881b869e74443.doc",
        "filename": "25_听觉能力评估记录表.doc"
    },
    {
        "name": "26、语言能力评估记录表(7个月)",
        "url": "/attachements/2021/04/08/99114b899dc3dde70b4b79f40571d872.doc",
        "filename": "26_语言能力评估记录表(7个月).doc"
    },
    {
        "name": "27、语言能力评估记录表（13、19个月)",
        "url": "/attachements/2021/04/08/11d7a1124bc7fe74627b97416ee509bf.doc",
        "filename": "27_语言能力评估记录表(13-19个月).doc"
    },
    {
        "name": "28、语言能力评估记录表（25个月)",
        "url": "/attachements/2021/04/08/6a5321b68764301367ec14642463f5e9.doc",
        "filename": "28_语言能力评估记录表(25个月).doc"
    },
    {
        "name": "29、语言能力评估记录表（37个月)",
        "url": "/attachements/2021/04/08/fefe490a8eb6b6c6a509944e31538977.doc",
        "filename": "29_语言能力评估记录表(37个月).doc"
    },
    # 构音障碍相关评估表
    {
        "name": "31、Frenchay构音障碍评定法",
        "url": "/attachements/2021/04/08/94177c762298bf2c7b3ac1dd58496e06.doc",
        "filename": "31_Frenchay构音障碍评定法.doc"
    },
    {
        "name": "32、S-S法语言发育迟缓",
        "url": "/attachements/2021/04/08/c0e6edf6318e7d0228f4a903e0a7402f.doc",
        "filename": "32_S-S法语言发育迟缓.doc"
    },
    {
        "name": "33、构音器官检查",
        "url": "/attachements/2021/04/08/bd317b58f8745a5d1c4f55e353c35402.doc",
        "filename": "33_构音器官检查.doc"
    },
    {
        "name": "34、构音障碍评价表",
        "url": "/attachements/2021/04/08/150f933e33f275a54e9b7a99c52c4288.doc",
        "filename": "34_构音障碍评价表.doc"
    }
]

def create_session():
    """创建带重试机制的session"""
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    })
    return session

def download_file(session, file_info, download_dir="assessment_forms"):
    """下载单个文件"""
    url = urljoin(BASE_URL, file_info["url"])
    filename = file_info["filename"]
    filepath = os.path.join(download_dir, filename)
    
    # 如果文件已存在，跳过下载
    if os.path.exists(filepath):
        print(f"⏭️  文件已存在，跳过: {filename}")
        return True
    
    try:
        print(f"📥 开始下载: {file_info['name']}")
        print(f"   URL: {url}")
        
        response = session.get(url, timeout=30, stream=True)
        response.raise_for_status()
        
        # 检查响应内容类型
        content_type = response.headers.get('content-type', '')
        if 'text/html' in content_type:
            print(f"❌ 下载失败: {filename} (返回HTML页面，可能是404或重定向)")
            return False
        
        # 创建目录
        os.makedirs(download_dir, exist_ok=True)
        
        # 下载文件
        with open(filepath, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        
        file_size = os.path.getsize(filepath)
        print(f"✅ 下载成功: {filename} ({file_size} bytes)")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 下载失败: {filename} - 网络错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 下载失败: {filename} - 其他错误: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始下载婴幼儿综合能力发展评估量表")
    print("   包括: 0-1月龄到34月龄评估表 + 听觉语言构音评估表")
    print(f"📁 文件将保存到: assessment_forms/ 目录")
    print(f"📊 总共需要下载 {len(FILES_TO_DOWNLOAD)} 个文件")
    print("=" * 60)
    
    # 创建session
    session = create_session()
    
    # 统计
    success_count = 0
    failed_files = []
    
    # 下载每个文件
    for i, file_info in enumerate(FILES_TO_DOWNLOAD, 1):
        print(f"\n[{i}/{len(FILES_TO_DOWNLOAD)}]", end=" ")
        
        success = download_file(session, file_info)
        if success:
            success_count += 1
        else:
            failed_files.append(file_info["name"])
        
        # 在下载之间稍作等待，避免对服务器造成压力
        if i < len(FILES_TO_DOWNLOAD):
            time.sleep(1)
    
    # 输出结果统计
    print("\n" + "=" * 60)
    print("📊 下载完成统计:")
    print(f"✅ 成功下载: {success_count}/{len(FILES_TO_DOWNLOAD)} 个文件")
    print(f"❌ 下载失败: {len(failed_files)} 个文件")
    
    if failed_files:
        print("\n❌ 失败的文件列表:")
        for failed_file in failed_files:
            print(f"   - {failed_file}")
        print("\n💡 提示: 可以重新运行脚本来重试失败的下载")
    else:
        print("\n🎉 所有文件下载成功！")
    
    print(f"\n📁 文件保存位置: {os.path.abspath('assessment_forms')}")

if __name__ == "__main__":
    main()
