#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动分析评估表结构和内容
基于文件信息和已知的评估表特征进行分析
"""

import os
import glob
import json
import re
from collections import defaultdict, Counter

def analyze_assessment_forms_manually():
    """基于文件名和已知信息手动分析评估表"""
    
    assessment_dir = "assessment_forms"
    pattern = os.path.join(assessment_dir, "*婴幼儿综合能力发展评估量表*.doc")
    files = glob.glob(pattern)
    files.sort()
    
    print("🔍 婴幼儿综合能力发展评估量表分析报告")
    print("=" * 60)
    
    # 基于文件名提取月龄信息
    age_groups = []
    file_info = []
    
    for file_path in files:
        filename = os.path.basename(file_path)
        file_size = os.path.getsize(file_path)
        
        # 提取月龄
        age_match = re.search(r'(\d+(?:-\d+)?)月龄', filename)
        if age_match:
            age = age_match.group(1)
            age_groups.append(age)
            file_info.append({
                'filename': filename,
                'age': age,
                'size_kb': file_size // 1024,
                'path': file_path
            })
    
    print(f"📁 找到 {len(files)} 个评估表文件")
    print(f"📅 覆盖月龄: {', '.join(sorted(set(age_groups), key=lambda x: int(x.split('-')[0])))}月龄")
    print()
    
    # 分析文件大小分布
    sizes = [info['size_kb'] for info in file_info]
    print(f"📊 文件大小分析:")
    print(f"   平均大小: {sum(sizes)//len(sizes)} KB")
    print(f"   最小文件: {min(sizes)} KB")
    print(f"   最大文件: {max(sizes)} KB")
    print()
    
    # 基于已知的婴幼儿发展评估标准分析
    development_domains = {
        '大运动发展': {
            'description': '粗大运动技能，如抬头、翻身、坐立、爬行、走路等',
            'age_milestones': {
                '0-1': ['抬头', '俯卧抬头'],
                '2-3': ['翻身', '坐立'],
                '4-6': ['独坐', '爬行'],
                '7-12': ['站立', '走路'],
                '13-24': ['跑步', '跳跃'],
                '25-36': ['单脚站立', '骑三轮车']
            }
        },
        '精细运动发展': {
            'description': '手部精细动作，如抓握、捏取、画画、使用工具等',
            'age_milestones': {
                '0-1': ['握拳反射'],
                '2-3': ['主动抓握'],
                '4-6': ['换手传递', '捏取'],
                '7-12': ['拇指食指捏取', '放手'],
                '13-24': ['叠积木', '画线'],
                '25-36': ['画圆', '使用剪刀']
            }
        },
        '语言发展': {
            'description': '语言理解和表达能力',
            'age_milestones': {
                '0-1': ['哭声变化'],
                '2-3': ['发出声音', '笑'],
                '4-6': ['咿呀学语'],
                '7-12': ['叫爸爸妈妈', '理解简单指令'],
                '13-24': ['词汇爆发期', '两词句'],
                '25-36': ['完整句子', '讲故事']
            }
        },
        '认知发展': {
            'description': '思维、记忆、注意力、问题解决能力',
            'age_milestones': {
                '0-1': ['视觉追踪'],
                '2-3': ['认识熟悉面孔'],
                '4-6': ['物体永恒性'],
                '7-12': ['模仿行为', '因果关系'],
                '13-24': ['符号思维', '假装游戏'],
                '25-36': ['分类', '计数概念']
            }
        },
        '社会情感发展': {
            'description': '社交技能、情感表达、自我意识',
            'age_milestones': {
                '0-1': ['社会性微笑'],
                '2-3': ['认识照顾者'],
                '4-6': ['陌生人焦虑'],
                '7-12': ['分离焦虑', '社会参照'],
                '13-24': ['平行游戏', '自我意识'],
                '25-36': ['合作游戏', '情感调节']
            }
        },
        '自理能力': {
            'description': '日常生活自理技能',
            'age_milestones': {
                '0-1': ['吸吮反射'],
                '2-3': ['配合喂食'],
                '4-6': ['自己拿奶瓶'],
                '7-12': ['用杯子喝水', '手抓食物'],
                '13-24': ['使用勺子', '脱简单衣物'],
                '25-36': ['如厕训练', '穿简单衣物']
            }
        }
    }
    
    print("🧠 婴幼儿发展领域分析:")
    for domain, info in development_domains.items():
        print(f"\n{domain}:")
        print(f"   描述: {info['description']}")
        print("   发展里程碑:")
        for age_range, milestones in info['age_milestones'].items():
            print(f"     {age_range}月龄: {', '.join(milestones)}")
    
    # 数据集构建可行性分析
    print("\n" + "=" * 60)
    print("📊 数据集构建可行性分析")
    print("=" * 60)
    
    total_size_kb = sum(sizes)
    estimated_text_per_kb = 500  # 估算每KB包含的中文字符数
    estimated_total_text = total_size_kb * estimated_text_per_kb
    
    print(f"📈 数据量估算:")
    print(f"   文件总数: {len(files)}")
    print(f"   总文件大小: {total_size_kb} KB")
    print(f"   估算文本量: {estimated_total_text:,} 字符")
    print(f"   平均每月龄: {estimated_total_text // len(set(age_groups)):,} 字符")
    
    print(f"\n📅 时间覆盖:")
    age_numbers = []
    for age in age_groups:
        if '-' in age:
            start, end = age.split('-')
            age_numbers.extend(range(int(start), int(end) + 1))
        else:
            age_numbers.append(int(age))
    
    age_coverage = sorted(set(age_numbers))
    print(f"   覆盖月龄: {min(age_coverage)}-{max(age_coverage)}月")
    print(f"   覆盖范围: {(max(age_coverage) - min(age_coverage) + 1) / 36 * 100:.1f}% (0-3岁)")
    
    missing_ages = []
    for i in range(min(age_coverage), max(age_coverage) + 1):
        if i not in age_coverage:
            missing_ages.append(i)
    
    if missing_ages:
        print(f"   缺失月龄: {missing_ages}")
    else:
        print(f"   ✅ 月龄覆盖完整")
    
    print(f"\n🎯 数据集应用场景:")
    applications = [
        "婴幼儿发展里程碑知识库",
        "育儿指导问答系统",
        "发展评估智能助手",
        "早期干预建议生成",
        "家长教育内容生成",
        "儿科医生辅助诊断",
        "特殊需求儿童评估",
        "教育机构课程设计"
    ]
    
    for i, app in enumerate(applications, 1):
        print(f"   {i}. {app}")
    
    print(f"\n💡 数据集构建建议:")
    suggestions = [
        "结构化数据提取: 将评估项目按发展领域分类",
        "标准化格式: 统一评估标准和评分方式", 
        "多模态扩展: 添加图片、视频等辅助材料",
        "案例数据: 收集真实的评估案例和结果",
        "专家标注: 邀请儿童发展专家进行质量审核",
        "持续更新: 根据最新研究更新评估标准",
        "本土化适配: 结合中国儿童发展特点",
        "数据增强: 通过同义词替换等方法扩充数据"
    ]
    
    for i, suggestion in enumerate(suggestions, 1):
        print(f"   {i}. {suggestion}")
    
    print(f"\n⚠️  潜在挑战:")
    challenges = [
        "文档格式老旧，文本提取困难",
        "专业术语较多，需要领域知识",
        "评估标准可能需要更新",
        "数据量相对较小，需要补充",
        "缺乏实际评估案例数据",
        "需要专业人员验证准确性"
    ]
    
    for i, challenge in enumerate(challenges, 1):
        print(f"   {i}. {challenge}")
    
    # 生成数据集元数据
    metadata = {
        'dataset_name': '婴幼儿综合能力发展评估量表数据集',
        'description': '基于广东省残疾人康复中心的0-34月龄婴幼儿综合能力发展评估量表',
        'total_files': len(files),
        'age_coverage': f"{min(age_coverage)}-{max(age_coverage)}月龄",
        'development_domains': list(development_domains.keys()),
        'estimated_text_size': estimated_total_text,
        'file_format': '.doc (Microsoft Word 97-2003)',
        'language': '中文',
        'source': '广东省残疾人康复中心',
        'applications': applications,
        'challenges': challenges,
        'files': file_info
    }
    
    # 保存分析结果
    with open('assessment_metadata.json', 'w', encoding='utf-8') as f:
        json.dump(metadata, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 分析结果已保存到: assessment_metadata.json")
    
    return metadata

def main():
    """主函数"""
    print("🚀 开始手动分析婴幼儿综合能力发展评估量表")
    print("由于文档格式限制，采用基于文件信息和专业知识的分析方法")
    print()
    
    metadata = analyze_assessment_forms_manually()
    
    print(f"\n✅ 分析完成！")
    print(f"📊 数据集包含 {metadata['total_files']} 个评估表")
    print(f"📅 覆盖 {metadata['age_coverage']} 的发展评估")
    print(f"🧠 涵盖 {len(metadata['development_domains'])} 个发展领域")

if __name__ == "__main__":
    main()
