#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试.doc文件读取
"""

import re
import os

def extract_text_from_doc_simple(file_path):
    """简单的.doc文件文本提取（基于字符串搜索）"""
    try:
        with open(file_path, 'rb') as f:
            content = f.read()
        
        print(f"文件大小: {len(content)} 字节")
        
        # 尝试多种编码
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'latin1']
        
        for encoding in encodings:
            try:
                text = content.decode(encoding, errors='ignore')
                print(f"尝试编码: {encoding}")
                
                # 使用正则表达式提取中文文本
                chinese_text = re.findall(r'[\u4e00-\u9fff]+', text)
                
                # 过滤掉太短的文本片段
                meaningful_text = [t for t in chinese_text if len(t) > 1]
                
                if meaningful_text:
                    print(f"找到 {len(meaningful_text)} 个中文文本片段")
                    print("前10个片段:")
                    for i, t in enumerate(meaningful_text[:10]):
                        print(f"  {i+1}: {t}")
                    
                    return '\n'.join(meaningful_text)
                    
            except Exception as e:
                print(f"编码 {encoding} 失败: {e}")
                continue
        
        print("未找到有效的中文文本")
        return None
        
    except Exception as e:
        print(f"读取文件时出错: {e}")
        return None

def test_file_reading():
    """测试文件读取"""
    test_file = "assessment_forms/01_婴幼儿综合能力发展评估量表(0-1月龄).doc"
    
    if not os.path.exists(test_file):
        print(f"测试文件不存在: {test_file}")
        return
    
    print(f"测试读取文件: {test_file}")
    print("=" * 50)
    
    text = extract_text_from_doc_simple(test_file)
    
    if text:
        print("\n提取的文本:")
        print("=" * 50)
        print(text[:1000])  # 显示前1000个字符
        if len(text) > 1000:
            print("...")
        print(f"\n总文本长度: {len(text)} 字符")
    else:
        print("未能提取到文本")

if __name__ == "__main__":
    test_file_reading()
