#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复.doc文件编码问题的解析器
专门处理中文内容的正确提取
"""

import os
import glob
import json
import re
import subprocess
from collections import defaultdict

def try_libreoffice_conversion(doc_path):
    """尝试使用LibreOffice将.doc转换为纯文本"""
    try:
        # 检查LibreOffice是否可用
        result = subprocess.run(['libreoffice', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode != 0:
            return None, "LibreOffice不可用"
        
        # 创建临时输出目录
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            # 转换为文本格式
            cmd = [
                'libreoffice', '--headless', '--convert-to', 'txt:Text',
                '--outdir', temp_dir, doc_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                # 查找生成的txt文件
                txt_files = glob.glob(os.path.join(temp_dir, '*.txt'))
                if txt_files:
                    with open(txt_files[0], 'r', encoding='utf-8') as f:
                        content = f.read()
                    return content, "LibreOffice转换成功"
            
            return None, f"LibreOffice转换失败: {result.stderr}"
            
    except subprocess.TimeoutExpired:
        return None, "LibreOffice转换超时"
    except Exception as e:
        return None, f"LibreOffice转换出错: {e}"

def try_antiword_conversion(doc_path):
    """尝试使用antiword提取文本"""
    try:
        result = subprocess.run(['antiword', '-t', doc_path], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            return result.stdout, "antiword转换成功"
        else:
            return None, f"antiword转换失败: {result.stderr}"
    except subprocess.TimeoutExpired:
        return None, "antiword转换超时"
    except FileNotFoundError:
        return None, "antiword未安装"
    except Exception as e:
        return None, f"antiword转换出错: {e}"

def try_catdoc_conversion(doc_path):
    """尝试使用catdoc提取文本"""
    try:
        result = subprocess.run(['catdoc', '-a', doc_path], 
                              capture_output=True, text=True, timeout=30)
        if result.returncode == 0:
            return result.stdout, "catdoc转换成功"
        else:
            return None, f"catdoc转换失败: {result.stderr}"
    except subprocess.TimeoutExpired:
        return None, "catdoc转换超时"
    except FileNotFoundError:
        return None, "catdoc未安装"
    except Exception as e:
        return None, f"catdoc转换出错: {e}"

def extract_chinese_from_binary(file_path):
    """从二进制文件中智能提取中文文本"""
    try:
        with open(file_path, 'rb') as f:
            content = f.read()
        
        # 尝试查找UTF-16编码的中文文本
        # Word文档中的文本通常以UTF-16LE格式存储
        try:
            # 查找连续的UTF-16LE编码文本
            text_utf16le = content.decode('utf-16le', errors='ignore')
            
            # 提取有意义的中文文本段落
            chinese_segments = []
            current_segment = ""
            
            for char in text_utf16le:
                if ('\u4e00' <= char <= '\u9fff' or  # 中文字符
                    char in '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ' or
                    char in '，。！？：；（）【】""''、～·…—'):
                    current_segment += char
                elif char in ' \t\n\r':
                    if current_segment.strip():
                        # 检查是否包含中文
                        if any('\u4e00' <= c <= '\u9fff' for c in current_segment):
                            chinese_segments.append(current_segment.strip())
                    current_segment = ""
                else:
                    # 遇到其他字符，结束当前段落
                    if current_segment.strip() and len(current_segment) > 3:
                        if any('\u4e00' <= c <= '\u9fff' for c in current_segment):
                            chinese_segments.append(current_segment.strip())
                    current_segment = ""
            
            # 最后一个段落
            if current_segment.strip() and len(current_segment) > 3:
                if any('\u4e00' <= c <= '\u9fff' for c in current_segment):
                    chinese_segments.append(current_segment.strip())
            
            if chinese_segments:
                # 合并相似的段落
                cleaned_segments = []
                for segment in chinese_segments:
                    if len(segment) > 5 and not any(segment in existing for existing in cleaned_segments):
                        cleaned_segments.append(segment)
                
                return '\n'.join(cleaned_segments), "二进制UTF-16LE提取"
        
        except Exception as e:
            pass
        
        # 尝试其他编码
        for encoding in ['gbk', 'gb2312', 'big5']:
            try:
                text = content.decode(encoding, errors='ignore')
                chinese_chars = [c for c in text if '\u4e00' <= c <= '\u9fff']
                if len(chinese_chars) > 100:  # 至少100个中文字符
                    # 提取包含中文的行
                    lines = text.split('\n')
                    chinese_lines = []
                    for line in lines:
                        line = line.strip()
                        if len(line) > 3 and any('\u4e00' <= c <= '\u9fff' for c in line):
                            chinese_lines.append(line)
                    
                    if chinese_lines:
                        return '\n'.join(chinese_lines), f"二进制{encoding}提取"
            except:
                continue
        
        return None, "二进制提取失败"
        
    except Exception as e:
        return None, f"二进制提取出错: {e}"

def extract_text_from_doc(doc_path):
    """使用多种方法提取.doc文件的文本"""
    filename = os.path.basename(doc_path)
    print(f"正在处理: {filename}")
    
    # 按优先级尝试不同的方法
    methods = [
        ("LibreOffice", try_libreoffice_conversion),
        ("antiword", try_antiword_conversion),
        ("catdoc", try_catdoc_conversion),
        ("二进制智能提取", extract_chinese_from_binary)
    ]
    
    for method_name, method_func in methods:
        print(f"  尝试方法: {method_name}")
        try:
            text, status = method_func(doc_path)
            if text and len(text.strip()) > 50:
                # 检查中文字符比例
                chinese_count = sum(1 for c in text if '\u4e00' <= c <= '\u9fff')
                total_count = len(text)
                chinese_ratio = chinese_count / total_count if total_count > 0 else 0
                
                if chinese_ratio > 0.1:  # 至少10%的中文字符
                    print(f"  ✅ {method_name} 成功")
                    print(f"     文本长度: {len(text)} 字符")
                    print(f"     中文字符: {chinese_count} 个 ({chinese_ratio:.1%})")
                    print(f"     状态: {status}")
                    
                    # 显示前100个字符作为预览
                    preview = text[:100].replace('\n', ' ').replace('\r', ' ')
                    print(f"     预览: {preview}...")
                    
                    return text, method_name, chinese_ratio
                else:
                    print(f"  ❌ {method_name} 中文字符比例太低 ({chinese_ratio:.1%})")
            else:
                print(f"  ❌ {method_name} 提取内容太少或为空")
                print(f"     状态: {status}")
        except Exception as e:
            print(f"  ❌ {method_name} 出错: {e}")
    
    print(f"  ❌ 所有方法都失败")
    return None, None, 0

def parse_assessment_content(text, filename):
    """解析评估表内容"""
    if not text:
        return None
    
    # 提取月龄信息
    age_match = re.search(r'(\d+(?:-\d+)?)月龄', filename)
    age_group = age_match.group(1) if age_match else "未知"
    
    # 按行分割并清理
    lines = [line.strip() for line in text.split('\n') if line.strip()]
    
    # 识别评估项目
    assessment_items = []
    current_section = None
    
    for line in lines:
        # 识别章节标题
        section_keywords = ['身体运动', '探索与操作', '听觉言语', '社会适应', '大运动', '精细运动']
        if any(keyword in line for keyword in section_keywords):
            current_section = line
            continue
        
        # 识别评估题目
        if ('宝宝' in line and ('能' in line or '不能' in line)) or \
           (re.match(r'^\d+', line) and '宝宝' in line):
            assessment_items.append({
                'section': current_section,
                'content': line,
                'has_ability_rating': '能' in line and '不能' in line
            })
    
    # 识别发展领域
    domains = []
    domain_patterns = {
        '大运动发展': ['大运动', '身体运动', '控制', '抬头', '翻身', '坐', '爬', '站', '走'],
        '精细运动发展': ['精细运动', '探索', '操作', '抓握', '手指', '玩具'],
        '语言发展': ['听觉', '言语', '语言', '声音', '说话', '叫'],
        '认知发展': ['认知', '思维', '记忆', '注意'],
        '社会情感发展': ['社会', '适应', '微笑', '哭', '情感'],
        '自理能力': ['自理', '吃', '喝', '穿']
    }
    
    for domain, keywords in domain_patterns.items():
        if any(keyword in text for keyword in keywords):
            domains.append(domain)
    
    return {
        'filename': filename,
        'age_group': age_group,
        'text_length': len(text),
        'clean_text': text,  # 保存清理后的完整文本
        'assessment_items': assessment_items,
        'domains_identified': domains,
        'statistics': {
            'total_lines': len(lines),
            'assessment_items_count': len(assessment_items),
            'baby_mentions': text.count('宝宝'),
            'chinese_char_count': sum(1 for c in text if '\u4e00' <= c <= '\u9fff'),
            'total_char_count': len(text)
        }
    }

def create_fixed_dataset():
    """创建修复编码问题的数据集"""
    assessment_dir = "assessment_forms"
    doc_files = glob.glob(os.path.join(assessment_dir, "*.doc"))
    doc_files.sort()
    
    print("🔧 修复.doc文件编码问题")
    print("=" * 60)
    
    dataset = {
        'metadata': {
            'dataset_name': '婴幼儿综合能力发展评估量表数据集（编码修复版）',
            'description': '使用专业工具正确提取中文内容的评估表数据集',
            'source': '广东省残疾人康复中心',
            'total_files': len(doc_files),
            'extraction_date': '2025-01-25',
            'format_version': '4.0'
        },
        'documents': [],
        'extraction_summary': {
            'successful': 0,
            'failed': 0,
            'total_text_length': 0,
            'total_assessment_items': 0,
            'methods_used': defaultdict(int),
            'chinese_ratios': []
        }
    }
    
    for i, doc_path in enumerate(doc_files, 1):
        filename = os.path.basename(doc_path)
        print(f"\n[{i}/{len(doc_files)}] 处理文件: {filename}")
        print("-" * 40)
        
        # 提取文本
        text, method, chinese_ratio = extract_text_from_doc(doc_path)
        
        if text and chinese_ratio > 0.1:
            # 解析内容
            analysis = parse_assessment_content(text, filename)
            
            if analysis:
                analysis['extraction_method'] = method
                analysis['chinese_ratio'] = chinese_ratio
                
                dataset['documents'].append(analysis)
                dataset['extraction_summary']['successful'] += 1
                dataset['extraction_summary']['methods_used'][method] += 1
                dataset['extraction_summary']['total_text_length'] += len(text)
                dataset['extraction_summary']['total_assessment_items'] += len(analysis['assessment_items'])
                dataset['extraction_summary']['chinese_ratios'].append(chinese_ratio)
                
                print(f"  ✅ 解析成功")
                print(f"     评估项目: {len(analysis['assessment_items'])} 个")
                print(f"     识别领域: {len(analysis['domains_identified'])} 个")
            else:
                dataset['extraction_summary']['failed'] += 1
                print(f"  ❌ 内容解析失败")
        else:
            dataset['extraction_summary']['failed'] += 1
            print(f"  ❌ 文本提取失败")
    
    # 计算统计信息
    if dataset['extraction_summary']['chinese_ratios']:
        dataset['extraction_summary']['average_chinese_ratio'] = sum(dataset['extraction_summary']['chinese_ratios']) / len(dataset['extraction_summary']['chinese_ratios'])
    
    # 保存结果
    output_file = 'fixed_assessment_dataset.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(dataset, f, ensure_ascii=False, indent=2)
    
    # 生成报告
    print("\n" + "=" * 60)
    print("📊 编码修复结果总结")
    print("=" * 60)
    print(f"✅ 成功解析: {dataset['extraction_summary']['successful']} 个文件")
    print(f"❌ 解析失败: {dataset['extraction_summary']['failed']} 个文件")
    print(f"📈 成功率: {dataset['extraction_summary']['successful']/len(doc_files)*100:.1f}%")
    print(f"📝 总文本长度: {dataset['extraction_summary']['total_text_length']:,} 字符")
    print(f"🎯 总评估项目: {dataset['extraction_summary']['total_assessment_items']} 个")
    
    if dataset['extraction_summary']['chinese_ratios']:
        print(f"🇨🇳 平均中文比例: {dataset['extraction_summary']['average_chinese_ratio']:.1%}")
    
    print(f"\n🔧 使用的提取方法:")
    for method, count in dataset['extraction_summary']['methods_used'].items():
        print(f"  {method}: {count} 个文件")
    
    print(f"\n📄 生成文件: {output_file}")
    
    return dataset

def main():
    """主函数"""
    print("🚀 启动.doc文件编码修复工具")
    print("专门解决中文内容显示为乱码的问题")
    print()
    
    # 检查可用工具
    print("🔍 检查可用的转换工具:")
    tools = ['libreoffice', 'antiword', 'catdoc']
    available_tools = []
    
    for tool in tools:
        try:
            result = subprocess.run([tool, '--version'], 
                                  capture_output=True, timeout=5)
            if result.returncode == 0:
                available_tools.append(tool)
                print(f"  ✅ {tool}: 可用")
            else:
                print(f"  ❌ {tool}: 不可用")
        except:
            print(f"  ❌ {tool}: 未安装")
    
    if not available_tools:
        print("\n⚠️  警告: 没有找到专业的文档转换工具")
        print("   建议安装: brew install antiword 或 apt-get install antiword")
        print("   将使用智能二进制提取方法")
    
    print()
    
    # 开始处理
    dataset = create_fixed_dataset()
    
    print(f"\n✅ 编码修复完成！")
    if dataset['extraction_summary']['successful'] > 0:
        print(f"🎯 成功提取了 {dataset['extraction_summary']['successful']} 个评估表的正确中文内容")

if __name__ == "__main__":
    main()
