# 育幼健康监测数据集构建流程图

本文档包含了用于论文发表的数据集构建流程图的Mermaid源代码。

## 图1：详细流程图（适合技术文档）

```mermaid
graph TD
    A[📚 权威数据源] --> B[🔍 数据预处理]
    B --> C[🎯 智能采样]
    C --> D[🤖 LLM增强生成]
    D --> E[🔧 质量控制]
    E --> F[📊 数据集成]
    F --> G[✅ 最终数据集]
    
    %% 数据源详细分类
    A1[WHO发育标准] --> A
    A2[中国卫健委指南] --> A
    A3[专业医学文献] --> A
    A4[临床评估表] --> A
    A5[权威教材] --> A
    
    %% 预处理步骤
    B --> B1[格式标准化]
    B --> B2[数据清洗]
    B --> B3[结构化处理]
    
    %% 智能采样策略
    C --> C1[年龄段平衡采样]
    C --> C2[功能模块覆盖]
    C --> C3[场景多样性保证]
    
    %% LLM增强详细流程
    D --> D1[Prompt工程设计]
    D --> D2[Qwen API调用]
    D --> D3[多轮对话生成]
    D --> D4[专业知识注入]
    
    %% 质量控制机制
    E --> E1[自动化检查]
    E --> E2[专家审核]
    E --> E3[交叉验证]
    E --> E4[安全性检查]
    
    %% 数据集成
    F --> F1[格式统一]
    F --> F2[元数据标注]
    F --> F3[版本控制]
    
    %% 最终输出
    G --> G1[JSONL格式]
    G --> G2[Alpaca格式]
    G --> G3[ChatML格式]
    
    %% 10个功能模块
    H[🗣️ 语音构音监测] --> D
    I[🏃 家庭场景运动监测] --> D
    J[🍎 饮食多样性监测] --> D
    K[📏 体格生长监测] --> D
    L[🗣️ 发音纠正指导] --> D
    M[🤸 粗大运动发展指导] --> D
    N[✋ 精细动作发展指导] --> D
    O[🍼 膳食营养指导] --> D
    P[😊 社会情绪发展指导] --> D
    Q[💬 语言发展指导] --> D
    
    %% 样式定义
    classDef sourceNode fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef processNode fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef llmNode fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef qualityNode fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef outputNode fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef moduleNode fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    
    class A,A1,A2,A3,A4,A5 sourceNode
    class B,B1,B2,B3,C,C1,C2,C3 processNode
    class D,D1,D2,D3,D4 llmNode
    class E,E1,E2,E3,E4 qualityNode
    class F,F1,F2,F3,G,G1,G2,G3 outputNode
    class H,I,J,K,L,M,N,O,P,Q moduleNode
```

## 图2：核心流程图（适合论文正文）

```mermaid
graph LR
    A[权威数据源<br/>📚 WHO标准<br/>🏥 卫健委指南<br/>📖 专业文献] --> B[数据预处理<br/>🔍 格式标准化<br/>🧹 数据清洗<br/>📋 结构化处理]
    
    B --> C[智能采样<br/>⚖️ 年龄段平衡<br/>🎯 功能覆盖<br/>🌈 场景多样性]
    
    C --> D[LLM增强生成<br/>🤖 Qwen API<br/>💡 Prompt工程<br/>🔄 多轮对话<br/>🧠 专业知识注入]
    
    D --> E[质量控制<br/>🔧 自动化检查<br/>👨‍⚕️ 专家审核<br/>✅ 交叉验证<br/>🛡️ 安全性检查]
    
    E --> F[数据集成<br/>📊 格式统一<br/>🏷️ 元数据标注<br/>📦 多格式输出]
    
    %% 10个功能模块输入到LLM增强
    G[10个功能模块<br/>🗣️ 语音构音监测<br/>🏃 运动监测<br/>🍎 饮食监测<br/>📏 生长监测<br/>🗣️ 发音纠正<br/>🤸 粗大运动<br/>✋ 精细动作<br/>🍼 膳食营养<br/>😊 情绪发展<br/>💬 语言发展] --> D
    
    %% 最终输出
    F --> H[高质量数据集<br/>📄 JSONL格式<br/>🦙 Alpaca格式<br/>💬 ChatML格式<br/>📈 5000+条记录]
    
    %% 样式定义
    classDef sourceStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000
    classDef processStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px,color:#000
    classDef llmStyle fill:#fff8e1,stroke:#f57c00,stroke-width:3px,color:#000
    classDef qualityStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px,color:#000
    classDef outputStyle fill:#fce4ec,stroke:#c2185b,stroke-width:3px,color:#000
    classDef moduleStyle fill:#f1f8e9,stroke:#689f38,stroke-width:2px,color:#000
    
    class A sourceStyle
    class B,C processStyle
    class D llmStyle
    class E qualityStyle
    class F,H outputStyle
    class G moduleStyle
```

## 图3：学术化分层架构图（适合方法论章节）

```mermaid
graph TD
    %% 第一层：数据源
    A[数据源层<br/>Data Source Layer]
    A1[WHO发育标准] --> A
    A2[中国卫健委指南] --> A
    A3[专业医学文献] --> A
    A4[临床评估工具] --> A
    
    %% 第二层：预处理
    A --> B[预处理层<br/>Preprocessing Layer]
    B1[数据清洗<br/>Data Cleaning] --> B
    B2[格式标准化<br/>Format Standardization] --> B
    B3[结构化处理<br/>Structuring] --> B
    
    %% 第三层：采样策略
    B --> C[采样策略层<br/>Sampling Strategy Layer]
    C1[年龄段平衡采样<br/>Age-balanced Sampling] --> C
    C2[功能模块覆盖<br/>Functional Coverage] --> C
    C3[场景多样性保证<br/>Scenario Diversity] --> C
    
    %% 第四层：LLM增强（核心）
    C --> D[LLM增强层<br/>LLM Enhancement Layer]
    D1[Prompt工程<br/>Prompt Engineering] --> D
    D2[Qwen API调用<br/>Qwen API Integration] --> D
    D3[多轮对话生成<br/>Multi-turn Generation] --> D
    D4[专业知识注入<br/>Domain Knowledge Injection] --> D
    
    %% 功能模块输入
    E[10个功能模块<br/>10 Functional Modules] --> D
    E1[监测类模块<br/>Monitoring Modules] --> E
    E2[指导类模块<br/>Guidance Modules] --> E
    
    %% 第五层：质量控制
    D --> F[质量控制层<br/>Quality Control Layer]
    F1[自动化检查<br/>Automated Validation] --> F
    F2[专家审核<br/>Expert Review] --> F
    F3[交叉验证<br/>Cross Validation] --> F
    F4[安全性检查<br/>Safety Check] --> F
    
    %% 第六层：数据集成
    F --> G[数据集成层<br/>Data Integration Layer]
    G1[格式统一<br/>Format Unification] --> G
    G2[元数据标注<br/>Metadata Annotation] --> G
    G3[版本控制<br/>Version Control] --> G
    
    %% 第七层：输出
    G --> H[输出层<br/>Output Layer]
    H1[JSONL格式<br/>JSONL Format] --> H
    H2[Alpaca格式<br/>Alpaca Format] --> H
    H3[ChatML格式<br/>ChatML Format] --> H
    
    %% 最终成果
    H --> I[高质量数据集<br/>High-Quality Dataset<br/>5000+ Records]
    
    %% 样式定义
    classDef layerStyle fill:#e8eaf6,stroke:#3f51b5,stroke-width:3px,font-weight:bold
    classDef sourceStyle fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef processStyle fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef llmStyle fill:#fff8e1,stroke:#f57c00,stroke-width:2px
    classDef qualityStyle fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef outputStyle fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef finalStyle fill:#ffebee,stroke:#d32f2f,stroke-width:3px,font-weight:bold
    
    class A,B,C,D,F,G,H layerStyle
    class A1,A2,A3,A4 sourceStyle
    class B1,B2,B3,C1,C2,C3 processStyle
    class D1,D2,D3,D4,E,E1,E2 llmStyle
    class F1,F2,F3,F4 qualityStyle
    class G1,G2,G3,H1,H2,H3 outputStyle
    class I finalStyle
```

## 使用说明

### 在论文中使用建议：

1. **图2（核心流程图）** - 适合放在论文的方法论章节，简洁明了地展示整个构建流程
2. **图3（分层架构图）** - 适合放在系统架构或技术实现章节，展示分层设计思想
3. **图1（详细流程图）** - 适合放在附录或技术文档中，提供完整的实现细节

### 图表特点：

- **专业性**：使用学术化的英文术语和分层架构
- **清晰性**：颜色编码区分不同类型的处理步骤
- **完整性**：涵盖从数据源到最终输出的完整流程
- **创新性**：突出LLM增强这一核心技术创新点

### 技术亮点：

1. **多源权威数据融合**：WHO标准、卫健委指南、专业文献
2. **智能采样策略**：年龄段平衡、功能覆盖、场景多样性
3. **LLM增强技术**：Prompt工程、多轮对话、专业知识注入
4. **严格质量控制**：自动化检查、专家审核、交叉验证
5. **多格式输出**：支持主流的AI训练数据格式
