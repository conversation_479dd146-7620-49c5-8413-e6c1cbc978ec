#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键转换脚本
自动安装依赖并将HTML转换为PPTX
"""

import subprocess
import sys
import os

def install_dependencies():
    """安装必要的依赖包"""
    print("🔧 正在安装必要的依赖包...")
    
    packages = [
        'python-pptx',
        'beautifulsoup4',
        'lxml'
    ]
    
    for package in packages:
        try:
            print(f"安装 {package}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError as e:
            print(f"❌ {package} 安装失败: {e}")
            return False
    
    print("✅ 所有依赖包安装完成!")
    return True

def run_conversion():
    """运行转换脚本"""
    print("\n🚀 开始转换HTML到PPTX...")
    
    try:
        # 导入转换模块
        from html_to_pptx_converter import main as convert_main
        convert_main()
        return True
    except ImportError as e:
        print(f"❌ 导入转换模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 转换过程出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🎯 HTML到PPTX转换工具")
    print("=" * 60)
    
    # 检查HTML文件是否存在
    html_file = "育幼健康监测数据集汇报.html"
    if not os.path.exists(html_file):
        print(f"❌ 错误: 找不到HTML文件 {html_file}")
        print("请确保HTML文件在当前目录中")
        return
    
    print(f"✅ 找到HTML文件: {html_file}")
    
    # 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败，无法继续")
        return
    
    # 运行转换
    if run_conversion():
        print("\n🎉 转换成功完成!")
        print("📁 输出文件: 育幼健康监测数据集汇报.pptx")
        print("\n💡 使用提示:")
        print("• 可以用Microsoft PowerPoint或WPS打开PPTX文件")
        print("• 建议在演示前检查字体和格式")
        print("• 可以根据需要调整幻灯片样式")
    else:
        print("\n❌ 转换失败")

if __name__ == "__main__":
    main()
