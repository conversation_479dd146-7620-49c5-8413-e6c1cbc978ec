<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>育幼健康监测-指导功能数据集汇报</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow-x: hidden;
        }
        
        .presentation {
            width: 100vw;
            height: 100vh;
            position: relative;
        }
        
        .slide {
            width: 100%;
            height: 100%;
            display: none;
            padding: 60px;
            position: relative;
            background: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .slide.active {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        
        .slide h1 {
            font-size: 3.5em;
            color: #2c3e50;
            margin-bottom: 30px;
            text-align: center;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .slide h2 {
            font-size: 2.5em;
            color: #34495e;
            margin-bottom: 40px;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
        }
        
        .slide h3 {
            font-size: 1.8em;
            color: #2980b9;
            margin-bottom: 20px;
        }
        
        .overview-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            width: 100%;
            max-width: 1200px;
        }
        
        .overview-item {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            padding: 25px;
            border-radius: 15px;
            color: white;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .overview-item:hover {
            transform: translateY(-5px);
        }
        
        .overview-item h4 {
            font-size: 1.4em;
            margin-bottom: 15px;
        }
        
        .overview-item p {
            font-size: 1.1em;
            line-height: 1.6;
        }
        
        .dataset-card {
            background: white;
            border-radius: 20px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-left: 5px solid #3498db;
            width: 100%;
            max-width: 1000px;
        }
        
        .dataset-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }
        
        .dataset-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5em;
            margin-right: 20px;
        }
        
        .dataset-title {
            font-size: 1.6em;
            color: #2c3e50;
            font-weight: bold;
        }
        
        .dataset-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 15px;
            margin: 20px 0;
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .stat-number {
            font-size: 1.8em;
            font-weight: bold;
            color: #3498db;
        }
        
        .stat-label {
            font-size: 0.9em;
            color: #7f8c8d;
            margin-top: 5px;
        }
        
        .prompt-box {
            background: #ecf0f1;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            border-left: 4px solid #e74c3c;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            line-height: 1.5;
        }
        
        .navigation {
            position: fixed;
            bottom: 30px;
            right: 30px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }
        
        .nav-btn {
            padding: 12px 20px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
        }
        
        .nav-btn:hover {
            background: #2980b9;
            transform: scale(1.05);
        }
        
        .slide-counter {
            position: fixed;
            bottom: 30px;
            left: 30px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 1em;
        }

        /* 数据格式样例样式 */
        .data-format-sample {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 15px;
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            max-height: 200px;
            overflow-y: auto;
            border-left: 4px solid #3498db;
            margin-top: 15px;
        }

        .data-format-sample pre {
            margin: 0;
            white-space: pre-wrap;
            word-wrap: break-word;
            color: #2c3e50;
            line-height: 1.4;
        }

        .data-format-sample::-webkit-scrollbar {
            width: 6px;
        }

        .data-format-sample::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .data-format-sample::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
        }

        .data-format-sample::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        
        .highlight {
            background: linear-gradient(120deg, #a8edea 0%, #fed6e3 100%);
            padding: 2px 8px;
            border-radius: 5px;
            font-weight: bold;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            width: 100%;
            max-width: 1200px;
        }
        
        .summary-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .summary-number {
            font-size: 3em;
            font-weight: bold;
            color: #e74c3c;
            margin-bottom: 10px;
        }
        
        .summary-label {
            font-size: 1.2em;
            color: #34495e;
        }
    </style>
</head>
<body>
    <div class="presentation">
        <!-- 封面页 -->
        <div class="slide active">
            <h1>育幼健康监测-指导功能数据集</h1>
            <div style="text-align: center; margin: 40px 0;">
                <div style="font-size: 1.5em; color: #7f8c8d; margin-bottom: 20px;">
                    支持10种核心功能的专业数据集构建
                </div>
                <div style="font-size: 1.2em; color: #95a5a6;">
                    基于权威医学标准 · AI增强生成 · 多模态融合
                </div>
            </div>
            <div class="overview-grid">
                <div class="overview-item">
                    <h4>🗣️ 语音构音监测</h4>
                    <p>基于52词汇标准化测试，1219条专业评估数据</p>
                </div>
                <div class="overview-item">
                    <h4>🏃 家庭场景运动监测</h4>
                    <p>CribHD多模态数据集，1499条场景描述</p>
                </div>
                <div class="overview-item">
                    <h4>🍎 饮食多样性监测</h4>
                    <p>571条营养指导对话，覆盖0-3岁全阶段</p>
                </div>
                <div class="overview-item">
                    <h4>📏 体格生长监测</h4>
                    <p>400条生长评估样本，多指标综合分析</p>
                </div>
            </div>
        </div>

        <!-- 第2页：语音构音监测 -->
        <div class="slide">
            <h2>🗣️ 语音构音监测数据集</h2>
            <div class="dataset-card">
                <div class="dataset-header">
                    <div class="dataset-icon">🎯</div>
                    <div class="dataset-title">专业语音评估与构音纠正</div>
                </div>
                <div class="dataset-stats">
                    <div class="stat-item">
                        <div class="stat-number">1,219</div>
                        <div class="stat-label">总记录数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">3.9MB</div>
                        <div class="stat-label">数据大小</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">52</div>
                        <div class="stat-label">标准测试词汇</div>
                    </div>
                </div>
                <h3>📊 数据来源与构成</h3>
                <ul style="font-size: 1.1em; line-height: 1.8; margin-left: 20px;">
                    <li><span class="highlight">标准化构音测试</span>：50条基于52词汇的专业评估</li>
                    <li><span class="highlight">通用对话训练</span>：169条多轮对话数据</li>
                    <li><span class="highlight">增强语音评估</span>：1000条LLM生成的专业案例</li>
                </ul>
                <div class="prompt-box">
                    <strong>核心Prompt示例：</strong><br>
                    "基于52个标准化测试词汇，对{年龄}个月{性别}婴儿进行构音评估。分析音素错误模式（替代/省略/歪曲/增加），提供专业治疗建议和家庭训练方案。"
                </div>
                <h3>📝 数据格式样例</h3>
                <div class="data-format-sample">
                    <pre>{
  "record_id": "ESA_20250725_0001",
  "child_info": {
    "age_months": 18,
    "gender": "女孩",
    "development_stage": "词汇爆发期"
  },
  "assessment_case": {
    "target_word": "绿",
    "target_phoneme": "l",
    "suspected_error_type": "替代",
    "parent_description": "孩子说'绿'时发音有问题...",
    "concern_level": "轻度关注"
  },
  "professional_assessment": "1. **问题判断**：正常发展...",
  "dialogue": "&lt;开始对话&gt;...&lt;结束对话&gt;",
  "metadata": {
    "generation_method": "llm_assisted_concurrent",
    "api_model": "qwen-turbo"
  }
}</pre>
                </div>
            </div>
        </div>

        <!-- 第3页：家庭场景运动监测 -->
        <div class="slide">
            <h2>🏃 家庭场景运动监测数据集</h2>
            <div class="dataset-card">
                <div class="dataset-header">
                    <div class="dataset-icon">👶</div>
                    <div class="dataset-title">CribHD多模态场景分析</div>
                </div>
                <div class="dataset-stats">
                    <div class="stat-item">
                        <div class="stat-number">1,499</div>
                        <div class="stat-label">场景样本</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">3</div>
                        <div class="stat-label">主要场景类型</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">5</div>
                        <div class="stat-label">评估维度</div>
                    </div>
                </div>
                <h3>🎯 场景分布与特色</h3>
                <ul style="font-size: 1.1em; line-height: 1.8; margin-left: 20px;">
                    <li><span class="highlight">毯子场景</span>：499个安全监测样本</li>
                    <li><span class="highlight">玩具场景</span>：1000个发育评估样本</li>
                    <li><span class="highlight">危机场景</span>：120个安全预警样本</li>
                </ul>
                <div class="prompt-box">
                    <strong>核心Prompt示例：</strong><br>
                    "基于婴儿床场景图像，进行5维度专业分析：1)基础场景描述 2)运动发育评估 3)安全风险评估 4)育儿指导建议 5)专业总结。使用国家卫健委发育里程碑标准。"
                </div>
                <h3>📝 数据格式样例</h3>
                <div class="data-format-sample">
                    <pre>{
  "timestamp": "2025-07-20T16:14:40.278459",
  "raw_description": [{
    "text": "## 1. 基础场景描述\n- 婴儿年龄估计：6-12个月\n- 婴儿当前姿势和动作状态：婴儿正在睡觉，侧卧在婴儿床上\n## 2. 运动发育评估\n- 大运动技能观察：婴儿能够侧卧并保持稳定\n## 3. 安全风险评估\n- 风险等级评定：低风险\n## 4. 育儿指导建议\n- 针对当前发育阶段的活动建议：继续鼓励婴儿进行翻身练习\n## 5. 专业总结\n- 整体发育状况评价：该婴儿的发育状况正常"
  }],
  "estimated_age_months": 6,
  "safety_assessment": {
    "risks_detected": [],
    "safe_items": [],
    "recommendations": []
  },
  "development_level": "正常",
  "scene_type": "crib"
}</pre>
                </div>
            </div>
        </div>

        <!-- 第4页：饮食多样性监测 -->
        <div class="slide">
            <h2>🍎 饮食多样性监测数据集</h2>
            <div class="dataset-card">
                <div class="dataset-header">
                    <div class="dataset-icon">🥄</div>
                    <div class="dataset-title">营养指导与膳食评估</div>
                </div>
                <div class="dataset-stats">
                    <div class="stat-item">
                        <div class="stat-number">571</div>
                        <div class="stat-label">对话数量</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">6</div>
                        <div class="stat-label">对话类型</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">24</div>
                        <div class="stat-label">国家覆盖</div>
                    </div>
                </div>
                <h3>📈 年龄段与类型分布</h3>
                <ul style="font-size: 1.1em; line-height: 1.8; margin-left: 20px;">
                    <li><span class="highlight">12-24个月</span>：437条（76.5%）幼儿期过渡</li>
                    <li><span class="highlight">营养分析</span>：101条专业成分分析</li>
                    <li><span class="highlight">喂养指导</span>：100条实用喂养建议</li>
                    <li><span class="highlight">质地建议</span>：104条食物质地指导</li>
                </ul>
                <div class="prompt-box">
                    <strong>核心Prompt示例：</strong><br>
                    "基于《3岁以下婴幼儿健康养育照护指南》，为{年龄段}婴儿提供{食物类型}的营养分析、分量控制和安全建议。包含能量转换和食物类别覆盖分析。"
                </div>
                <h3>📝 数据格式样例</h3>
                <div class="data-format-sample">
                    <pre>{
  "instruction": "我的12～24月龄宝宝，一天的饮食应该怎么安排？",
  "output": "根据国家卫健委《3岁以下婴幼儿健康养育照护指南》...\n### 一、喂养频次与时间安排\n- **每日进食次数**：5次\n- **正餐3次**：早、中、晚\n### 二、每餐分量标准\n- **每餐建议分量**：3/4碗到1整碗（1碗为250ml）\n### 三、食物质地要求\n- 食物应**软烂、易咀嚼吞咽**...",
  "metadata": {
    "type": "feeding_guidance",
    "age_group": "12-24m",
    "stage": "幼儿期过渡",
    "country": "CRI",
    "generation_method": "enhanced_qwen_api_with_portion_conversion",
    "based_on_official_guidelines": true,
    "food_categories_covered": 4
  }
}</pre>
                </div>
            </div>
        </div>

        <!-- 第5页：体格生长监测 -->
        <div class="slide">
            <h2>📏 体格生长监测数据集</h2>
            <div class="dataset-card">
                <div class="dataset-header">
                    <div class="dataset-icon">📊</div>
                    <div class="dataset-title">多指标综合生长评估</div>
                </div>
                <div class="dataset-stats">
                    <div class="stat-item">
                        <div class="stat-number">400</div>
                        <div class="stat-label">主要样本数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">27%</div>
                        <div class="stat-label">包含头围数据</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">4</div>
                        <div class="stat-label">关键指标</div>
                    </div>
                </div>
                <h3>🎯 指标分布与特色</h3>
                <ul style="font-size: 1.1em; line-height: 1.8; margin-left: 20px;">
                    <li><span class="highlight">头围专项</span>：96条（24%）脑发育监测</li>
                    <li><span class="highlight">综合评估</span>：90条体重+身高+BMI</li>
                    <li><span class="highlight">体重评估</span>：73条营养状态分析</li>
                    <li><span class="highlight">身高评估</span>：64条线性生长评估</li>
                </ul>
                <div class="prompt-box">
                    <strong>核心Prompt示例：</strong><br>
                    "基于中国卫健委《7岁以下儿童生长发育标准》和WHO标准，评估{年龄}个月{性别}婴儿：体重{weight}kg、身高{height}cm、头围{head}cm。提供专业医学评估和指导建议。"
                </div>
                <h3>📝 数据格式样例</h3>
                <div class="data-format-sample">
                    <pre>{
  "instruction": "2岁3月男童体重12.2kg、身高88.6cm、头围47.8cm，整体生长发育是否在正常范围？",
  "input": "",
  "output": "1. 体重评估：该男童体重12.2kg，处于2岁男童体重正常范围（WHO标准，约10.2–14.3kg）的中上水平...\n\n2. 身高评估：身高88.6cm，接近2岁男童平均身高（约87–89cm），位于第50–75百分位...\n\n3. 头围评估：头围47.8cm，处于2岁男童正常范围（约46.5–48.5cm）。头围反映颅骨及脑容量发育...\n\n4. BMI评估：BMI为15.5，在同龄男童中属正常范围...\n\n5. 综合结论：该男童体重、身高、头围和BMI均在正常范围...\n\n6. 指导建议：继续保持均衡饮食，每日摄入奶类400–500ml..."
}</pre>
                </div>
            </div>
        </div>

        <!-- 第6页：发音纠正指导 -->
        <div class="slide">
            <h2>🗣️ 发音纠正指导数据集</h2>
            <div class="dataset-card">
                <div class="dataset-header">
                    <div class="dataset-icon">🎤</div>
                    <div class="dataset-title">五步发音纠正法</div>
                </div>
                <div class="dataset-stats">
                    <div class="stat-item">
                        <div class="stat-number">650+</div>
                        <div class="stat-label">总记录数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">15MB</div>
                        <div class="stat-label">数据大小</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">5</div>
                        <div class="stat-label">纠正步骤</div>
                    </div>
                </div>
                <h3>📚 数据来源与方法</h3>
                <ul style="font-size: 1.1em; line-height: 1.8; margin-left: 20px;">
                    <li><span class="highlight">Qwen生成案例</span>：400条基于455个错误模式</li>
                    <li><span class="highlight">真实收集数据</span>：60条权威医疗机构数据</li>
                    <li><span class="highlight">综合数据集</span>：46条最完整版本</li>
                </ul>
                <div class="prompt-box">
                    <strong>五步纠正法Prompt：</strong><br>
                    "采用五步发音纠正法：1)说出单词 2)判断是否认识 3)联想相似音词 4)选择最接近真实词汇 5)检查语境合理性。为{年龄}个月婴儿的{错误发音}提供分步矫正指导。"
                </div>
                <h3>📝 数据格式样例</h3>
                <div class="data-format-sample">
                    <pre>{
  "case_id": "PC_001",
  "child_age_months": 24,
  "mispronunciation": "dāi dāi",
  "intended_word": "奶奶",
  "correction_steps": {
    "step1_say_word": "孩子说：'dāi dāi'",
    "step2_recognition": "家长确认孩子想表达'奶奶'",
    "step3_similar_sounds": "联想：'dāi'接近'nāi'音",
    "step4_closest_match": "引导发音：'nāi nāi'",
    "step5_context_check": "在看到奶奶时练习正确发音"
  },
  "professional_guidance": "2岁儿童'n'音发育尚未完全成熟...",
  "practice_suggestions": [
    "每日练习5-10分钟",
    "使用镜子观察口型"
  ]
}</pre>
                </div>
            </div>
        </div>

        <!-- 第7页：粗大运动发展指导 -->
        <div class="slide">
            <h2>🤸 粗大运动发展指导数据集</h2>
            <div class="dataset-card">
                <div class="dataset-header">
                    <div class="dataset-icon">🏃</div>
                    <div class="dataset-title">0-36个月运动里程碑</div>
                </div>
                <div class="dataset-stats">
                    <div class="stat-item">
                        <div class="stat-number">4,586</div>
                        <div class="stat-label">总条目数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">614</div>
                        <div class="stat-label">主要数据集</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">17</div>
                        <div class="stat-label">数据集数量</div>
                    </div>
                </div>
                <h3>🎯 数据集构成</h3>
                <ul style="font-size: 1.1em; line-height: 1.8; margin-left: 20px;">
                    <li><span class="highlight">权威指导标准</span>：104条Excel指导文件</li>
                    <li><span class="highlight">医学文献提取</span>：186条专业医学知识</li>
                    <li><span class="highlight">LLM增强</span>：26条AI生成专业问答</li>
                    <li><span class="highlight">多源整合</span>：758条最全面版本</li>
                </ul>
                <div class="prompt-box">
                    <strong>核心Prompt示例：</strong><br>
                    "基于0-36个月粗大运动发育里程碑，为{年龄}个月婴儿提供发育特点、观察要点、发育意义和个体差异指导。如有异常请建议咨询专业医生。"
                </div>
                <h3>📝 数据格式样例</h3>
                <div class="data-format-sample">
                    <pre>{
  "instruction": "我的宝宝27个月，需要支撑才能坐，应该怎么办？",
  "output": "针对坐位发育的指导建议：\n\n• 加强核心肌群训练\n• 逐渐减少支撑\n• 提供适当的坐位支撑\n\n安全提醒：\n• 确保练习环境安全\n• 避免强迫宝宝做不愿意的动作\n• 如有异常情况及时停止并咨询医生",
  "age_group": "25-36月",
  "interaction_type": "指导",
  "source": "base_generated",
  "category": "guidance"
}</pre>
                </div>
            </div>
        </div>

        <!-- 第8页：精细动作发展指导 -->
        <div class="slide">
            <h2>✋ 精细动作发展指导数据集</h2>
            <div class="dataset-card">
                <div class="dataset-header">
                    <div class="dataset-icon">🤏</div>
                    <div class="dataset-title">手眼协调与精细技能</div>
                </div>
                <div class="dataset-stats">
                    <div class="stat-item">
                        <div class="stat-number">827</div>
                        <div class="stat-label">高质量数据</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">300</div>
                        <div class="stat-label">行为分析样本</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">4</div>
                        <div class="stat-label">结构化维度</div>
                    </div>
                </div>
                <h3>🎯 数据集特色</h3>
                <ul style="font-size: 1.1em; line-height: 1.8; margin-left: 20px;">
                    <li><span class="highlight">行为分析</span>：300条最高质量分析+指导</li>
                    <li><span class="highlight">发育里程碑</span>：200条0-36个月标准</li>
                    <li><span class="highlight">家长教育</span>：110条通俗易懂指导</li>
                    <li><span class="highlight">专业培训</span>：67条医生康复师参考</li>
                </ul>
                <div class="prompt-box">
                    <strong>四维度分析Prompt：</strong><br>
                    "对{年龄}个月婴儿{行为描述}进行四维度分析：1)行为分析-解释发育意义 2)发育评估-判断正常水平 3)指导建议-具体训练方法 4)观察要点-后续关注重点。"
                </div>
                <h3>📝 数据格式样例</h3>
                <div class="data-format-sample">
                    <pre>{
  "question": "宝宝已经满一周岁了，他现在很喜欢尝试着自己用杯子喝水，但经常会洒出来。",
  "answer": "**行为分析**：这是非常自然且积极的行为迹象，说明您的孩子正在学习如何独立进食饮水，并且对模仿大人产生了兴趣。\n**发育评估**：一岁的孩子开始展现出更多自主性，想要尝试自己做事是很正常的成长过程中的一步。\n**指导建议**：为避免太多水分溢出造成浪费或弄湿衣服，您可以先给宝宝准备一些专门设计给儿童使用的带盖水杯或者吸管杯...\n**观察要点**：除了关注孩子喝水时的表现外，还可以留意他们在其他日常生活活动中是否有类似的学习模仿行为出现...",
  "category": "behavior_analysis",
  "age_group": "general",
  "difficulty": "intermediate",
  "generated_by": "qwen_api_batch"
}</pre>
                </div>
            </div>
        </div>

        <!-- 第9页：社会情绪发展指导 -->
        <div class="slide">
            <h2>😊 社会情绪发展指导数据集</h2>
            <div class="dataset-card">
                <div class="dataset-header">
                    <div class="dataset-icon">💝</div>
                    <div class="dataset-title">PEC理论框架与ASQ:SE-2标准</div>
                </div>
                <div class="dataset-stats">
                    <div class="stat-item">
                        <div class="stat-number">4,041</div>
                        <div class="stat-label">总样本数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">6.29MB</div>
                        <div class="stat-label">数据大小</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">6</div>
                        <div class="stat-label">年龄阶段</div>
                    </div>
                </div>
                <h3>🎯 数据集构成</h3>
                <ul style="font-size: 1.1em; line-height: 1.8; margin-left: 20px;">
                    <li><span class="highlight">结构化场景</span>：2641个样本，6个数据集</li>
                    <li><span class="highlight">Qwen增强</span>：200个高质量情绪分析</li>
                    <li><span class="highlight">对话数据</span>：600个指导对话</li>
                    <li><span class="highlight">指令跟随</span>：600个专业指令样本</li>
                </ul>
                <div class="prompt-box">
                    <strong>PEC理论Prompt：</strong><br>
                    "基于PEC（Parental Emotion Coaching）理论和ASQ:SE-2标准，为{年龄段}婴儿{情绪表现}提供：认知、运动、语言、社交、情绪五领域发展分析和专业指导策略。"
                </div>
                <h3>📝 数据格式样例</h3>
                <div class="data-format-sample">
                    <pre>{
  "scenario_id": "general_0-1m_5166",
  "age_range": "0-1m",
  "development_domain": "cognitive",
  "scenario_type": "daily_care",
  "context": {
    "setting": "home",
    "participants": ["caregiver", "infant"],
    "background": "日常互动中的观察和指导"
  },
  "infant_behavior": {
    "observable_signs": ["安静", "睡眠"],
    "emotional_state": "sad",
    "developmental_indicators": ["general_development"]
  },
  "guidance_dialogue": [
    {
      "speaker": "家长",
      "content": "宝宝总是哭闹，我不知道怎么安抚..."
    },
    {
      "speaker": "专家",
      "content": "基于PEC理论，建议您..."
    }
  ]
}</pre>
                </div>
            </div>
        </div>

        <!-- 第10页：语言发展指导 -->
        <div class="slide">
            <h2>💬 语言发展指导数据集</h2>
            <div class="dataset-card">
                <div class="dataset-header">
                    <div class="dataset-icon">📚</div>
                    <div class="dataset-title">权威教材+AI增强生成</div>
                </div>
                <div class="dataset-stats">
                    <div class="stat-item">
                        <div class="stat-number">707</div>
                        <div class="stat-label">专业问答对</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">180</div>
                        <div class="stat-label">教材页数</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-number">100%</div>
                        <div class="stat-label">AI生成成功率</div>
                    </div>
                </div>
                <h3>📚 数据来源与特色</h3>
                <ul style="font-size: 1.1em; line-height: 1.8; margin-left: 20px;">
                    <li><span class="highlight">权威教材</span>：《0-3岁婴幼儿语言发展与教育》</li>
                    <li><span class="highlight">Qwen增强</span>：500个高质量对话，成功率100%</li>
                    <li><span class="highlight">内容丰富</span>：实践指导、理论知识、里程碑、问题解决</li>
                    <li><span class="highlight">质量保证</span>：严格去重、过滤和质量控制</li>
                </ul>
                <div class="prompt-box">
                    <strong>核心Prompt示例：</strong><br>
                    "基于《0-3岁婴幼儿语言发展与教育》专业教材，为{年龄段}婴儿提供语言发展里程碑、实践指导方法、理论知识解释和常见问题解决方案。"
                </div>
                <h3>📝 数据格式样例</h3>
                <div class="data-format-sample">
                    <pre>{
  "instruction": "2岁宝宝语言发展迟缓，只会说几个简单的词，应该怎么办？",
  "output": "根据《0-3岁婴幼儿语言发展与教育》专业指导：\n\n**发育评估**：\n2岁儿童正常语言发展应达到：\n- 词汇量：50-200个词汇\n- 句法：开始使用2-3个词的简单句\n- 理解：能理解简单指令\n\n**指导建议**：\n1. **增加语言输入**：每天与孩子进行丰富的语言交流\n2. **创造语言环境**：通过游戏、歌曲、故事增加语言刺激\n3. **及时回应**：对孩子的发声和手势给予积极回应\n\n**专业建议**：\n如果2岁半后仍无明显改善，建议咨询语言治疗师进行专业评估。",
  "metadata": {
    "source": "professional_textbook",
    "age_group": "24m",
    "topic": "language_delay",
    "quality_score": 0.95
  }
}</pre>
                </div>
            </div>
        </div>

        <!-- 第11页：数据集总览 -->
        <div class="slide">
            <h2>📊 数据集总览与统计</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <div class="summary-number">10</div>
                    <div class="summary-label">核心功能模块</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">13,000+</div>
                    <div class="summary-label">总数据量</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">9</div>
                    <div class="summary-label">数据集文件夹</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number">100%</div>
                    <div class="summary-label">权威标准覆盖</div>
                </div>
            </div>
            <div style="margin-top: 40px;">
                <h3 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">🎯 核心技术特色</h3>
                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 20px;">
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #3498db;">
                        <h4 style="color: #2980b9; margin-bottom: 10px;">🏥 权威医学标准</h4>
                        <p>基于国家卫健委、WHO等权威标准构建</p>
                    </div>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #e74c3c;">
                        <h4 style="color: #c0392b; margin-bottom: 10px;">🤖 AI增强生成</h4>
                        <p>使用Qwen等先进模型生成高质量数据</p>
                    </div>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #27ae60;">
                        <h4 style="color: #229954; margin-bottom: 10px;">📋 结构化标注</h4>
                        <p>统一格式、丰富元数据、质量控制</p>
                    </div>
                    <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; border-left: 4px solid #f39c12;">
                        <h4 style="color: #d68910; margin-bottom: 10px;">🎯 实用导向</h4>
                        <p>面向实际应用场景的专业指导</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第12页：应用前景 -->
        <div class="slide">
            <h2>🚀 应用前景与价值</h2>
            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 30px; width: 100%; max-width: 1200px;">
                <div class="dataset-card" style="margin: 0;">
                    <h3 style="color: #2980b9; margin-bottom: 20px;">🏥 医疗健康领域</h3>
                    <ul style="font-size: 1.1em; line-height: 1.8;">
                        <li>智能儿科诊断辅助系统</li>
                        <li>发育评估自动化工具</li>
                        <li>家庭健康监测应用</li>
                        <li>专业医生培训平台</li>
                    </ul>
                </div>
                <div class="dataset-card" style="margin: 0;">
                    <h3 style="color: #27ae60; margin-bottom: 20px;">👨‍👩‍👧‍👦 家庭教育领域</h3>
                    <ul style="font-size: 1.1em; line-height: 1.8;">
                        <li>智能育儿助手</li>
                        <li>个性化发育指导</li>
                        <li>早期干预预警系统</li>
                        <li>家长教育知识库</li>
                    </ul>
                </div>
                <div class="dataset-card" style="margin: 0;">
                    <h3 style="color: #e74c3c; margin-bottom: 20px;">🎓 教育科研领域</h3>
                    <ul style="font-size: 1.1em; line-height: 1.8;">
                        <li>儿童发展研究工具</li>
                        <li>AI模型训练数据</li>
                        <li>跨学科研究支持</li>
                        <li>标准化评估体系</li>
                    </ul>
                </div>
                <div class="dataset-card" style="margin: 0;">
                    <h3 style="color: #f39c12; margin-bottom: 20px;">💼 产业应用领域</h3>
                    <ul style="font-size: 1.1em; line-height: 1.8;">
                        <li>智能硬件产品</li>
                        <li>移动健康应用</li>
                        <li>专业服务平台</li>
                        <li>数据驱动创新</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 导航控制 -->
    <div class="navigation">
        <button class="nav-btn" onclick="previousSlide()">← 上一页</button>
        <button class="nav-btn" onclick="nextSlide()">下一页 →</button>
    </div>

    <!-- 幻灯片计数器 -->
    <div class="slide-counter">
        <span id="current-slide">1</span> / <span id="total-slides">12</span>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        document.getElementById('total-slides').textContent = totalSlides;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');
            document.getElementById('current-slide').textContent = currentSlide + 1;
        }

        function nextSlide() {
            showSlide(currentSlide + 1);
        }

        function previousSlide() {
            showSlide(currentSlide - 1);
        }

        // 键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                nextSlide();
            } else if (e.key === 'ArrowLeft') {
                previousSlide();
            } else if (e.key === 'Home') {
                showSlide(0);
            } else if (e.key === 'End') {
                showSlide(totalSlides - 1);
            }
        });

        // 触摸滑动支持
        let startX = 0;
        let endX = 0;

        document.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
        });

        document.addEventListener('touchend', function(e) {
            endX = e.changedTouches[0].clientX;
            if (startX - endX > 50) {
                nextSlide();
            } else if (endX - startX > 50) {
                previousSlide();
            }
        });

        // 鼠标滚轮支持
        document.addEventListener('wheel', function(e) {
            if (e.deltaY > 0) {
                nextSlide();
            } else {
                previousSlide();
            }
            e.preventDefault();
        });

        // 点击切换
        document.addEventListener('click', function(e) {
            if (e.target.closest('.nav-btn')) return;
            if (e.clientX > window.innerWidth / 2) {
                nextSlide();
            } else {
                previousSlide();
            }
        });
    </script>
</body>
</html>
