#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HTML到PPTX转换器
将育幼健康监测数据集汇报HTML文件转换为PowerPoint格式
"""

import os
import re
import json
from bs4 import BeautifulSoup
from pptx import Presentation
from pptx.util import Inches, Pt
from pptx.enum.text import PP_ALIGN
from pptx.dml.color import RGBColor
from pptx.enum.shapes import MSO_SHAPE
from pptx.enum.text import MSO_ANCHOR

def parse_html_content(html_file_path):
    """解析HTML文件，提取幻灯片内容"""
    print(f"正在解析HTML文件: {html_file_path}")
    
    with open(html_file_path, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 提取所有幻灯片
    slides = soup.find_all('div', class_='slide')
    print(f"找到 {len(slides)} 个幻灯片")
    
    slide_data = []
    
    for i, slide in enumerate(slides):
        slide_info = {
            'index': i,
            'title': '',
            'content': [],
            'stats': [],
            'prompt': '',
            'data_sample': ''
        }
        
        # 提取标题
        title_elem = slide.find('h2')
        if title_elem:
            slide_info['title'] = title_elem.get_text().strip()
        
        # 提取统计数据
        stats_container = slide.find('div', class_='dataset-stats')
        if stats_container:
            stat_items = stats_container.find_all('div', class_='stat-item')
            for stat in stat_items:
                number_elem = stat.find('div', class_='stat-number')
                label_elem = stat.find('div', class_='stat-label')
                if number_elem and label_elem:
                    slide_info['stats'].append({
                        'number': number_elem.get_text().strip(),
                        'label': label_elem.get_text().strip()
                    })
        
        # 提取内容列表
        content_lists = slide.find_all('ul')
        for ul in content_lists:
            items = ul.find_all('li')
            for item in items:
                text = item.get_text().strip()
                if text:
                    slide_info['content'].append(text)
        
        # 提取Prompt信息
        prompt_box = slide.find('div', class_='prompt-box')
        if prompt_box:
            slide_info['prompt'] = prompt_box.get_text().strip()
        
        # 提取数据格式样例
        data_sample = slide.find('div', class_='data-format-sample')
        if data_sample:
            pre_elem = data_sample.find('pre')
            if pre_elem:
                slide_info['data_sample'] = pre_elem.get_text().strip()
        
        slide_data.append(slide_info)
    
    return slide_data

def create_pptx_presentation(slide_data, output_path):
    """创建PPTX演示文稿"""
    print("正在创建PowerPoint演示文稿...")
    
    # 创建演示文稿
    prs = Presentation()
    
    # 设置幻灯片尺寸为16:9
    prs.slide_width = Inches(13.33)
    prs.slide_height = Inches(7.5)
    
    for slide_info in slide_data:
        print(f"正在创建第 {slide_info['index'] + 1} 页: {slide_info['title']}")
        
        if slide_info['index'] == 0:
            # 封面页
            create_title_slide(prs, slide_info)
        elif slide_info['index'] == len(slide_data) - 2:
            # 总览页
            create_summary_slide(prs, slide_info)
        elif slide_info['index'] == len(slide_data) - 1:
            # 结束页
            create_end_slide(prs, slide_info)
        else:
            # 内容页
            create_content_slide(prs, slide_info)
    
    # 保存文件
    prs.save(output_path)
    print(f"PowerPoint文件已保存: {output_path}")

def create_title_slide(prs, slide_info):
    """创建封面页"""
    slide_layout = prs.slide_layouts[0]  # 标题幻灯片布局
    slide = prs.slides.add_slide(slide_layout)
    
    # 设置标题
    title = slide.shapes.title
    title.text = "育幼健康监测数据集汇报"
    title.text_frame.paragraphs[0].font.size = Pt(44)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = RGBColor(46, 134, 171)
    
    # 设置副标题
    subtitle = slide.placeholders[1]
    subtitle.text = "10个功能模块的专业数据集构建与应用\n基于LLM增强的多模态婴幼儿发展监测系统"
    subtitle.text_frame.paragraphs[0].font.size = Pt(24)
    subtitle.text_frame.paragraphs[1].font.size = Pt(20)

def create_content_slide(prs, slide_info):
    """创建内容页"""
    slide_layout = prs.slide_layouts[1]  # 标题和内容布局
    slide = prs.slides.add_slide(slide_layout)
    
    # 设置标题
    title = slide.shapes.title
    title.text = slide_info['title']
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = RGBColor(46, 134, 171)
    
    # 获取内容占位符
    content_placeholder = slide.placeholders[1]
    
    # 构建内容文本
    content_text = ""
    
    # 添加统计数据
    if slide_info['stats']:
        content_text += "📊 核心数据:\n"
        for stat in slide_info['stats']:
            content_text += f"• {stat['label']}: {stat['number']}\n"
        content_text += "\n"
    
    # 添加特色内容
    if slide_info['content']:
        content_text += "🎯 数据集特色:\n"
        for item in slide_info['content'][:6]:  # 限制显示条目数
            # 清理HTML标签和特殊字符
            clean_item = re.sub(r'<[^>]+>', '', item)
            content_text += f"• {clean_item}\n"
        content_text += "\n"
    
    # 添加Prompt信息
    if slide_info['prompt']:
        content_text += "💡 核心技术:\n"
        prompt_text = slide_info['prompt'].replace('核心Prompt示例：', '').replace('五步纠正法Prompt：', '').replace('四维度分析Prompt：', '').replace('PEC理论Prompt：', '').strip()
        # 截取前200字符
        if len(prompt_text) > 200:
            prompt_text = prompt_text[:200] + "..."
        content_text += f"• {prompt_text}\n\n"
    
    # 添加数据格式样例
    if slide_info['data_sample']:
        content_text += "📝 数据格式样例:\n"
        # 提取JSON的关键字段
        try:
            sample_lines = slide_info['data_sample'].split('\n')[:8]  # 取前8行
            for line in sample_lines:
                if line.strip():
                    content_text += f"  {line}\n"
            content_text += "  ...\n"
        except:
            content_text += "  结构化JSON格式数据\n"
    
    # 设置内容
    content_placeholder.text = content_text
    
    # 设置字体样式
    for paragraph in content_placeholder.text_frame.paragraphs:
        paragraph.font.size = Pt(16)
        paragraph.font.name = "Microsoft YaHei"

def create_summary_slide(prs, slide_info):
    """创建总览页"""
    slide_layout = prs.slide_layouts[1]
    slide = prs.slides.add_slide(slide_layout)
    
    # 设置标题
    title = slide.shapes.title
    title.text = slide_info['title']
    title.text_frame.paragraphs[0].font.size = Pt(32)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = RGBColor(46, 134, 171)
    
    # 设置总览内容
    content_placeholder = slide.placeholders[1]
    
    summary_text = """🎯 数据集建设成果:
• 10个专业功能模块，覆盖婴幼儿发展全领域
• 总计超过5,000条高质量数据记录
• 基于权威医学标准和发育里程碑
• 多模态AI技术深度集成

🔧 技术创新亮点:
• LLM增强的专业数据生成
• 多源数据融合与质量控制
• 标准化数据格式与元数据标注
• 可扩展的模块化架构设计

📈 应用价值:
• 支持婴幼儿发展评估与监测
• 为AI模型训练提供专业数据
• 促进育幼领域数字化发展
• 建立行业数据标准规范"""
    
    content_placeholder.text = summary_text
    
    for paragraph in content_placeholder.text_frame.paragraphs:
        paragraph.font.size = Pt(18)
        paragraph.font.name = "Microsoft YaHei"

def create_end_slide(prs, slide_info):
    """创建结束页"""
    slide_layout = prs.slide_layouts[0]
    slide = prs.slides.add_slide(slide_layout)
    
    title = slide.shapes.title
    title.text = "谢谢聆听"
    title.text_frame.paragraphs[0].font.size = Pt(48)
    title.text_frame.paragraphs[0].font.bold = True
    title.text_frame.paragraphs[0].font.color.rgb = RGBColor(46, 134, 171)
    title.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
    
    subtitle = slide.placeholders[1]
    subtitle.text = "育幼健康监测数据集\n期待您的宝贵建议"
    subtitle.text_frame.paragraphs[0].font.size = Pt(28)
    subtitle.text_frame.paragraphs[1].font.size = Pt(24)
    subtitle.text_frame.paragraphs[0].alignment = PP_ALIGN.CENTER
    subtitle.text_frame.paragraphs[1].alignment = PP_ALIGN.CENTER

def main():
    """主函数"""
    html_file = "育幼健康监测数据集汇报.html"
    output_file = "育幼健康监测数据集汇报.pptx"
    
    if not os.path.exists(html_file):
        print(f"错误: 找不到HTML文件 {html_file}")
        return
    
    try:
        # 解析HTML内容
        slide_data = parse_html_content(html_file)
        
        # 创建PPTX文件
        create_pptx_presentation(slide_data, output_file)
        
        print(f"\n✅ 转换完成!")
        print(f"📁 输出文件: {output_file}")
        print(f"📊 总页数: {len(slide_data)}")
        
    except Exception as e:
        print(f"❌ 转换过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
