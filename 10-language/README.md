# 婴幼儿语言发展指导数据集构建项目

## 项目概述

本项目旨在构建一个高质量的婴幼儿语言发展指导文本数据集，用于训练支持语言发展指导方向的育幼健康监测-指导功能的大语言模型。

## 数据来源

1. **国家政策文档**: 国家卫健委、教育部等发布的婴幼儿语言发展相关政策文档和指导意见
2. **专业文献**: 儿童发展心理学、语言学等专业书籍和百科全书中的相关内容
3. **QA问答对**: 基于专业知识构建的高质量问答对，涵盖不同年龄段的语言发展指导

## 项目结构

```
├── README.md                    # 项目说明文档
├── requirements.txt             # Python依赖包
├── config/                      # 配置文件
│   ├── data_sources.yaml       # 数据源配置
│   └── processing_config.yaml  # 数据处理配置
├── data/                        # 数据目录
│   ├── raw/                     # 原始数据
│   │   ├── government/          # 政府文档
│   │   ├── literature/          # 专业文献
│   │   └── qa_pairs/           # 问答对
│   ├── processed/               # 处理后数据
│   └── final/                   # 最终数据集
├── scripts/                     # 数据处理脚本
│   ├── collect_data.py         # 数据收集
│   ├── preprocess.py           # 数据预处理
│   ├── generate_qa.py          # QA生成
│   └── validate.py             # 数据验证
├── docs/                        # 文档
│   ├── data_schema.md          # 数据格式说明
│   ├── collection_guide.md     # 数据收集指南
│   └── quality_standards.md    # 质量标准
└── notebooks/                   # Jupyter笔记本
    ├── data_analysis.ipynb     # 数据分析
    └── quality_check.ipynb     # 质量检查
```

## 数据集特点

- **专业性**: 基于权威医学、教育和心理学资料
- **实用性**: 针对实际育儿场景的指导建议
- **年龄分层**: 覆盖0-6岁不同发展阶段
- **多样性**: 包含理论知识、实践指导和问答对话

## 使用方法

1. 安装依赖: `pip install -r requirements.txt`
2. 配置数据源: 编辑 `config/data_sources.yaml`
3. 运行数据收集: `python scripts/collect_data.py`
4. 数据预处理: `python scripts/preprocess.py`
5. 生成QA对: `python scripts/generate_qa.py`
6. 验证数据质量: `python scripts/validate.py`

## 数据格式

数据集采用JSON Lines格式，每行包含一个数据样本：

```json
{
  "id": "unique_identifier",
  "source": "government|literature|qa",
  "category": "language_development",
  "age_range": "0-6months|6-12months|1-2years|2-3years|3-6years",
  "content_type": "guideline|knowledge|qa_pair",
  "title": "标题",
  "content": "内容文本",
  "metadata": {
    "author": "作者/机构",
    "publish_date": "发布日期",
    "source_url": "来源链接",
    "keywords": ["关键词1", "关键词2"]
  }
}
```

## 质量控制

- 内容准确性验证
- 专业术语一致性检查
- 年龄适宜性评估
- 重复内容去除
- 格式标准化处理

## 许可证

本项目遵循 MIT 许可证。
