{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 婴幼儿语言发展指导数据集使用示例\n", "# Infant Language Development Guidance Dataset Usage Example\n", "\n", "本notebook展示如何使用婴幼儿语言发展指导数据集进行各种数据分析和模型训练任务。\n", "\n", "This notebook demonstrates how to use the Infant Language Development Guidance Dataset for various data analysis and model training tasks."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 环境设置和数据加载\n", "## Environment Setup and Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 安装必要的依赖包\n", "# Install required packages\n", "!pip install pandas numpy mat<PERSON><PERSON><PERSON>b seaborn jieba\n", "\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from collections import Counter\n", "import jieba\n", "from load_dataset import InfantLanguageDataset\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"环境设置完成！\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载数据集\n", "# Load dataset\n", "dataset = InfantLanguageDataset()\n", "\n", "# 获取主数据集\n", "main_data = dataset.get_main_dataset()\n", "dialogue_data = dataset.get_dialogue_dataset()\n", "\n", "print(f\"主数据集包含 {len(main_data)} 条记录\")\n", "print(f\"对话数据集包含 {len(dialogue_data)} 条记录\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 数据集基本统计\n", "## Basic Dataset Statistics"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 获取统计信息\n", "stats = dataset.get_statistics()\n", "\n", "print(\"=== 数据集统计信息 ===\")\n", "for key, value in stats.items():\n", "    if isinstance(value, dict):\n", "        print(f\"\\n{key}:\")\n", "        for k, v in value.items():\n", "            print(f\"  {k}: {v}\")\n", "    else:\n", "        print(f\"{key}: {value}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建DataFrame进行分析\n", "df = pd.DataFrame(main_data)\n", "\n", "# 显示基本信息\n", "print(\"数据集基本信息:\")\n", "print(df.info())\n", "\n", "print(\"\\n前5条记录:\")\n", "print(df[['id', 'age_range', 'content_type', 'source']].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 数据分布可视化\n", "## Data Distribution Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建子图\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# 年龄段分布\n", "age_counts = df['age_range'].value_counts()\n", "axes[0, 0].pie(age_counts.values, labels=age_counts.index, autopct='%1.1f%%')\n", "axes[0, 0].set_title('年龄段分布')\n", "\n", "# 内容类型分布\n", "content_counts = df['content_type'].value_counts()\n", "axes[0, 1].bar(content_counts.index, content_counts.values)\n", "axes[0, 1].set_title('内容类型分布')\n", "axes[0, 1].tick_params(axis='x', rotation=45)\n", "\n", "# 数据来源分布\n", "source_counts = df['source'].value_counts()\n", "axes[1, 0].pie(source_counts.values, labels=source_counts.index, autopct='%1.1f%%')\n", "axes[1, 0].set_title('数据来源分布')\n", "\n", "# 内容长度分布\n", "content_lengths = df['content'].str.len()\n", "axes[1, 1].hist(content_lengths, bins=30, alpha=0.7)\n", "axes[1, 1].set_title('内容长度分布')\n", "axes[1, 1].set_xlabel('字符数')\n", "axes[1, 1].set_ylabel('频次')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. <PERSON><PERSON>生成数据分析\n", "## Qwen Generated Data Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析<PERSON>wen生成的数据\n", "qwen_data = dataset.filter_by_source('qwen_api')\n", "qwen_df = pd.DataFrame(qwen_data)\n", "\n", "print(f\"Qwen生成的数据: {len(qwen_data)} 条\")\n", "print(f\"占总数据的比例: {len(qwen_data)/len(main_data)*100:.1f}%\")\n", "\n", "# 按批次分析\n", "if len(qwen_data) > 0:\n", "    batch_info = {}\n", "    for item in qwen_data:\n", "        batch = item.get('metadata', {}).get('batch', 'unknown')\n", "        batch_info[batch] = batch_info.get(batch, 0) + 1\n", "    \n", "    print(\"\\n按生成批次分布:\")\n", "    for batch, count in batch_info.items():\n", "        print(f\"  {batch}: {count} 条\")\n", "    \n", "    # 可视化批次分布\n", "    plt.figure(figsize=(8, 6))\n", "    plt.bar(batch_info.keys(), batch_info.values())\n", "    plt.title('<PERSON><PERSON>生成数据批次分布')\n", "    plt.ylabel('数据条数')\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 文本内容分析\n", "## Text Content Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析问题和回答的长度\n", "title_lengths = df['title'].str.len()\n", "content_lengths = df['content'].str.len()\n", "\n", "print(\"文本长度统计:\")\n", "print(f\"问题平均长度: {title_lengths.mean():.1f} 字符\")\n", "print(f\"回答平均长度: {content_lengths.mean():.1f} 字符\")\n", "print(f\"问题长度范围: {title_lengths.min()} - {title_lengths.max()}\")\n", "print(f\"回答长度范围: {content_lengths.min()} - {content_lengths.max()}\")\n", "\n", "# 可视化长度分布\n", "fig, axes = plt.subplots(1, 2, figsize=(15, 5))\n", "\n", "axes[0].hist(title_lengths, bins=30, alpha=0.7, color='blue')\n", "axes[0].set_title('问题长度分布')\n", "axes[0].set_xlabel('字符数')\n", "axes[0].set_ylabel('频次')\n", "\n", "axes[1].hist(content_lengths, bins=30, alpha=0.7, color='green')\n", "axes[1].set_title('回答长度分布')\n", "axes[1].set_xlabel('字符数')\n", "axes[1].set_ylabel('频次')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 关键词分析\n", "def extract_keywords(texts, top_n=20):\n", "    \"\"\"提取关键词\"\"\"\n", "    all_words = []\n", "    for text in texts:\n", "        words = jieba.lcut(text)\n", "        # 过滤停用词和短词\n", "        words = [w for w in words if len(w) > 1 and w not in ['的', '了', '是', '在', '有', '和', '我', '你', '他', '她', '它']]\n", "        all_words.extend(words)\n", "    \n", "    return Counter(all_words).most_common(top_n)\n", "\n", "# 分析问题中的关键词\n", "title_keywords = extract_keywords(df['title'].tolist())\n", "print(\"问题中的高频关键词:\")\n", "for word, count in title_keywords[:10]:\n", "    print(f\"  {word}: {count}\")\n", "\n", "# 分析回答中的关键词\n", "content_keywords = extract_keywords(df['content'].tolist())\n", "print(\"\\n回答中的高频关键词:\")\n", "for word, count in content_keywords[:10]:\n", "    print(f\"  {word}: {count}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 数据筛选和导出示例\n", "## Data Filtering and Export Examples"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 筛选特定年龄段的数据\n", "age_0_3_data = dataset.filter_by_age('0-3years')\n", "print(f\"0-3岁数据: {len(age_0_3_data)} 条\")\n", "\n", "# 筛选问答对话数据\n", "qa_data = dataset.filter_by_content_type('qa_pair')\n", "print(f\"问答对话数据: {len(qa_data)} 条\")\n", "\n", "# 筛选中级难度数据\n", "intermediate_data = dataset.filter_by_difficulty('intermediate')\n", "print(f\"中级难度数据: {len(intermediate_data)} 条\")\n", "\n", "# 搜索包含特定关键词的数据\n", "language_dev_data = dataset.search_content('语言发展')\n", "print(f\"包含'语言发展'的数据: {len(language_dev_data)} 条\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导出筛选后的数据\n", "# Export filtered data\n", "\n", "# 导出0-3岁的问答数据\n", "filters = {\n", "    'age_range': '0-3years',\n", "    'content_type': 'qa_pair'\n", "}\n", "\n", "# 注意：这里只是示例，实际运行时请确保有写入权限\n", "# dataset.export_filtered_data(filters, 'filtered_0_3_qa_data.json')\n", "print(\"数据导出示例（已注释，避免实际写入文件）\")\n", "\n", "# 导出为CSV格式\n", "# dataset.export_to_csv('dataset_export.csv', include_metadata=False)\n", "print(\"CSV导出示例（已注释，避免实际写入文件）\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 样本数据展示\n", "## Sample Data Display"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 显示随机样本\n", "samples = dataset.get_random_sample(3)\n", "\n", "for i, sample in enumerate(samples, 1):\n", "    print(f\"\\n=== 样本 {i} ===\")\n", "    print(f\"ID: {sample.get('id')}\")\n", "    print(f\"年龄段: {sample.get('age_range')}\")\n", "    print(f\"内容类型: {sample.get('content_type')}\")\n", "    print(f\"来源: {sample.get('source')}\")\n", "    \n", "    metadata = sample.get('metadata', {})\n", "    if metadata.get('generated_by'):\n", "        print(f\"生成方式: {metadata.get('generated_by')}\")\n", "    if metadata.get('batch'):\n", "        print(f\"生成批次: {metadata.get('batch')}\")\n", "    \n", "    print(f\"\\n问题: {sample.get('title', '')[:100]}...\")\n", "    print(f\"\\n回答: {sample.get('content', '')[:200]}...\")\n", "    print(\"-\" * 50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 对话格式数据分析\n", "## Dialogue Format Data Analysis"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 分析对话格式数据\n", "if dialogue_data:\n", "    print(f\"对话数据集包含 {len(dialogue_data)} 条对话\")\n", "    \n", "    # 分析对话轮次\n", "    conversation_lengths = [len(item.get('conversations', [])) for item in dialogue_data]\n", "    print(f\"平均对话轮次: {np.mean(conversation_lengths):.1f}\")\n", "    \n", "    # 显示对话示例\n", "    print(\"\\n=== 对话示例 ===\")\n", "    sample_dialogue = dialogue_data[0]\n", "    conversations = sample_dialogue.get('conversations', [])\n", "    \n", "    for conv in conversations:\n", "        role = conv.get('from', '')\n", "        content = conv.get('value', '')\n", "        print(f\"{role}: {content[:100]}...\")\n", "else:\n", "    print(\"未找到对话格式数据\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. 数据质量评估\n", "## Data Quality Assessment"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 数据完整性检查\n", "print(\"=== 数据质量评估 ===\")\n", "\n", "# 检查必要字段的完整性\n", "required_fields = ['id', 'title', 'content', 'age_range', 'content_type']\n", "for field in required_fields:\n", "    missing_count = df[field].isnull().sum()\n", "    print(f\"{field} 缺失数量: {missing_count}\")\n", "\n", "# 检查空内容\n", "empty_titles = (df['title'].str.len() == 0).sum()\n", "empty_contents = (df['content'].str.len() == 0).sum()\n", "print(f\"\\n空标题数量: {empty_titles}\")\n", "print(f\"空内容数量: {empty_contents}\")\n", "\n", "# 检查重复数据\n", "duplicate_titles = df['title'].duplicated().sum()\n", "duplicate_contents = df['content'].duplicated().sum()\n", "print(f\"\\n重复标题数量: {duplicate_titles}\")\n", "print(f\"重复内容数量: {duplicate_contents}\")\n", "\n", "# 数据质量评分\n", "total_records = len(df)\n", "quality_score = (total_records - missing_count - empty_titles - empty_contents - duplicate_titles) / total_records * 100\n", "print(f\"\\n数据质量评分: {quality_score:.1f}%\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. 总结和建议\n", "## Summary and Recommendations"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== 数据集总结 ===\")\n", "print(f\"• 总记录数: {len(main_data)}\")\n", "print(f\"• 对话记录数: {len(dialogue_data)}\")\n", "print(f\"• <PERSON><PERSON>生成比例: {len(qwen_data)/len(main_data)*100:.1f}%\")\n", "print(f\"• 平均内容长度: {content_lengths.mean():.0f} 字符\")\n", "print(f\"• 年龄段覆盖: {', '.join(df['age_range'].unique())}\")\n", "\n", "print(\"\\n=== 使用建议 ===\")\n", "print(\"• 对话模型训练: 使用dialogue格式数据\")\n", "print(\"• 问答系统: 使用qa_pair类型数据\")\n", "print(\"• 特定年龄段应用: 根据age_range筛选\")\n", "print(\"• 内容生成: 参考Qwen生成的高质量样本\")\n", "print(\"• 数据扩展: 基于现有模式生成更多数据\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}