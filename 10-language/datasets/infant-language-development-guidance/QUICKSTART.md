# 快速开始指南 | Quick Start Guide

## 🚀 5分钟快速上手

### 1. 环境准备

```bash
# 安装依赖
pip install pandas numpy matplotlib seaborn jieba

# 或者使用requirements.txt
pip install -r requirements.txt
```

### 2. 加载数据集

```python
from load_dataset import InfantLanguageDataset

# 创建数据集实例
dataset = InfantLanguageDataset()

# 获取基本信息
stats = dataset.get_statistics()
print(f"数据集包含 {stats['total_records']} 条记录")
```

### 3. 基本操作

```python
# 获取主数据集
main_data = dataset.get_main_dataset()

# 筛选特定年龄段
age_0_3_data = dataset.filter_by_age('0-3years')
print(f"0-3岁数据: {len(age_0_3_data)} 条")

# 筛选问答对话
qa_data = dataset.filter_by_content_type('qa_pair')
print(f"问答数据: {len(qa_data)} 条")

# 搜索关键词
language_data = dataset.search_content('语言发展')
print(f"包含'语言发展'的数据: {len(language_data)} 条")
```

### 4. 查看样本数据

```python
# 显示随机样本
dataset.print_sample_data(3)
```

## 📁 文件说明

| 文件名 | 描述 | 用途 |
|--------|------|------|
| `super_final_infant_language_development_dataset.json` | 主数据集 | 完整的707条记录 |
| `super_final_dialogue_training_dataset.jsonl` | 对话训练格式 | 用于对话模型训练 |
| `load_dataset.py` | 数据加载器 | 便捷的数据操作工具 |
| `example_usage.ipynb` | 使用示例 | Jupyter Notebook教程 |
| `README.md` | 详细文档 | 完整的数据集说明 |

## 🎯 常见用例

### 对话模型训练

```python
# 加载对话格式数据
dialogue_data = dataset.get_dialogue_dataset()

# 转换为训练格式
for item in dialogue_data:
    conversations = item['conversations']
    # 处理对话数据...
```

### 问答系统构建

```python
# 筛选问答数据
qa_pairs = dataset.filter_by_content_type('qa_pair')

# 构建问答对
for item in qa_pairs:
    question = item['title']
    answer = item['content']
    # 构建问答系统...
```

### 特定年龄段应用

```python
# 筛选0-6个月数据
infant_data = dataset.filter_by_age('0-6months')

# 筛选2-3岁数据
toddler_data = dataset.filter_by_age('2-3years')
```

## 📊 数据统计概览

- **总记录数**: 707条
- **对话记录**: 675条
- **Qwen生成**: 605条 (85.6%)
- **年龄段覆盖**: 0-3岁全覆盖
- **内容类型**: 问答对话、指导方法、知识点等

## 🔧 高级功能

### 数据导出

```python
# 导出为CSV
dataset.export_to_csv('my_dataset.csv')

# 导出筛选后的数据
filters = {'age_range': '0-3years', 'content_type': 'qa_pair'}
dataset.export_filtered_data(filters, 'filtered_data.json')
```

### 数据分析

```python
import pandas as pd

# 转换为DataFrame
df = pd.DataFrame(dataset.get_main_dataset())

# 分析内容长度分布
content_lengths = df['content'].str.len()
print(f"平均内容长度: {content_lengths.mean():.0f} 字符")

# 分析年龄段分布
age_dist = df['age_range'].value_counts()
print(age_dist)
```

## 🎓 学习资源

1. **详细文档**: 查看 `README.md` 了解完整功能
2. **交互式教程**: 运行 `example_usage.ipynb` 学习高级用法
3. **配置文件**: 查看 `dataset_config.yaml` 了解数据结构
4. **API文档**: 查看 `load_dataset.py` 中的函数文档

## ❓ 常见问题

**Q: 如何选择合适的数据格式？**
A: JSON格式适合数据分析，JSONL格式适合流式处理，对话格式专门用于模型训练。

**Q: 数据集是否包含敏感信息？**
A: 不包含，所有内容都是基于公开教材和AI生成的通用指导内容。

**Q: 如何获取最新版本？**
A: 查看版本历史和更新说明，关注GitHub仓库获取最新版本。

## 📞 获取帮助

- 📧 Email: [<EMAIL>]
- 🐛 Issues: [GitHub Issues页面]
- 💬 讨论: [GitHub Discussions页面]

---

**开始探索数据集，构建你的婴幼儿语言发展AI应用！** 🚀
