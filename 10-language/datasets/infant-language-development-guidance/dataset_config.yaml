# 婴幼儿语言发展指导数据集配置文件
# Infant Language Development Guidance Dataset Configuration

dataset_info:
  name: "infant-language-development-guidance"
  version: "4.0.0"
  description: "专门针对0-3岁婴幼儿语言发展指导的高质量中文数据集"
  language: "zh"
  license: "CC BY 4.0"
  homepage: "https://github.com/your-repo/infant-language-development-guidance"
  
  # 数据统计
  statistics:
    total_records: 707
    qa_pairs: 675
    qwen_generated: 605
    enhanced_records: 131
    avg_content_length: 402
    
  # 年龄段分布
  age_distribution:
    "0-3years": 632
    "0-6months": 39
    "2-3years": 28
    "1-2years": 8
    
  # 内容类型分布
  content_type_distribution:
    "qa_pair": 675
    "guidance_methods": 25
    "knowledge": 4
    "general": 3

# 文件配置
files:
  main_dataset:
    filename: "super_final_infant_language_development_dataset.json"
    format: "json"
    description: "主数据集，包含所有记录的完整信息"
    size_mb: 2.8
    
  main_dataset_jsonl:
    filename: "super_final_infant_language_development_dataset.jsonl"
    format: "jsonl"
    description: "主数据集的JSONL格式，适合流式处理"
    size_mb: 2.8
    
  dialogue_training:
    filename: "super_final_dialogue_training_dataset.jsonl"
    format: "jsonl"
    description: "专门用于对话模型训练的格式"
    size_mb: 2.6
    records: 675
    
  statistics_report:
    filename: "super_final_dataset_report.json"
    format: "json"
    description: "详细的统计报告"
    
  qwen_batch1:
    filename: "qwen_generated_dialogues_full.jsonl"
    format: "jsonl"
    description: "第一批100个Qwen生成的对话"
    records: 100
    
  qwen_batch2:
    filename: "qwen_generated_dialogues_400.jsonl"
    format: "jsonl"
    description: "第二批400个Qwen生成的对话"
    records: 400

# 数据字段定义
schema:
  main_dataset:
    id:
      type: "string"
      description: "数据记录的唯一标识符"
      required: true
      
    source:
      type: "string"
      description: "数据来源类型"
      values: ["qa", "literature", "government"]
      required: true
      
    category:
      type: "string"
      description: "数据类别"
      values: ["language_development"]
      required: true
      
    age_range:
      type: "string"
      description: "适用年龄段"
      values: ["0-3years", "0-6months", "1-2years", "2-3years"]
      required: true
      
    content_type:
      type: "string"
      description: "内容类型"
      values: ["qa_pair", "knowledge", "guidance_methods", "general"]
      required: true
      
    title:
      type: "string"
      description: "问题或标题内容"
      required: true
      
    content:
      type: "string"
      description: "回答或正文内容"
      required: true
      
    metadata:
      type: "object"
      description: "元数据信息"
      properties:
        generated_by:
          type: "string"
          values: ["qwen_api", "manual", "ocr_extraction"]
        model:
          type: "string"
          values: ["qwen-turbo-latest"]
        batch:
          type: "string"
          values: ["first_100", "second_400"]
        difficulty:
          type: "string"
          values: ["intermediate", "advanced"]
        enhanced:
          type: "boolean"
        conversation_format:
          type: "boolean"

  dialogue_format:
    id:
      type: "string"
      description: "对话记录的唯一标识符"
      required: true
      
    conversations:
      type: "array"
      description: "对话内容列表"
      items:
        type: "object"
        properties:
          from:
            type: "string"
            values: ["human", "gpt"]
          value:
            type: "string"
      required: true
      
    source:
      type: "string"
      description: "数据来源"
      required: true
      
    age_range:
      type: "string"
      description: "适用年龄段"
      required: true
      
    metadata:
      type: "object"
      description: "对话元数据信息"

# 使用指南
usage:
  loading:
    python: |
      from load_dataset import InfantLanguageDataset
      dataset = InfantLanguageDataset()
      main_data = dataset.get_main_dataset()
      
    pandas: |
      import pandas as pd
      import json
      with open('super_final_infant_language_development_dataset.json', 'r', encoding='utf-8') as f:
          data = json.load(f)
      df = pd.DataFrame(data)
      
  filtering:
    by_age: |
      age_0_3_data = dataset.filter_by_age('0-3years')
      
    by_type: |
      qa_data = dataset.filter_by_content_type('qa_pair')
      
    by_source: |
      qwen_data = dataset.filter_by_source('qwen_api')
      
  applications:
    - "对话模型训练"
    - "问答系统构建"
    - "智能育儿助手"
    - "教育内容生成"
    - "学术研究"

# 质量保证
quality_assurance:
  data_sources:
    - "权威专业教材《0-3岁婴幼儿语言发展与教育》"
    - "AI生成高质量对话（100%成功率）"
    - "专业文献和政府文档"
    
  processing_steps:
    - "OCR文字提取和清洗"
    - "AI对话生成和验证"
    - "多源数据整合"
    - "去重和质量过滤"
    - "格式标准化"
    
  validation:
    - "内容完整性检查"
    - "格式一致性验证"
    - "重复数据检测"
    - "专业术语准确性"

# 更新历史
version_history:
  "4.0.0":
    date: "2024-07-24"
    changes:
      - "整合500个Qwen生成的高质量对话"
      - "数据量增加到707条记录"
      - "添加数据增强和变体生成"
      - "完善元数据标注"
      
  "3.0.0":
    date: "2024-07-24"
    changes:
      - "首次集成AI生成对话"
      - "数据量增加到288条记录"
      - "添加对话训练格式"
      
  "2.0.0":
    date: "2024-07-23"
    changes:
      - "基于OCR提取内容扩展"
      - "添加专业文献数据"
      
  "1.0.0":
    date: "2024-07-22"
    changes:
      - "初始版本发布"
      - "21条手工创建的示例数据"

# 联系信息
contact:
  email: "<EMAIL>"
  github: "https://github.com/your-repo/infant-language-development-guidance"
  issues: "https://github.com/your-repo/infant-language-development-guidance/issues"
