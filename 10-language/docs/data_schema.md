# 婴幼儿语言发展指导数据集格式说明

## 数据集概述

本数据集专门用于训练支持婴幼儿语言发展指导的大语言模型，包含来自政府文档、专业文献和专家问答的高质量数据。

## 数据格式

数据集采用JSON Lines格式，每行包含一个完整的数据样本。

### 基本结构

```json
{
  "id": "dataset_000001",
  "source": "government|literature|qa",
  "category": "language_development",
  "age_range": "0-6months|6-12months|1-2years|2-3years|3-6years",
  "content_type": "guideline|knowledge|qa_pair",
  "title": "数据标题",
  "content": "数据内容",
  "metadata": {
    "original_source": "原始来源",
    "publish_date": "发布日期",
    "source_url": "来源链接",
    "keywords": ["关键词1", "关键词2"],
    "authors": ["作者1", "作者2"],
    "question_type": "问题类型",
    "difficulty": "难度等级",
    "focus_areas": ["重点领域1", "重点领域2"]
  }
}
```

## 字段说明

### 必填字段

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `id` | string | 唯一标识符 | "dataset_000001" |
| `source` | string | 数据来源类型 | "government", "literature", "qa" |
| `category` | string | 数据类别 | "language_development" |
| `age_range` | string | 适用年龄段 | "0-6months", "1-2years" |
| `content` | string | 主要内容 | "0-6个月婴儿语言发展特点..." |

### 可选字段

| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| `content_type` | string | 内容类型 | "guideline", "knowledge", "qa_pair" |
| `title` | string | 标题 | "婴儿语言发展里程碑" |
| `metadata` | object | 元数据信息 | 见下方详细说明 |

## 数据来源类型

### 1. government（政府文档）
- 国家卫健委发布的指导文件
- 教育部学前教育政策
- 疾控中心健康指导

### 2. literature（专业文献）
- 儿童发展心理学教材
- 语言学专业书籍
- 医学百科全书条目

### 3. qa（问答对）
- 专家回答的常见问题
- 基于专业知识生成的QA对
- 实际咨询案例整理

## 年龄段分类

| 代码 | 显示名称 | 年龄范围 | 发展特点 |
|------|----------|----------|----------|
| `0-6months` | 0-6个月 | 新生儿到6个月 | 听觉发展、发声练习 |
| `6-12months` | 6-12个月 | 6个月到1岁 | 咿呀学语、理解简单指令 |
| `1-2years` | 1-2岁 | 1岁到2岁 | 词汇积累、简单句表达 |
| `2-3years` | 2-3岁 | 2岁到3岁 | 语法发展、复杂句表达 |
| `3-6years` | 3-6岁 | 3岁到6岁 | 叙述能力、抽象概念理解 |

## 内容类型

### guideline（指导文件）
- 官方发布的指导原则
- 标准化的操作规范
- 政策解读文档

### knowledge（知识内容）
- 理论知识介绍
- 发展规律说明
- 专业概念解释

### qa_pair（问答对）
- 结构化的问题和答案
- 实用的指导建议
- 案例分析讨论

## 元数据字段

### 基本信息
- `original_source`: 原始数据来源
- `publish_date`: 发布或更新日期
- `source_url`: 原始链接地址
- `authors`: 作者或机构列表

### 内容标签
- `keywords`: 关键词列表，用于检索和分类
- `focus_areas`: 重点关注领域
- `question_type`: 问题类型（仅QA数据）
- `difficulty`: 难度等级（basic/intermediate/advanced）

## 数据质量标准

### 内容要求
1. **准确性**: 基于权威医学和教育资料
2. **实用性**: 提供可操作的指导建议
3. **完整性**: 包含必要的背景信息和具体方法
4. **适宜性**: 符合对应年龄段的发展特点

### 格式要求
1. **编码**: 统一使用UTF-8编码
2. **长度**: 内容长度在50-5000字符之间
3. **结构**: 遵循标准JSON格式
4. **一致性**: 术语使用保持一致

### 语言要求
1. **规范性**: 使用标准中文表达
2. **专业性**: 正确使用医学和教育术语
3. **可读性**: 语言简洁明了，易于理解
4. **安全性**: 避免可能误导的医疗建议

## 使用示例

### 政府文档示例
```json
{
  "id": "dataset_000001",
  "source": "government",
  "category": "language_development",
  "age_range": "0-6months",
  "content_type": "guideline",
  "title": "0-6个月婴儿发育指导",
  "content": "0-6个月是婴儿语言发展的基础阶段...",
  "metadata": {
    "original_source": "国家卫生健康委员会",
    "publish_date": "2023-01-15",
    "keywords": ["婴儿发育", "语言发展", "健康指导"]
  }
}
```

### QA对示例
```json
{
  "id": "dataset_000002",
  "source": "qa",
  "category": "language_development",
  "age_range": "1-2years",
  "content_type": "qa_pair",
  "title": "1-2岁孩子不说话怎么办？",
  "content": "1-2岁孩子语言发展存在个体差异...",
  "metadata": {
    "question_type": "problem_identification",
    "difficulty": "intermediate",
    "focus_areas": ["语言迟缓", "家庭指导"]
  }
}
```

## 版本信息

- **当前版本**: 1.0
- **创建日期**: 2024年
- **更新频率**: 根据需要不定期更新
- **兼容性**: 支持主流机器学习框架

## 注意事项

1. 本数据集仅供学习和研究使用
2. 不能替代专业医疗诊断和建议
3. 使用时请注意数据的时效性
4. 建议结合最新的专业指导使用
