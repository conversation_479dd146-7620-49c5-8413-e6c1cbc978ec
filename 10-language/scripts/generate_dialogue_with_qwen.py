#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Qwen API基于书籍内容生成对话数据集
"""

import json
import requests
import time
import random
from pathlib import Path
from typing import Dict, List
import logging
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class QwenDialogueGenerator:
    """使用Qwen API生成对话数据集"""
    
    def __init__(self, api_key: str, request_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"):
        self.api_key = api_key
        self.request_url = request_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 对话生成的提示模板
        self.dialogue_prompts = {
            "理论知识": """基于以下婴幼儿语言发展的理论知识，生成一段专业的对话。对话应该包含一个家长的问题和专家的详细回答。

理论内容：
{content}

请生成一段自然的对话，格式如下：
家长：[提出相关问题]
专家：[基于理论内容给出专业回答]

要求：
1. 家长的问题要贴近实际育儿场景
2. 专家的回答要专业、实用、易懂
3. 对话要自然流畅
4. 回答要基于提供的理论内容""",

            "实践指导": """基于以下婴幼儿语言发展的实践指导内容，生成一段实用的对话。对话应该包含一个家长的具体问题和专家的操作建议。

指导内容：
{content}

请生成一段自然的对话，格式如下：
家长：[提出具体的实践问题]
专家：[基于指导内容给出具体的操作建议]

要求：
1. 家长的问题要具体、实际
2. 专家的建议要可操作、有步骤
3. 对话要贴近真实咨询场景
4. 建议要基于提供的指导内容""",

            "发展里程碑": """基于以下婴幼儿语言发展里程碑的内容，生成一段关于发展评估的对话。

里程碑内容：
{content}

请生成一段自然的对话，格式如下：
家长：[询问孩子发展情况或评估标准]
专家：[基于里程碑内容解释发展标准和评估方法]

要求：
1. 家长的问题要关注孩子的具体发展情况
2. 专家的回答要包含评估标准和正常范围
3. 对话要有助于家长理解发展规律
4. 回答要基于提供的里程碑内容""",

            "问题解决": """基于以下婴幼儿语言发展问题的内容，生成一段关于问题识别和解决的对话。

问题内容：
{content}

请生成一段自然的对话，格式如下：
家长：[描述孩子的语言发展问题或担忧]
专家：[基于内容分析问题并提供解决方案]

要求：
1. 家长要描述具体的问题或担忧
2. 专家要分析问题原因并提供解决方案
3. 对话要有助于问题的识别和解决
4. 回答要基于提供的问题内容""",

            "一般内容": """基于以下婴幼儿语言发展的内容，生成一段教育性的对话。

内容：
{content}

请生成一段自然的对话，格式如下：
家长：[提出相关的教育问题]
专家：[基于内容给出教育建议]

要求：
1. 家长的问题要与内容相关
2. 专家的回答要教育性强、实用
3. 对话要自然、专业
4. 回答要基于提供的内容"""
        }
    
    def load_reference_data(self, filepath: str) -> List[Dict]:
        """加载参考数据"""
        logger.info(f"加载参考数据: {filepath}")
        
        references = []
        with open(filepath, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    references.append(json.loads(line))
        
        logger.info(f"加载了 {len(references)} 条参考数据")
        return references
    
    def call_qwen_api(self, prompt: str, model: str = "qwen-turbo") -> str:
        """调用Qwen API"""
        payload = {
            "model": model,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.7,
            "max_tokens": 1500
        }
        
        try:
            response = requests.post(
                self.request_url,
                headers=self.headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                return result['choices'][0]['message']['content']
            else:
                logger.error(f"API调用失败: {response.status_code}, {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"API调用异常: {e}")
            return None
    
    def generate_dialogue_from_reference(self, reference: Dict) -> Dict:
        """基于单个参考内容生成对话"""
        content = reference['desc']
        content_type = reference.get('content_type', '一般内容')
        
        # 选择合适的提示模板
        prompt_template = self.dialogue_prompts.get(content_type, self.dialogue_prompts['一般内容'])
        prompt = prompt_template.format(content=content)
        
        # 调用API生成对话
        dialogue_text = self.call_qwen_api(prompt)
        
        if dialogue_text:
            # 解析对话
            dialogue_data = self._parse_dialogue(dialogue_text, reference)
            return dialogue_data
        else:
            return None
    
    def _parse_dialogue(self, dialogue_text: str, reference: Dict) -> Dict:
        """解析生成的对话文本"""
        # 尝试分离家长和专家的对话
        lines = dialogue_text.strip().split('\n')
        
        parent_question = ""
        expert_answer = ""
        
        current_speaker = None
        current_content = []
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            if line.startswith('家长：') or line.startswith('家长:'):
                if current_speaker == 'expert' and current_content:
                    expert_answer = '\n'.join(current_content)
                current_speaker = 'parent'
                current_content = [line[3:].strip()]
            elif line.startswith('专家：') or line.startswith('专家:'):
                if current_speaker == 'parent' and current_content:
                    parent_question = '\n'.join(current_content)
                current_speaker = 'expert'
                current_content = [line[3:].strip()]
            else:
                if current_content:
                    current_content.append(line)
        
        # 处理最后一段内容
        if current_speaker == 'expert' and current_content:
            expert_answer = '\n'.join(current_content)
        elif current_speaker == 'parent' and current_content:
            parent_question = '\n'.join(current_content)
        
        # 如果解析失败，使用整个文本作为专家回答
        if not parent_question and not expert_answer:
            expert_answer = dialogue_text
            parent_question = f"请问关于{reference.get('age_range', '婴幼儿')}的语言发展，有什么需要注意的吗？"
        
        return {
            "id": f"dialogue_{random.randint(100000, 999999)}",
            "conversations": [
                {
                    "from": "human",
                    "value": parent_question
                },
                {
                    "from": "gpt", 
                    "value": expert_answer
                }
            ],
            "source": "book_content",
            "age_range": reference.get('age_range', '0-3岁'),
            "content_type": reference.get('content_type', '一般内容'),
            "page_number": reference.get('page_number', 0),
            "reference_title": reference.get('title', ''),
            "metadata": {
                "generated_from": "qwen_api",
                "reference_source": reference.get('source', ''),
                "original_content_length": len(reference.get('desc', ''))
            }
        }
    
    def generate_dialogues_batch(self, references: List[Dict], num_dialogues: int = 100, max_workers: int = 5) -> List[Dict]:
        """批量生成对话"""
        logger.info(f"开始批量生成 {num_dialogues} 个对话，使用 {max_workers} 个并发线程")
        
        # 随机选择参考内容
        selected_references = random.sample(references, min(num_dialogues, len(references)))
        
        dialogues = []
        failed_count = 0
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交任务
            future_to_ref = {
                executor.submit(self.generate_dialogue_from_reference, ref): ref 
                for ref in selected_references
            }
            
            # 收集结果
            for future in as_completed(future_to_ref):
                try:
                    dialogue = future.result()
                    if dialogue:
                        dialogues.append(dialogue)
                        logger.info(f"成功生成对话 {len(dialogues)}/{num_dialogues}")
                    else:
                        failed_count += 1
                        logger.warning(f"对话生成失败，失败计数: {failed_count}")
                    
                    # 添加延迟避免API限流
                    time.sleep(0.5)
                    
                except Exception as e:
                    failed_count += 1
                    logger.error(f"对话生成异常: {e}")
        
        logger.info(f"批量生成完成，成功: {len(dialogues)}, 失败: {failed_count}")
        return dialogues
    
    def save_dialogues(self, dialogues: List[Dict], filepath: str):
        """保存生成的对话"""
        logger.info(f"保存对话到: {filepath}")
        
        with open(filepath, 'w', encoding='utf-8') as f:
            for dialogue in dialogues:
                f.write(json.dumps(dialogue, ensure_ascii=False) + '\n')
        
        # 生成统计报告
        self._generate_dialogue_report(dialogues, filepath)
    
    def _generate_dialogue_report(self, dialogues: List[Dict], filepath: str):
        """生成对话统计报告"""
        report = {
            'total_dialogues': len(dialogues),
            'by_age_range': {},
            'by_content_type': {},
            'avg_question_length': 0,
            'avg_answer_length': 0,
            'generation_info': {
                'api_used': 'qwen',
                'total_generated': len(dialogues)
            }
        }
        
        question_lengths = []
        answer_lengths = []
        
        for dialogue in dialogues:
            # 按年龄段统计
            age_range = dialogue.get('age_range', 'unknown')
            report['by_age_range'][age_range] = report['by_age_range'].get(age_range, 0) + 1
            
            # 按内容类型统计
            content_type = dialogue.get('content_type', 'unknown')
            report['by_content_type'][content_type] = report['by_content_type'].get(content_type, 0) + 1
            
            # 长度统计
            conversations = dialogue.get('conversations', [])
            if len(conversations) >= 2:
                question_lengths.append(len(conversations[0].get('value', '')))
                answer_lengths.append(len(conversations[1].get('value', '')))
        
        if question_lengths:
            report['avg_question_length'] = sum(question_lengths) / len(question_lengths)
        if answer_lengths:
            report['avg_answer_length'] = sum(answer_lengths) / len(answer_lengths)
        
        # 保存报告
        report_file = filepath.replace('.jsonl', '_report.json')
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"对话统计报告已保存到: {report_file}")
        
        # 打印摘要
        print(f"\n=== 对话生成完成 ===")
        print(f"总对话数: {report['total_dialogues']}")
        print(f"平均问题长度: {report['avg_question_length']:.0f} 字符")
        print(f"平均回答长度: {report['avg_answer_length']:.0f} 字符")
        
        print(f"\n按年龄段分布:")
        for age_range, count in report['by_age_range'].items():
            print(f"  {age_range}: {count}")
        
        print(f"\n按内容类型分布:")
        for content_type, count in report['by_content_type'].items():
            print(f"  {content_type}: {count}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='使用Qwen API生成婴幼儿语言发展对话数据集')
    parser.add_argument('--api_key', required=True, help='Qwen API密钥')
    parser.add_argument('--reference_file', default='data/dialogue_generation/book_content_references.jsonl', help='参考数据文件路径')
    parser.add_argument('--output_file', default='data/dialogue_generation/qwen_generated_dialogues.jsonl', help='输出文件路径')
    parser.add_argument('--num_dialogues', type=int, default=50, help='生成对话数量')
    parser.add_argument('--max_workers', type=int, default=3, help='并发线程数')
    parser.add_argument('--model', default='qwen-turbo', help='使用的模型')
    
    args = parser.parse_args()
    
    # 创建生成器
    generator = QwenDialogueGenerator(args.api_key)
    
    # 加载参考数据
    references = generator.load_reference_data(args.reference_file)
    
    if not references:
        logger.error("没有加载到参考数据")
        return
    
    # 生成对话
    dialogues = generator.generate_dialogues_batch(
        references, 
        num_dialogues=args.num_dialogues,
        max_workers=args.max_workers
    )
    
    if dialogues:
        # 保存对话
        Path(args.output_file).parent.mkdir(parents=True, exist_ok=True)
        generator.save_dialogues(dialogues, args.output_file)
        
        print(f"\n🎉 对话生成完成！")
        print(f"生成了 {len(dialogues)} 个高质量对话")
        print(f"输出文件: {args.output_file}")
    else:
        logger.error("没有成功生成任何对话")

if __name__ == "__main__":
    main()
