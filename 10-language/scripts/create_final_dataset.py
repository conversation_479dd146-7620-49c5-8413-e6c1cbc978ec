#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建最终的完整数据集
"""

import json
import uuid
from pathlib import Path
from typing import Dict, List
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FinalDatasetCreator:
    """最终数据集创建器"""
    
    def __init__(self):
        self.output_dir = Path("data/final")
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def load_all_datasets(self) -> List[Dict]:
        """加载所有数据集"""
        logger.info("加载所有数据集...")
        
        all_data = []
        
        # 1. 加载基于完整书籍的综合数据集
        comprehensive_file = Path("data/raw/literature/comprehensive_dataset/comprehensive_dataset.json")
        if comprehensive_file.exists():
            with open(comprehensive_file, 'r', encoding='utf-8') as f:
                comprehensive_data = json.load(f)
                all_data.extend(comprehensive_data)
                logger.info(f"加载了综合数据集: {len(comprehensive_data)} 条")
        
        # 2. 加载政府数据
        gov_data_dir = Path("data/raw/government")
        if gov_data_dir.exists():
            for file_path in gov_data_dir.glob("*.json"):
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    if isinstance(data, list):
                        all_data.extend(data)
                    else:
                        all_data.append(data)
                    logger.info(f"加载了政府数据: {file_path.name}")
        
        # 3. 加载原始文献数据（作为补充）
        lit_data_file = Path("data/raw/literature/academic_literature.json")
        if lit_data_file.exists():
            with open(lit_data_file, 'r', encoding='utf-8') as f:
                lit_data = json.load(f)
                all_data.extend(lit_data)
                logger.info(f"加载了原始文献数据: {len(lit_data)} 条")
        
        # 4. 加载原始QA数据（作为补充）
        qa_data_file = Path("data/raw/qa_pairs/generated_qa.json")
        if qa_data_file.exists():
            with open(qa_data_file, 'r', encoding='utf-8') as f:
                qa_data = json.load(f)
                all_data.extend(qa_data)
                logger.info(f"加载了原始QA数据: {len(qa_data)} 条")
        
        logger.info(f"总共加载了 {len(all_data)} 条数据")
        return all_data
    
    def enhance_age_range_classification(self, data: List[Dict]) -> List[Dict]:
        """增强年龄段分类"""
        logger.info("增强年龄段分类...")
        
        enhanced_data = []
        
        for item in data:
            # 复制原始数据
            enhanced_item = item.copy()
            
            # 根据内容更精确地分类年龄段
            content = item.get('content', '') + ' ' + item.get('title', '')
            
            # 如果当前年龄段是0-3years，尝试更精确分类
            if item.get('age_range') == '0-3years':
                new_age_range = self._classify_age_range_from_content(content)
                if new_age_range != '0-3years':
                    enhanced_item['age_range'] = new_age_range
            
            enhanced_data.append(enhanced_item)
        
        return enhanced_data
    
    def _classify_age_range_from_content(self, content: str) -> str:
        """根据内容分类年龄段"""
        age_indicators = {
            '0-6months': [
                '0-3个月', '4-6个月', '新生儿', '婴儿期早期',
                '咕咕声', '元音', '哭声', '听觉反应'
            ],
            '6-12months': [
                '7-9个月', '10-12个月', '婴儿期晚期', 
                '咿呀学语', 'mama', 'baba', '第一个词'
            ],
            '1-2years': [
                '13-18个月', '19-24个月', '1-2岁', '幼儿期早期',
                '双词句', '词汇爆发', '50个词', '200个词'
            ],
            '2-3years': [
                '25-30个月', '31-36个月', '2-3岁', '幼儿期',
                '语法发展', '复杂句', '为什么', '是什么'
            ]
        }
        
        # 计算每个年龄段的匹配分数
        age_scores = {}
        for age_range, indicators in age_indicators.items():
            score = sum(1 for indicator in indicators if indicator in content)
            if score > 0:
                age_scores[age_range] = score
        
        # 返回得分最高的年龄段
        if age_scores:
            return max(age_scores.items(), key=lambda x: x[1])[0]
        
        return '0-3years'  # 默认返回
    
    def add_enhanced_qa_pairs(self, data: List[Dict]) -> List[Dict]:
        """添加增强的QA问答对"""
        logger.info("添加增强的QA问答对...")
        
        enhanced_qa = []
        
        # 基于现有数据生成更多QA对
        knowledge_items = [item for item in data if item.get('content_type') == 'knowledge']
        guidance_items = [item for item in data if item.get('content_type') == 'guidance_methods']
        
        # 为每个理论知识生成应用类问题
        for knowledge in knowledge_items:
            title = knowledge['title']
            content = knowledge['content']
            
            enhanced_qa.append({
                'id': str(uuid.uuid4()),
                'source': 'qa',
                'category': 'language_development',
                'age_range': knowledge['age_range'],
                'content_type': 'qa_pair',
                'title': f'{title}在实践中如何应用？',
                'content': f'{title}在婴幼儿语言教育实践中的应用：\n\n{content[:200]}...\n\n在实际教育中，我们可以运用这一理论来指导语言教育活动的设计和实施，为婴幼儿创造更好的语言发展环境。',
                'metadata': {
                    'question_type': 'application',
                    'difficulty': 'advanced',
                    'enhanced': True,
                    'source_book': '0-3岁婴幼儿语言发展与教育'
                }
            })
        
        # 为指导方法生成常见问题
        for guidance in guidance_items:
            title = guidance['title']
            content = guidance['content']
            age_range = guidance['age_range']
            
            method_name = title.replace('指导方法', '')
            
            enhanced_qa.append({
                'id': str(uuid.uuid4()),
                'source': 'qa',
                'category': 'language_development',
                'age_range': age_range,
                'content_type': 'qa_pair',
                'title': f'{method_name}需要注意什么？',
                'content': f'在进行{method_name}时，需要注意以下几个方面：\n\n{content[:300]}...\n\n通过注意这些要点，可以确保{method_name}的效果，更好地促进婴幼儿的语言发展。',
                'metadata': {
                    'question_type': 'precautions',
                    'difficulty': 'intermediate',
                    'enhanced': True,
                    'source_book': '0-3岁婴幼儿语言发展与教育'
                }
            })
        
        logger.info(f"添加了 {len(enhanced_qa)} 个增强QA问答对")
        return enhanced_qa
    
    def standardize_data_format(self, data: List[Dict]) -> List[Dict]:
        """标准化数据格式"""
        logger.info("标准化数据格式...")
        
        standardized_data = []
        
        for i, item in enumerate(data):
            # 确保必要字段存在
            standardized_item = {
                'id': item.get('id', f"final_dataset_{i:06d}"),
                'source': item.get('source', 'unknown'),
                'category': item.get('category', 'language_development'),
                'age_range': item.get('age_range', '0-3years'),
                'content_type': item.get('content_type', 'general'),
                'title': item.get('title', '').strip(),
                'content': self._clean_content(item.get('content', '')),
                'metadata': self._standardize_metadata(item.get('metadata', {}))
            }
            
            # 只保留有效内容的项目
            if standardized_item['content'] and len(standardized_item['content']) > 30:
                standardized_data.append(standardized_item)
        
        logger.info(f"标准化后保留 {len(standardized_data)} 条有效数据")
        return standardized_data
    
    def _clean_content(self, content: str) -> str:
        """清理内容"""
        if not content:
            return ""
        
        # 移除多余的空白字符
        content = ' '.join(content.split())
        
        # 移除OCR常见错误
        content = content.replace('届幼儿', '婴幼儿')
        content = content.replace('机幼儿', '婴幼儿')
        content = content.replace('要幼儿', '婴幼儿')
        
        return content.strip()
    
    def _standardize_metadata(self, metadata: Dict) -> Dict:
        """标准化元数据"""
        standardized_metadata = {
            'book_title': metadata.get('book_title', metadata.get('original_source', '')),
            'authors': metadata.get('authors', []),
            'page_number': metadata.get('page_number', metadata.get('page_range', '')),
            'keywords': metadata.get('keywords', []),
            'question_type': metadata.get('question_type', ''),
            'difficulty': metadata.get('difficulty', ''),
            'enhanced': metadata.get('enhanced', False),
            'source_book': metadata.get('source_book', ''),
            'publish_date': metadata.get('publish_date', ''),
            'source_url': metadata.get('source_url', '')
        }
        
        # 移除空值
        return {k: v for k, v in standardized_metadata.items() if v}
    
    def create_final_dataset(self) -> List[Dict]:
        """创建最终数据集"""
        logger.info("开始创建最终数据集...")
        
        # 1. 加载所有数据
        all_data = self.load_all_datasets()
        
        # 2. 增强年龄段分类
        enhanced_data = self.enhance_age_range_classification(all_data)
        
        # 3. 添加增强QA对
        enhanced_qa = self.add_enhanced_qa_pairs(enhanced_data)
        complete_data = enhanced_data + enhanced_qa
        
        # 4. 标准化格式
        final_data = self.standardize_data_format(complete_data)
        
        # 5. 去重（基于标题和内容的前100字符）
        unique_data = self._remove_duplicates(final_data)
        
        # 6. 重新分配ID
        for i, item in enumerate(unique_data):
            item['id'] = f"infant_lang_dev_{i:06d}"
        
        logger.info(f"最终数据集包含 {len(unique_data)} 条唯一记录")
        return unique_data
    
    def _remove_duplicates(self, data: List[Dict]) -> List[Dict]:
        """去除重复数据"""
        logger.info("去除重复数据...")
        
        seen_signatures = set()
        unique_data = []
        
        for item in data:
            # 创建唯一签名
            title = item.get('title', '')
            content_preview = item.get('content', '')[:100]
            signature = f"{title}_{content_preview}"
            
            if signature not in seen_signatures:
                seen_signatures.add(signature)
                unique_data.append(item)
        
        logger.info(f"去重后保留 {len(unique_data)} 条数据")
        return unique_data
    
    def save_final_dataset(self, dataset: List[Dict]):
        """保存最终数据集"""
        logger.info("保存最终数据集...")
        
        # 保存为JSON格式
        json_file = self.output_dir / "infant_language_development_dataset.json"
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, ensure_ascii=False, indent=2)
        
        # 保存为JSONL格式（用于训练）
        jsonl_file = self.output_dir / "infant_language_development_dataset.jsonl"
        with open(jsonl_file, 'w', encoding='utf-8') as f:
            for item in dataset:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        # 生成详细统计报告
        report = self._generate_final_report(dataset)
        
        # 保存报告
        report_file = self.output_dir / "final_dataset_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"最终数据集已保存:")
        logger.info(f"  JSON格式: {json_file}")
        logger.info(f"  JSONL格式: {jsonl_file}")
        logger.info(f"  统计报告: {report_file}")
        
        return report
    
    def _generate_final_report(self, dataset: List[Dict]) -> Dict:
        """生成最终报告"""
        report = {
            'dataset_info': {
                'total_records': len(dataset),
                'creation_date': '2024-07-24',
                'version': '2.0',
                'description': '基于《0-3岁婴幼儿语言发展与教育》完整OCR提取的专业数据集'
            },
            'data_sources': {
                'primary_book': '0-3岁婴幼儿语言发展与教育（袁萍、祝泽舟主编）',
                'government_docs': '国家卫健委、教育部政策文件',
                'academic_literature': '专业教材和百科全书',
                'generated_qa': '基于专业内容生成的问答对'
            },
            'distribution': {
                'by_source': {},
                'by_content_type': {},
                'by_age_range': {},
                'by_difficulty': {}
            },
            'quality_metrics': {
                'avg_content_length': 0,
                'min_content_length': 0,
                'max_content_length': 0,
                'enhanced_records': 0,
                'book_based_records': 0
            }
        }
        
        content_lengths = []
        
        for item in dataset:
            # 按来源分布
            source = item.get('source', 'unknown')
            report['distribution']['by_source'][source] = \
                report['distribution']['by_source'].get(source, 0) + 1
            
            # 按内容类型分布
            content_type = item.get('content_type', 'unknown')
            report['distribution']['by_content_type'][content_type] = \
                report['distribution']['by_content_type'].get(content_type, 0) + 1
            
            # 按年龄段分布
            age_range = item.get('age_range', 'unknown')
            report['distribution']['by_age_range'][age_range] = \
                report['distribution']['by_age_range'].get(age_range, 0) + 1
            
            # 按难度分布
            difficulty = item.get('metadata', {}).get('difficulty', '')
            if difficulty:
                report['distribution']['by_difficulty'][difficulty] = \
                    report['distribution']['by_difficulty'].get(difficulty, 0) + 1
            
            # 内容长度统计
            content_length = len(item.get('content', ''))
            content_lengths.append(content_length)
            
            # 统计增强记录
            if item.get('metadata', {}).get('enhanced'):
                report['quality_metrics']['enhanced_records'] += 1
            
            # 统计书籍记录
            if item.get('metadata', {}).get('source_book'):
                report['quality_metrics']['book_based_records'] += 1
        
        if content_lengths:
            report['quality_metrics'].update({
                'avg_content_length': sum(content_lengths) / len(content_lengths),
                'min_content_length': min(content_lengths),
                'max_content_length': max(content_lengths)
            })
        
        return report

def main():
    """主函数"""
    creator = FinalDatasetCreator()
    
    # 创建最终数据集
    final_dataset = creator.create_final_dataset()
    
    if final_dataset:
        # 保存最终数据集
        report = creator.save_final_dataset(final_dataset)
        
        # 显示结果
        print(f"\n🎉 最终数据集创建完成！")
        print(f"=" * 50)
        print(f"总记录数: {report['dataset_info']['total_records']}")
        print(f"版本: {report['dataset_info']['version']}")
        print(f"基于书籍的记录: {report['quality_metrics']['book_based_records']}")
        print(f"增强的记录: {report['quality_metrics']['enhanced_records']}")
        
        print(f"\n📊 数据分布:")
        print(f"按来源分布:")
        for source, count in report['distribution']['by_source'].items():
            print(f"  {source}: {count}")
        
        print(f"\n按内容类型分布:")
        for content_type, count in report['distribution']['by_content_type'].items():
            print(f"  {content_type}: {count}")
        
        print(f"\n按年龄段分布:")
        for age_range, count in report['distribution']['by_age_range'].items():
            print(f"  {age_range}: {count}")
        
        if report['distribution']['by_difficulty']:
            print(f"\n按难度分布:")
            for difficulty, count in report['distribution']['by_difficulty'].items():
                print(f"  {difficulty}: {count}")
        
        print(f"\n📈 质量指标:")
        print(f"平均内容长度: {report['quality_metrics']['avg_content_length']:.0f} 字符")
        print(f"内容长度范围: {report['quality_metrics']['min_content_length']}-{report['quality_metrics']['max_content_length']} 字符")
        
        print(f"\n🎯 数据集特点:")
        print(f"✅ 基于权威专业教材的完整OCR提取")
        print(f"✅ 涵盖理论知识、实践指导和问答对话")
        print(f"✅ 按年龄段科学分类，覆盖0-3岁全阶段")
        print(f"✅ 包含政府政策文件和学术文献")
        print(f"✅ 适用于LLM训练和专业应用开发")
    
    else:
        logger.error("最终数据集创建失败")

if __name__ == "__main__":
    main()
