#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于OCR提取的书籍内容生成专业数据集
"""

import json
import re
import uuid
from pathlib import Path
from typing import Dict, List
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BookDatasetGenerator:
    """基于书籍内容的数据集生成器"""
    
    def __init__(self):
        self.ocr_output_dir = Path("data/raw/literature/ocr_output")
        self.output_dir = Path("data/raw/literature/book_dataset")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
    def load_ocr_content(self) -> Dict:
        """加载OCR提取的内容"""
        logger.info("加载OCR提取的内容...")
        
        # 加载所有页面内容
        pages_file = self.ocr_output_dir / "ocr_all_pages.json"
        if not pages_file.exists():
            logger.error("OCR页面文件不存在")
            return {}
        
        with open(pages_file, 'r', encoding='utf-8') as f:
            pages_data = json.load(f)
        
        logger.info(f"加载了 {len(pages_data)} 页内容")
        return pages_data
    
    def extract_theoretical_content(self, pages_data: Dict) -> List[Dict]:
        """提取理论知识内容"""
        logger.info("提取理论知识内容...")
        
        theoretical_data = []
        
        # 从第50-54页提取的理论内容
        theory_pages = ['page_50', 'page_51', 'page_52', 'page_53', 'page_54']
        
        for page_key in theory_pages:
            if page_key in pages_data:
                page_data = pages_data[page_key]
                content = page_data['cleaned_text']
                
                # 提取社会交互作用理论相关内容
                if '社会交互作用理论' in content:
                    theoretical_data.append({
                        'id': str(uuid.uuid4()),
                        'source': 'literature',
                        'category': 'language_development',
                        'age_range': '0-3years',
                        'content_type': 'knowledge',
                        'title': '社会交互作用理论',
                        'content': self._extract_theory_section(content, '社会交互作用理论'),
                        'metadata': {
                            'book_title': '0-3岁婴幼儿语言发展与教育',
                            'authors': ['袁萍', '祝泽舟'],
                            'page_number': page_data['page_number'],
                            'keywords': ['社会交互作用', '语言发展理论', '儿童语言获得']
                        }
                    })
                
                # 提取语言功能相关内容
                if '语言的功能' in content:
                    theoretical_data.append({
                        'id': str(uuid.uuid4()),
                        'source': 'literature',
                        'category': 'language_development',
                        'age_range': '0-3years',
                        'content_type': 'knowledge',
                        'title': '语言的功能',
                        'content': self._extract_theory_section(content, '语言的功能'),
                        'metadata': {
                            'book_title': '0-3岁婴幼儿语言发展与教育',
                            'authors': ['袁萍', '祝泽舟'],
                            'page_number': page_data['page_number'],
                            'keywords': ['语言功能', '儿童语言', '语言发展']
                        }
                    })
                
                # 提取语言输入相关内容
                if '语言输入' in content:
                    theoretical_data.append({
                        'id': str(uuid.uuid4()),
                        'source': 'literature',
                        'category': 'language_development',
                        'age_range': '1-2years',
                        'content_type': 'knowledge',
                        'title': '语言输入与儿童早期词汇习得',
                        'content': self._extract_theory_section(content, '语言输入'),
                        'metadata': {
                            'book_title': '0-3岁婴幼儿语言发展与教育',
                            'authors': ['袁萍', '祝泽舟'],
                            'page_number': page_data['page_number'],
                            'keywords': ['语言输入', '词汇习得', '早期发展']
                        }
                    })
        
        logger.info(f"提取了 {len(theoretical_data)} 条理论知识")
        return theoretical_data
    
    def extract_evaluation_content(self, pages_data: Dict) -> List[Dict]:
        """提取评价指标内容"""
        logger.info("提取评价指标内容...")
        
        evaluation_data = []
        
        # 从第170-174页提取评价相关内容
        eval_pages = ['page_170', 'page_171', 'page_172', 'page_173', 'page_174']
        
        for page_key in eval_pages:
            if page_key in pages_data:
                page_data = pages_data[page_key]
                content = page_data['cleaned_text']
                
                # 提取0-3个月评价指标
                if '0一3个月婴幼儿语言发展评价指标' in content:
                    evaluation_data.append({
                        'id': str(uuid.uuid4()),
                        'source': 'literature',
                        'category': 'language_development',
                        'age_range': '0-6months',
                        'content_type': 'development_milestones',
                        'title': '0-3个月婴幼儿语言发展评价指标',
                        'content': self._extract_evaluation_table(content, '0一3个月'),
                        'metadata': {
                            'book_title': '0-3岁婴幼儿语言发展与教育',
                            'authors': ['袁萍', '祝泽舟'],
                            'page_number': page_data['page_number'],
                            'keywords': ['发展评价', '0-3个月', '语言里程碑']
                        }
                    })
                
                # 提取4-6个月评价指标
                if '4~ 6个月婴幼儿语言发展评价指标' in content:
                    evaluation_data.append({
                        'id': str(uuid.uuid4()),
                        'source': 'literature',
                        'category': 'language_development',
                        'age_range': '0-6months',
                        'content_type': 'development_milestones',
                        'title': '4-6个月婴幼儿语言发展评价指标',
                        'content': self._extract_evaluation_table(content, '4~ 6个月'),
                        'metadata': {
                            'book_title': '0-3岁婴幼儿语言发展与教育',
                            'authors': ['袁萍', '祝泽舟'],
                            'page_number': page_data['page_number'],
                            'keywords': ['发展评价', '4-6个月', '语言里程碑']
                        }
                    })
                
                # 提取7-9个月评价指标
                if '7~ 9个月' in content:
                    evaluation_data.append({
                        'id': str(uuid.uuid4()),
                        'source': 'literature',
                        'category': 'language_development',
                        'age_range': '6-12months',
                        'content_type': 'development_milestones',
                        'title': '7-9个月婴幼儿语言发展评价指标',
                        'content': self._extract_evaluation_table(content, '7~ 9个月'),
                        'metadata': {
                            'book_title': '0-3岁婴幼儿语言发展与教育',
                            'authors': ['袁萍', '祝泽舟'],
                            'page_number': page_data['page_number'],
                            'keywords': ['发展评价', '7-9个月', '语言里程碑']
                        }
                    })
                
                # 提取10-12个月评价指标
                if '10~ 12 个月' in content:
                    evaluation_data.append({
                        'id': str(uuid.uuid4()),
                        'source': 'literature',
                        'category': 'language_development',
                        'age_range': '6-12months',
                        'content_type': 'development_milestones',
                        'title': '10-12个月婴幼儿语言发展评价指标',
                        'content': self._extract_evaluation_table(content, '10~ 12 个月'),
                        'metadata': {
                            'book_title': '0-3岁婴幼儿语言发展与教育',
                            'authors': ['袁萍', '祝泽舟'],
                            'page_number': page_data['page_number'],
                            'keywords': ['发展评价', '10-12个月', '语言里程碑']
                        }
                    })
        
        logger.info(f"提取了 {len(evaluation_data)} 条评价指标")
        return evaluation_data
    
    def _extract_theory_section(self, content: str, theory_name: str) -> str:
        """提取理论章节内容"""
        # 查找理论名称的位置
        start_pos = content.find(theory_name)
        if start_pos == -1:
            return content[:500]  # 如果找不到，返回前500字符
        
        # 提取从理论名称开始的内容
        theory_content = content[start_pos:]
        
        # 限制长度并清理
        if len(theory_content) > 1000:
            theory_content = theory_content[:1000] + "..."
        
        return theory_content.strip()
    
    def _extract_evaluation_table(self, content: str, age_range: str) -> str:
        """提取评价表格内容"""
        # 查找年龄段的位置
        start_pos = content.find(age_range)
        if start_pos == -1:
            return content[:500]
        
        # 提取相关内容
        eval_content = content[start_pos:]
        
        # 查找下一个年龄段或章节，作为结束位置
        next_sections = ['个月婴幼儿语言发展评价指标', '第二节', '第三节']
        end_pos = len(eval_content)
        
        for section in next_sections:
            pos = eval_content.find(section, 50)  # 从50字符后开始查找
            if pos != -1 and pos < end_pos:
                end_pos = pos
        
        eval_content = eval_content[:end_pos]
        
        # 限制长度
        if len(eval_content) > 800:
            eval_content = eval_content[:800] + "..."
        
        return eval_content.strip()
    
    def generate_qa_from_book_content(self, theoretical_data: List[Dict], evaluation_data: List[Dict]) -> List[Dict]:
        """基于书籍内容生成QA问答对"""
        logger.info("基于书籍内容生成QA问答对...")
        
        qa_data = []
        
        # 基于理论内容生成问答对
        for theory in theoretical_data:
            title = theory['title']
            content = theory['content']
            
            if '社会交互作用理论' in title:
                qa_data.append({
                    'id': str(uuid.uuid4()),
                    'source': 'qa',
                    'category': 'language_development',
                    'age_range': '0-3years',
                    'content_type': 'qa_pair',
                    'title': '什么是社会交互作用理论？',
                    'content': f'社会交互作用理论是语言发展的重要理论之一。{content[:300]}...\n\n这一理论强调语言发展受到多种因素的影响，包括社会、生物发展成熟、认知等因素相互依赖、共同作用。',
                    'metadata': {
                        'question_type': 'theory_explanation',
                        'difficulty': 'advanced',
                        'source_book': '0-3岁婴幼儿语言发展与教育'
                    }
                })
            
            if '语言的功能' in title:
                qa_data.append({
                    'id': str(uuid.uuid4()),
                    'source': 'qa',
                    'category': 'language_development',
                    'age_range': '0-3years',
                    'content_type': 'qa_pair',
                    'title': '儿童语言有哪些功能？',
                    'content': f'根据英国语言学家韩礼德的研究，儿童语言具有7种主要功能：\n\n1. 工具功能：使用语言获得所需要的东西\n2. 调节功能：通过语言调节、控制别人的行为\n3. 相互作用功能：使用语言与周围的人交往\n4. 表达个人功能：表达自己的思想情感\n5. 认识功能：用语言探索周围环境\n6. 想象功能：用语言创造假想世界\n7. 传达功能：向别人传达信息\n\n这些功能的实现帮助儿童表达意思和满足需要。',
                    'metadata': {
                        'question_type': 'knowledge_explanation',
                        'difficulty': 'intermediate',
                        'source_book': '0-3岁婴幼儿语言发展与教育'
                    }
                })
        
        # 基于评价指标生成问答对
        for eval_item in evaluation_data:
            title = eval_item['title']
            content = eval_item['content']
            age_range = eval_item['age_range']
            
            if '0-3个月' in title:
                qa_data.append({
                    'id': str(uuid.uuid4()),
                    'source': 'qa',
                    'category': 'language_development',
                    'age_range': '0-6months',
                    'content_type': 'qa_pair',
                    'title': '0-3个月婴儿的语言发展有哪些特点？',
                    'content': f'0-3个月婴儿的语言发展主要表现在以下几个方面：\n\n语言理解能力：\n- 对说话声很敏感，尤其对高音敏感\n- 听到新异的声音会停下正在做的动作\n- 开始将声音和形象联系起来，试图找出声音的来源\n\n语言表达能力：\n- 对养育者逗引有反应，会发出"咕咕"声\n- 会发简单的元音，如"a"、"o"、"e"音\n- 听到养育者的声音时会露出笑脸\n\n语言运用能力：\n- 用表情、动作、自发声音等表示自己的身体情绪状态\n- 不同类型的哭声代表不同的意思',
                    'metadata': {
                        'question_type': 'milestone',
                        'difficulty': 'basic',
                        'source_book': '0-3岁婴幼儿语言发展与教育'
                    }
                })
            
            if '4-6个月' in title:
                qa_data.append({
                    'id': str(uuid.uuid4()),
                    'source': 'qa',
                    'category': 'language_development',
                    'age_range': '0-6months',
                    'content_type': 'qa_pair',
                    'title': '4-6个月婴儿的语言发展有什么进步？',
                    'content': f'4-6个月婴儿的语言发展相比前期有明显进步：\n\n语言理解能力：\n- 养育者跟孩子说话时，能停止哭泣\n- 能够持续注意，并且能找寻声音的来源\n- 能区别养育者不同语调、语气、音色变化并做出相应反应\n\n语言表达能力：\n- 口中经常发出成串的语音，如"boboba"、"dododo"等\n- 发出长音、会尖叫\n- 咿呀学语，开始发辅音，如"d"、"n"、"m"等\n\n语言运用能力：\n- 开始注意看图书，常抓起书试着放进嘴里\n- 用语音来吸引养育者的注意',
                    'metadata': {
                        'question_type': 'milestone',
                        'difficulty': 'basic',
                        'source_book': '0-3岁婴幼儿语言发展与教育'
                    }
                })
        
        logger.info(f"生成了 {len(qa_data)} 个基于书籍内容的QA对")
        return qa_data
    
    def generate_complete_dataset(self) -> List[Dict]:
        """生成完整的数据集"""
        logger.info("开始生成基于书籍内容的完整数据集...")
        
        # 加载OCR内容
        pages_data = self.load_ocr_content()
        if not pages_data:
            return []
        
        # 提取理论内容
        theoretical_data = self.extract_theoretical_content(pages_data)
        
        # 提取评价指标
        evaluation_data = self.extract_evaluation_content(pages_data)
        
        # 生成QA问答对
        qa_data = self.generate_qa_from_book_content(theoretical_data, evaluation_data)
        
        # 合并所有数据
        complete_dataset = theoretical_data + evaluation_data + qa_data
        
        logger.info(f"完整数据集包含 {len(complete_dataset)} 条记录")
        return complete_dataset
    
    def save_dataset(self, dataset: List[Dict]):
        """保存数据集"""
        logger.info("保存基于书籍内容的数据集...")
        
        # 保存为JSON格式
        output_file = self.output_dir / "book_based_dataset.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, ensure_ascii=False, indent=2)
        
        # 生成统计报告
        report = {
            'total_records': len(dataset),
            'by_content_type': {},
            'by_age_range': {},
            'by_source': {}
        }
        
        for item in dataset:
            # 按内容类型统计
            content_type = item.get('content_type', 'unknown')
            report['by_content_type'][content_type] = report['by_content_type'].get(content_type, 0) + 1
            
            # 按年龄段统计
            age_range = item.get('age_range', 'unknown')
            report['by_age_range'][age_range] = report['by_age_range'].get(age_range, 0) + 1
            
            # 按来源统计
            source = item.get('source', 'unknown')
            report['by_source'][source] = report['by_source'].get(source, 0) + 1
        
        # 保存报告
        report_file = self.output_dir / "dataset_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"数据集已保存到 {output_file}")
        logger.info(f"统计报告已保存到 {report_file}")
        
        return report

def main():
    """主函数"""
    generator = BookDatasetGenerator()
    
    # 生成数据集
    dataset = generator.generate_complete_dataset()
    
    if dataset:
        # 保存数据集
        report = generator.save_dataset(dataset)
        
        # 显示结果
        print(f"\n=== 基于书籍内容的数据集生成完成 ===")
        print(f"总记录数: {report['total_records']}")
        print(f"\n按内容类型分布:")
        for content_type, count in report['by_content_type'].items():
            print(f"  {content_type}: {count}")
        print(f"\n按年龄段分布:")
        for age_range, count in report['by_age_range'].items():
            print(f"  {age_range}: {count}")
        print(f"\n按来源分布:")
        for source, count in report['by_source'].items():
            print(f"  {source}: {count}")
        
        # 显示样本内容
        print(f"\n=== 数据样本 ===")
        for i, item in enumerate(dataset[:3]):
            print(f"\n{i+1}. {item['title']}")
            print(f"   类型: {item['content_type']}")
            print(f"   年龄段: {item['age_range']}")
            print(f"   内容: {item['content'][:100]}...")
    
    else:
        logger.error("数据集生成失败")

if __name__ == "__main__":
    main()
