#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于OCR的PDF内容提取脚本 - 提取《0-3岁婴幼儿语言发展与教育》书籍内容
"""

import pytesseract
from pdf2image import convert_from_path
from PIL import Image
import json
import re
import os
from pathlib import Path
import logging
from typing import List, Dict

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OCRPDFExtractor:
    """基于OCR的PDF内容提取器"""
    
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.output_dir = Path("data/raw/literature/ocr_output")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置Tesseract路径（macOS Homebrew安装路径）
        pytesseract.pytesseract.tesseract_cmd = '/opt/homebrew/bin/tesseract'
        
    def convert_pdf_to_images(self, start_page: int = 1, end_page: int = None) -> List[Image.Image]:
        """将PDF转换为图像"""
        logger.info(f"将PDF转换为图像，页面范围: {start_page}-{end_page or '末页'}")
        
        try:
            # 转换PDF为图像，设置较高的DPI以提高OCR准确性
            images = convert_from_path(
                self.pdf_path,
                dpi=300,  # 高DPI提高OCR准确性
                first_page=start_page,
                last_page=end_page,
                fmt='jpeg'
            )
            
            logger.info(f"成功转换 {len(images)} 页为图像")
            return images
            
        except Exception as e:
            logger.error(f"PDF转图像失败: {e}")
            return []
    
    def extract_text_from_image(self, image: Image.Image, page_num: int) -> str:
        """从单个图像提取文字"""
        try:
            # 使用中英文混合识别
            # 'chi_sim+eng' 表示简体中文和英文
            text = pytesseract.image_to_string(
                image, 
                lang='chi_sim+eng',
                config='--psm 6'  # 假设是统一的文本块
            )
            
            logger.debug(f"第{page_num}页提取文字长度: {len(text)}")
            return text
            
        except Exception as e:
            logger.warning(f"第{page_num}页OCR识别失败: {e}")
            return ""
    
    def clean_ocr_text(self, text: str) -> str:
        """清理OCR识别的文字"""
        if not text:
            return ""
        
        # 移除多余的空白字符
        text = re.sub(r'\s+', ' ', text)
        
        # 移除常见的OCR错误字符
        text = re.sub(r'[|｜]', '', text)  # 移除竖线
        text = re.sub(r'[_—-]{3,}', '', text)  # 移除长横线
        
        # 修复常见的中文OCR错误
        ocr_corrections = {
            '语言发屐': '语言发展',
            '语言发屣': '语言发展',
            '语言发屡': '语言发展',
            '婴幼儿': '婴幼儿',
            '发屐': '发展',
            '发屣': '发展',
            '发屡': '发展',
            '儿童': '儿童',
            '教育': '教育',
            '学习': '学习',
            '能力': '能力',
            '培养': '培养',
            '方法': '方法',
            '训练': '训练',
            '指导': '指导',
        }
        
        for wrong, correct in ocr_corrections.items():
            text = text.replace(wrong, correct)
        
        return text.strip()
    
    def extract_sample_pages(self, sample_pages: List[int] = None) -> Dict[str, str]:
        """提取样本页面进行测试"""
        if sample_pages is None:
            # 默认提取前5页、中间5页、后5页
            sample_pages = [1, 2, 3, 4, 5, 50, 51, 52, 53, 54, 170, 171, 172, 173, 174]
        
        logger.info(f"提取样本页面: {sample_pages}")
        
        extracted_texts = {}
        
        for page_num in sample_pages:
            try:
                # 转换单页
                images = self.convert_pdf_to_images(start_page=page_num, end_page=page_num)
                
                if images:
                    # OCR识别
                    raw_text = self.extract_text_from_image(images[0], page_num)
                    
                    # 清理文字
                    cleaned_text = self.clean_ocr_text(raw_text)
                    
                    if cleaned_text and len(cleaned_text) > 50:  # 只保留有意义的内容
                        extracted_texts[f"page_{page_num}"] = {
                            'page_number': page_num,
                            'raw_text': raw_text,
                            'cleaned_text': cleaned_text,
                            'text_length': len(cleaned_text)
                        }
                        
                        logger.info(f"第{page_num}页提取成功，文字长度: {len(cleaned_text)}")
                    else:
                        logger.warning(f"第{page_num}页内容太少或为空")
                
            except Exception as e:
                logger.error(f"处理第{page_num}页时出错: {e}")
                continue
        
        return extracted_texts
    
    def extract_full_book(self, batch_size: int = 10) -> Dict[str, str]:
        """提取整本书的内容（分批处理）"""
        logger.info("开始提取整本书内容...")
        
        all_texts = {}
        
        # 先获取总页数
        try:
            sample_images = self.convert_pdf_to_images(start_page=1, end_page=1)
            if not sample_images:
                logger.error("无法获取PDF页数")
                return {}
        except Exception as e:
            logger.error(f"获取PDF信息失败: {e}")
            return {}
        
        # 分批处理页面
        total_pages = 180  # 根据之前的信息，这个PDF有180页
        
        for start_page in range(1, total_pages + 1, batch_size):
            end_page = min(start_page + batch_size - 1, total_pages)
            
            logger.info(f"处理页面 {start_page}-{end_page}")
            
            try:
                # 转换批次页面
                images = self.convert_pdf_to_images(start_page=start_page, end_page=end_page)
                
                for i, image in enumerate(images):
                    page_num = start_page + i
                    
                    # OCR识别
                    raw_text = self.extract_text_from_image(image, page_num)
                    cleaned_text = self.clean_ocr_text(raw_text)
                    
                    if cleaned_text and len(cleaned_text) > 30:
                        all_texts[f"page_{page_num}"] = {
                            'page_number': page_num,
                            'cleaned_text': cleaned_text,
                            'text_length': len(cleaned_text)
                        }
                
            except Exception as e:
                logger.error(f"处理页面 {start_page}-{end_page} 时出错: {e}")
                continue
        
        logger.info(f"完成整本书提取，共提取 {len(all_texts)} 页有效内容")
        return all_texts
    
    def identify_chapters(self, texts: Dict[str, str]) -> List[Dict]:
        """识别章节结构"""
        logger.info("识别章节结构...")
        
        chapters = []
        
        # 章节标题的常见模式
        chapter_patterns = [
            r'第[一二三四五六七八九十\d]+章[^\n]{0,50}',
            r'第[一二三四五六七八九十\d]+节[^\n]{0,50}',
            r'\d+\.\d+[^\n]{5,50}',
            r'[一二三四五六七八九十]、[^\n]{5,50}',
            r'（[一二三四五六七八九十]）[^\n]{5,50}',
        ]
        
        for page_key in sorted(texts.keys(), key=lambda x: int(x.split('_')[1])):
            page_data = texts[page_key]
            text = page_data['cleaned_text']
            page_num = page_data['page_number']
            
            for pattern in chapter_patterns:
                matches = re.findall(pattern, text)
                for match in matches:
                    if len(match.strip()) > 3:  # 过滤太短的匹配
                        chapters.append({
                            'title': match.strip(),
                            'page_number': page_num,
                            'content_preview': text[:200] + '...' if len(text) > 200 else text
                        })
        
        # 去重
        seen_titles = set()
        unique_chapters = []
        for chapter in chapters:
            if chapter['title'] not in seen_titles:
                seen_titles.add(chapter['title'])
                unique_chapters.append(chapter)
        
        logger.info(f"识别到 {len(unique_chapters)} 个章节")
        return unique_chapters
    
    def save_extracted_content(self, texts: Dict[str, str], chapters: List[Dict]):
        """保存提取的内容"""
        logger.info("保存提取的内容...")
        
        # 保存所有页面文本
        with open(self.output_dir / 'ocr_all_pages.json', 'w', encoding='utf-8') as f:
            json.dump(texts, f, ensure_ascii=False, indent=2)
        
        # 保存章节信息
        with open(self.output_dir / 'ocr_chapters.json', 'w', encoding='utf-8') as f:
            json.dump(chapters, f, ensure_ascii=False, indent=2)
        
        # 保存合并的文本
        combined_text = ""
        for page_key in sorted(texts.keys(), key=lambda x: int(x.split('_')[1])):
            page_data = texts[page_key]
            combined_text += f"\n--- 第{page_data['page_number']}页 ---\n"
            combined_text += page_data['cleaned_text'] + "\n"
        
        with open(self.output_dir / 'ocr_combined_text.txt', 'w', encoding='utf-8') as f:
            f.write(combined_text)
        
        logger.info("内容保存完成")
        
        # 生成统计报告
        total_chars = sum(data['text_length'] for data in texts.values())
        report = {
            'total_pages_extracted': len(texts),
            'total_characters': total_chars,
            'average_chars_per_page': total_chars / len(texts) if texts else 0,
            'chapters_found': len(chapters),
            'extraction_date': str(Path().cwd())
        }
        
        with open(self.output_dir / 'extraction_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report

def main():
    """主函数"""
    pdf_path = "0-3岁婴幼儿语言发展与教育 (袁萍，祝泽舟主编；张敏，乔芳玲，张建国副主编, 袁萍, 祝泽舟主编, 袁萍, 祝泽舟) (Z-Library)-已压缩.pdf"
    
    if not Path(pdf_path).exists():
        logger.error(f"PDF文件不存在: {pdf_path}")
        return
    
    extractor = OCRPDFExtractor(pdf_path)
    
    # 选择提取模式
    print("请选择提取模式:")
    print("1. 样本页面提取（快速测试）")
    print("2. 全书提取（耗时较长）")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        # 样本页面提取
        logger.info("开始样本页面提取...")
        texts = extractor.extract_sample_pages()
    elif choice == "2":
        # 全书提取
        logger.info("开始全书提取...")
        texts = extractor.extract_full_book()
    else:
        logger.info("默认进行样本页面提取...")
        texts = extractor.extract_sample_pages()
    
    if texts:
        # 识别章节
        chapters = extractor.identify_chapters(texts)
        
        # 保存结果
        report = extractor.save_extracted_content(texts, chapters)
        
        # 显示结果
        print(f"\n=== 提取完成 ===")
        print(f"提取页面数: {report['total_pages_extracted']}")
        print(f"总字符数: {report['total_characters']}")
        print(f"平均每页字符数: {report['average_chars_per_page']:.0f}")
        print(f"识别章节数: {report['chapters_found']}")
        
        # 显示前几个章节
        if chapters:
            print(f"\n=== 识别的章节 ===")
            for i, chapter in enumerate(chapters[:10]):
                print(f"{i+1}. {chapter['title']} (第{chapter['page_number']}页)")
        
        # 显示部分内容
        if texts:
            first_page = list(texts.values())[0]
            print(f"\n=== 内容示例（第{first_page['page_number']}页前200字符）===")
            print(first_page['cleaned_text'][:200] + "...")
    
    else:
        logger.error("OCR提取失败，没有获得有效内容")

if __name__ == "__main__":
    main()
