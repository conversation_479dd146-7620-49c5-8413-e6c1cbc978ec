#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen API调用诊断工具
用于诊断和解决API调用异常问题
"""

import requests
import json
import time
from datetime import datetime
import sys
import os

# 添加项目路径
sys.path.append('../data_generation')

class QwenAPIDiagnostics:
    """Qwen API诊断工具"""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.api_url = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    def test_basic_connection(self):
        """测试基础连接"""
        print("🔍 测试1: 基础API连接")
        print("-" * 40)
        
        simple_payload = {
            "model": "qwen-turbo",
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": "你好，请简单回复'连接成功'"
                    }
                ]
            },
            "parameters": {
                "max_tokens": 50
            }
        }
        
        try:
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=simple_payload,
                timeout=30
            )
            
            print(f"状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 连接成功!")
                print(f"响应内容: {result}")
                return True
            else:
                print(f"❌ 连接失败")
                print(f"错误内容: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 连接异常: {str(e)}")
            return False
    
    def test_model_availability(self):
        """测试不同模型的可用性"""
        print("\n🔍 测试2: 模型可用性")
        print("-" * 40)
        
        models = [
            "qwen-turbo",
            "qwen-plus",
            "qwen-max",
            "qwen-plus-2025-04-28"
        ]
        
        test_payload_base = {
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": "测试"
                    }
                ]
            },
            "parameters": {
                "max_tokens": 10
            }
        }
        
        available_models = []
        
        for model in models:
            print(f"测试模型: {model}")
            payload = test_payload_base.copy()
            payload["model"] = model
            
            try:
                response = requests.post(
                    self.api_url,
                    headers=self.headers,
                    json=payload,
                    timeout=30
                )
                
                if response.status_code == 200:
                    print(f"  ✅ {model} 可用")
                    available_models.append(model)
                else:
                    print(f"  ❌ {model} 不可用 ({response.status_code})")
                    
            except Exception as e:
                print(f"  ❌ {model} 测试异常: {str(e)}")
        
        print(f"\n可用模型: {available_models}")
        return available_models
    
    def test_rate_limits(self):
        """测试API调用频率限制"""
        print("\n🔍 测试3: API频率限制")
        print("-" * 40)
        
        payload = {
            "model": "qwen-turbo",
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": "简短回复"
                    }
                ]
            },
            "parameters": {
                "max_tokens": 10
            }
        }
        
        success_count = 0
        rate_limit_count = 0
        
        print("连续发送10个请求测试频率限制...")
        
        for i in range(10):
            try:
                start_time = time.time()
                response = requests.post(
                    self.api_url,
                    headers=self.headers,
                    json=payload,
                    timeout=30
                )
                end_time = time.time()
                
                if response.status_code == 200:
                    success_count += 1
                    print(f"  请求 {i+1}: ✅ 成功 ({end_time - start_time:.2f}s)")
                elif response.status_code == 429:
                    rate_limit_count += 1
                    print(f"  请求 {i+1}: ⚠️  频率限制")
                else:
                    print(f"  请求 {i+1}: ❌ 失败 ({response.status_code})")
                
                time.sleep(0.5)  # 短暂等待
                
            except Exception as e:
                print(f"  请求 {i+1}: ❌ 异常 {str(e)}")
        
        print(f"\n结果: 成功 {success_count}/10, 频率限制 {rate_limit_count}/10")
        
        if rate_limit_count > 0:
            print("⚠️  检测到频率限制，建议增加请求间隔")
        
        return success_count, rate_limit_count
    
    def test_large_prompt(self):
        """测试大型prompt处理"""
        print("\n🔍 测试4: 大型Prompt处理")
        print("-" * 40)
        
        # 创建一个较大的prompt
        large_prompt = "请分析以下儿童发音问题：" + "这是一个测试内容。" * 200
        
        payload = {
            "model": "qwen-turbo",
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": large_prompt
                    }
                ]
            },
            "parameters": {
                "max_tokens": 100
            }
        }
        
        print(f"Prompt长度: {len(large_prompt)} 字符")
        
        try:
            response = requests.post(
                self.api_url,
                headers=self.headers,
                json=payload,
                timeout=60
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 大型Prompt处理成功")
                return True
            else:
                print(f"❌ 大型Prompt处理失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 大型Prompt处理异常: {str(e)}")
            return False
    
    def test_network_stability(self, test_count: int = 5):
        """测试网络稳定性"""
        print(f"\n🔍 测试5: 网络稳定性 ({test_count}次)")
        print("-" * 40)
        
        payload = {
            "model": "qwen-turbo",
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": "网络稳定性测试"
                    }
                ]
            },
            "parameters": {
                "max_tokens": 20
            }
        }
        
        success_times = []
        failures = 0
        
        for i in range(test_count):
            try:
                start_time = time.time()
                response = requests.post(
                    self.api_url,
                    headers=self.headers,
                    json=payload,
                    timeout=30
                )
                end_time = time.time()
                
                if response.status_code == 200:
                    response_time = end_time - start_time
                    success_times.append(response_time)
                    print(f"  测试 {i+1}: ✅ 成功 ({response_time:.2f}s)")
                else:
                    failures += 1
                    print(f"  测试 {i+1}: ❌ 失败 ({response.status_code})")
                
                time.sleep(2)  # 等待间隔
                
            except Exception as e:
                failures += 1
                print(f"  测试 {i+1}: ❌ 异常 {str(e)}")
        
        if success_times:
            avg_time = sum(success_times) / len(success_times)
            min_time = min(success_times)
            max_time = max(success_times)
            
            print(f"\n网络稳定性统计:")
            print(f"  成功率: {len(success_times)}/{test_count} ({len(success_times)/test_count*100:.1f}%)")
            print(f"  平均响应时间: {avg_time:.2f}s")
            print(f"  最快响应: {min_time:.2f}s")
            print(f"  最慢响应: {max_time:.2f}s")
        else:
            print("❌ 所有网络测试都失败了")
        
        return len(success_times), failures
    
    def generate_diagnostic_report(self):
        """生成完整的诊断报告"""
        print("🚀 Qwen API 完整诊断报告")
        print("=" * 60)
        print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"API密钥: {self.api_key[:10]}...{self.api_key[-4:]}")
        
        # 执行所有测试
        basic_ok = self.test_basic_connection()
        available_models = self.test_model_availability()
        success_rate, rate_limits = self.test_rate_limits()
        large_prompt_ok = self.test_large_prompt()
        network_success, network_failures = self.test_network_stability()
        
        # 生成建议
        print("\n📋 诊断总结和建议")
        print("=" * 60)
        
        if not basic_ok:
            print("❌ 基础连接失败 - 请检查API密钥和网络连接")
        
        if not available_models:
            print("❌ 没有可用模型 - 请检查API权限")
        elif "qwen-plus-2025-04-28" not in available_models:
            print("⚠️  指定模型不可用，建议使用: " + ", ".join(available_models))
        
        if rate_limits > 2:
            print("⚠️  频率限制较多，建议增加请求间隔到3-5秒")
        
        if not large_prompt_ok:
            print("⚠️  大型Prompt处理有问题，建议缩短prompt长度")
        
        if network_failures > network_success:
            print("⚠️  网络不稳定，建议增加重试次数和等待时间")
        
        # 推荐配置
        print("\n🔧 推荐配置")
        print("-" * 40)
        
        if available_models:
            recommended_model = available_models[0]
            print(f"推荐模型: {recommended_model}")
        
        recommended_interval = max(2, rate_limits)
        print(f"推荐请求间隔: {recommended_interval} 秒")
        
        recommended_retries = max(3, network_failures)
        print(f"推荐重试次数: {recommended_retries}")
        
        recommended_timeout = 60 if network_failures > 2 else 30
        print(f"推荐超时时间: {recommended_timeout} 秒")

def main():
    """主函数"""
    api_key = "sk-5eba46fbcff649d5bf28313bc865de10"  # 使用您的API密钥
    
    if not api_key or api_key == "YOUR_API_KEY_HERE":
        print("❌ 请设置正确的API密钥")
        return
    
    diagnostics = QwenAPIDiagnostics(api_key)
    diagnostics.generate_diagnostic_report()

if __name__ == "__main__":
    main()
