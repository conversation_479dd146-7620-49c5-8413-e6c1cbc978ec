#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Qwen API的发音纠正指导生成器
生成真实的发音错误示例，并使用LLM生成专业指导意见
"""

import json
import random
import requests
import time
import os
from typing import Dict, List, Any, Optional
from datetime import datetime
import hashlib

class QwenPronunciationGenerator:
    """基于Qwen API的发音纠正指导生成器"""
    
    def __init__(self, api_key: str, api_url: str = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"):
        self.api_key = api_key
        self.api_url = api_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 0-3岁常用词汇和发音错误模式（基于专业语音发育研究）
        self.pronunciation_data = {
            "12_months": {
                "words": ["妈妈", "爸爸", "奶奶", "爷爷", "拜拜", "抱抱", "水水", "吃吃", "走走", "坐坐", "要要", "不不"],
                "common_errors": {
                    "妈妈": ["na na", "ma ma(平声)", "ba ba", "wa wa"],
                    "爸爸": ["ba ba(平声)", "pa pa", "da da", "ma ma"],
                    "奶奶": ["nai nai(平声)", "dai dai", "lai lai", "ai ai"],
                    "爷爷": ["ye ye(平声)", "de de", "le le", "ai ai"],
                    "水水": ["dui dui", "sui sui", "tu tu", "zi zi"],
                    "抱抱": ["bao bao(平声)", "pao pao", "dao dao", "ao ao"],
                    "拜拜": ["bai bai(平声)", "pai pai", "dai dai", "ai ai"],
                    "吃吃": ["ci ci", "ti ti", "di di", "zi zi"],
                    "走走": ["dou dou", "zou zou(平声)", "tou tou", "gou gou"],
                    "坐坐": ["duo duo", "zuo zuo(平声)", "tuo tuo", "co co"],
                    "要要": ["yao yao(平声)", "dao dao", "lao lao", "ao ao"],
                    "不不": ["bu bu(平声)", "pu pu", "du du", "wu wu"]
                }
            },
            "18_months": {
                "words": ["苹果", "香蕉", "汽车", "小狗", "小猫", "谢谢", "再见", "睡觉", "起来", "回来", "出去", "进来", "开门", "关门"],
                "common_errors": {
                    "苹果": ["ping o", "bin guo", "ping duo", "bing guo", "ping wo"],
                    "香蕉": ["siang jiao", "xiang diao", "sang jiao", "xiang tiao"],
                    "谢谢": ["se se", "de de", "te te", "sie sie", "ce ce"],
                    "汽车": ["qi ce", "ti che", "di che", "gi che", "qi te"],
                    "小狗": ["siao gou", "tiao dou", "xiao dou", "siao kou", "diao gou"],
                    "小猫": ["siao mao", "tiao nao", "xiao dao", "siao bao", "diao mao"],
                    "再见": ["zai dian", "dai jian", "zai gan", "cai jian", "zai tian"],
                    "睡觉": ["sui jiao", "zhui jiao", "shui diao", "dui jiao", "sui giao"],
                    "起来": ["qi dai", "ti lai", "di lai", "gi lai", "qi nai"],
                    "回来": ["hui dai", "fui lai", "hui nai", "gui lai", "wei lai"],
                    "出去": ["cu qu", "chu tu", "tu qu", "chu gu", "cu tu"],
                    "进来": ["jin dai", "din lai", "jin nai", "gin lai", "zin lai"],
                    "开门": ["kai men(平声)", "gai men", "kai nen", "dai men", "kai ben"],
                    "关门": ["guan men(平声)", "duan men", "guan nen", "kuan men", "wan men"]
                }
            },
            "24_months": {
                "words": ["哥哥", "姐姐", "飞机", "火车", "公园", "回家", "吃饭", "喝水", "洗手", "刷牙", "穿衣", "脱鞋", "上床", "下床"],
                "common_errors": {
                    "哥哥": ["de de", "ke ke", "do do", "go go", "te te"],
                    "姐姐": ["de de", "ze ze", "jie jie(平声)", "tie tie", "se se"],
                    "飞机": ["bei ji", "pei ji", "fei di", "wei ji", "fei ti"],
                    "火车": ["huo ce", "fo che", "huo chan", "wo che", "huo te"],
                    "公园": ["dong yuan", "gong wan", "kong yuan", "tong yuan", "gong duan"],
                    "回家": ["hui ga", "fui jia", "hui da", "wei jia", "hui ka"],
                    "吃饭": ["ci fan", "ti fan", "chi wan", "zi fan", "chi ban"],
                    "喝水": ["he dui", "ke shui", "he tui", "ge shui", "he sui"],
                    "洗手": ["si shou", "xi tou", "di shou", "xi dou", "si tou"],
                    "刷牙": ["sua ya", "shua da", "tua ya", "shua ga", "sua da"],
                    "穿衣": ["cuan yi", "chuan di", "tuan yi", "chuan gi", "cuan di"],
                    "脱鞋": ["tuo sie", "duo xie", "tuo die", "wo xie", "tuo te"],
                    "上床": ["sang chuang", "shang tuang", "dang chuang", "shang duang", "sang tuang"],
                    "下床": ["sia chuang", "xia tuang", "da chuang", "xia duang", "sia tuang"]
                }
            },
            "30_months": {
                "words": ["幼儿园", "小朋友", "老师", "游戏", "故事", "唱歌", "跳舞", "画画", "积木", "玩具", "滑梯", "秋千", "蹦床", "皮球"],
                "common_errors": {
                    "幼儿园": ["you er wan", "you li yuan", "you er duan", "dou er yuan", "you er gan"],
                    "小朋友": ["siao peng you", "xiao beng you", "xiao peng dou", "tiao peng you", "xiao peng lou"],
                    "老师": ["lao si", "nao shi", "lao sei", "dao shi", "lao ti"],
                    "游戏": ["you si", "dou xi", "you di", "lou xi", "you ti"],
                    "故事": ["gu si", "ku shi", "gu sei", "du shi", "gu ti"],
                    "唱歌": ["cang ge", "chang ke", "tang ge", "chang de", "cang ke"],
                    "跳舞": ["tiao u", "diao wu", "tiao fu", "jiao wu", "tiao bu"],
                    "画画": ["hua hua(平声)", "wa wa", "hua fa", "gua hua", "hua pa"],
                    "积木": ["ji u", "di mu", "ji bu", "gi mu", "zi mu"],
                    "玩具": ["wan u", "wan du", "fan ju", "wan tu", "ban ju"],
                    "滑梯": ["hua i", "wa ti", "hua di", "gua ti", "hua gi"],
                    "秋千": ["qiu ian", "tiu qian", "qiu dian", "ciu qian", "qiu tian"],
                    "蹦床": ["beng uang", "peng chuang", "beng tuang", "meng chuang", "beng duang"],
                    "皮球": ["pi iu", "bi qiu", "pi tiu", "fi qiu", "pi diu"]
                }
            },
            "36_months": {
                "words": ["蝴蝶", "彩虹", "星星", "月亮", "太阳", "白云", "下雨", "刮风", "打雷", "闪电", "彩色", "漂亮", "好看", "美丽"],
                "common_errors": {
                    "蝴蝶": ["hu die(平声)", "fu die", "hu te", "wu die", "hu le"],
                    "彩虹": ["cai hong(平声)", "tai hong", "cai gong", "sai hong", "cai dong"],
                    "星星": ["sing sing", "xing xing(平声)", "ting ting", "xin xin", "sin sin"],
                    "月亮": ["yue niang", "ye liang", "yue lang", "due liang", "yue diang"],
                    "太阳": ["tai iang", "dai yang", "tai dang", "tai lang", "dai dang"],
                    "白云": ["bai un", "pai yun", "bai dun", "fai yun", "bai gun"],
                    "下雨": ["sia yu", "xia du", "da yu", "xia gu", "sia du"],
                    "刮风": ["gua ong", "kua feng", "gua dong", "dua feng", "gua bong"],
                    "打雷": ["da ei", "ta lei", "da nei", "ba lei", "da dei"],
                    "闪电": ["san dian", "shan tian", "dan dian", "shan lian", "san tian"],
                    "彩色": ["cai e", "tai se", "cai te", "sai se", "cai de"],
                    "漂亮": ["piao iang", "biao liang", "piao diang", "fiao liang", "piao niang"],
                    "好看": ["hao an", "dao kan", "hao gan", "bao kan", "hao dan"],
                    "美丽": ["mei i", "bei li", "mei di", "fei li", "mei gi"]
                }
            },
            # 添加更细分的年龄段和特殊发音问题
            "15_months": {
                "words": ["球球", "书书", "鱼鱼", "花花", "车车", "鸟鸟", "牛牛", "羊羊"],
                "common_errors": {
                    "球球": ["tiu tiu", "qiu qiu(平声)", "diu diu", "ciu ciu"],
                    "书书": ["tu tu", "shu shu(平声)", "du du", "cu cu"],
                    "鱼鱼": ["yu yu(平声)", "du du", "lu lu", "wu wu"],
                    "花花": ["hua hua(平声)", "wa wa", "fa fa", "pa pa"],
                    "车车": ["te te", "che che(平声)", "de de", "ce ce"],
                    "鸟鸟": ["niao niao(平声)", "diao diao", "liao liao", "tiao tiao"],
                    "牛牛": ["niu niu(平声)", "diu diu", "liu liu", "tiu tiu"],
                    "羊羊": ["yang yang(平声)", "dang dang", "lang lang", "nang nang"]
                }
            },
            "21_months": {
                "words": ["电视", "电话", "冰箱", "洗衣机", "空调", "风扇", "灯灯", "桌子"],
                "common_errors": {
                    "电视": ["dian si", "tian shi", "dian sei", "lian shi"],
                    "电话": ["dian wa", "tian hua", "dian fa", "lian hua"],
                    "冰箱": ["bing siang", "ping xiang", "bing diang", "ming xiang"],
                    "洗衣机": ["si yi ji", "xi yi di", "di yi ji", "xi yi ti"],
                    "空调": ["kong iao", "tong tiao", "kong diao", "gong tiao"],
                    "风扇": ["fong san", "feng tan", "wong shan", "feng dan"],
                    "灯灯": ["deng deng(平声)", "teng teng", "neng neng", "leng leng"],
                    "桌子": ["zhuo i", "duo zi", "zhuo di", "tuo zi"]
                }
            },
            "27_months": {
                "words": ["蜘蛛", "蚂蚁", "蝴蝶", "蜻蜓", "青蛙", "小鸡", "小鸭", "小兔"],
                "common_errors": {
                    "蜘蛛": ["zi u", "zhi du", "di zhu", "zi du"],
                    "蚂蚁": ["ma i", "ma di", "ba yi", "ma gi"],
                    "蝴蝶": ["hu ie", "fu die", "hu le", "wu die"],
                    "蜻蜓": ["qing ing", "ting ting", "qing ding", "cing ting"],
                    "青蛙": ["qing a", "ting wa", "qing fa", "cing wa"],
                    "小鸡": ["siao i", "xiao di", "tiao ji", "xiao ti"],
                    "小鸭": ["siao a", "xiao da", "tiao ya", "xiao ga"],
                    "小兔": ["siao u", "xiao du", "tiao tu", "xiao bu"]
                }
            },
            "33_months": {
                "words": ["草莓", "葡萄", "橘子", "西瓜", "桃子", "梨子", "樱桃", "芒果"],
                "common_errors": {
                    "草莓": ["cao ei", "tao mei", "cao bei", "sao mei"],
                    "葡萄": ["pu ao", "bu tao", "pu dao", "fu tao"],
                    "橘子": ["ju i", "du zi", "ju di", "tu zi"],
                    "西瓜": ["si ua", "xi dua", "di gua", "xi wa"],
                    "桃子": ["tao i", "dao zi", "tao di", "bao zi"],
                    "梨子": ["li i", "ni zi", "li di", "yi zi"],
                    "樱桃": ["ying ao", "ding tao", "ying dao", "lin tao"],
                    "芒果": ["mang uo", "nang guo", "mang duo", "bang guo"]
                }
            }
        }

        # 添加发音错误的专业分类
        self.error_types = {
            "substitution": {  # 替换错误
                "description": "用一个音素替换另一个音素",
                "examples": ["哥哥→得得", "飞机→杯机", "水水→堆堆"]
            },
            "omission": {  # 省略错误
                "description": "省略词汇中的某个音素",
                "examples": ["苹果→苹o", "谢谢→谢e", "蝴蝶→蝴e"]
            },
            "distortion": {  # 歪曲错误
                "description": "发音不准确但接近目标音",
                "examples": ["r音不清", "舌音位置不准", "气流不足"]
            },
            "addition": {  # 添加错误
                "description": "在词汇中添加多余的音素",
                "examples": ["车→车儿", "花→花啊", "书→书呀"]
            },
            "tone_error": {  # 声调错误
                "description": "声调发音不准确",
                "examples": ["妈妈(平声)", "爸爸(平声)", "水水(平声)"]
            }
        }
        
        # 五步纠正法prompt模板
        self.five_step_prompt = """
你是一位专业的儿童语言治疗师，请基于以下五步发音纠正法为家长提供具体的指导建议：

五步纠正法：
1. Say the word aloud (大声说出这个词) - 家长清晰示范正确发音
2. Decide if you know it (判断是否理解) - 确认孩子理解词汇含义  
3. Think of words that sound similar (想想发音相似的词) - 引导联想相似发音
4. Choose the closest-sounding real word (选择发音最接近的真实词汇) - 从简单开始练习
5. Check if it makes sense in context (检查在语境中是否合理) - 在实际情境中验证

核心原则：分步矫正 + 语境检验 + 鼓励语

现在请为以下情况提供详细的纠正指导：

孩子信息：
- 年龄：{age}个月
- 目标词汇：{target_word}
- 错误发音：{mispronunciation}
- 正确发音：{correct_pronunciation}

请提供：
1. 问题分析（为什么会出现这种错误）
2. 五步纠正法的具体实施步骤
3. 3-4个具体的鼓励语句
4. 预期改善时间
5. 注意事项

请用温和、专业、实用的语调回答，确保家长容易理解和执行。
"""

    def call_qwen_api(self, prompt: str, max_retries: int = 5) -> Optional[str]:
        """调用Qwen API生成内容 - 改进的错误处理和重试机制"""

        # 验证prompt长度
        if len(prompt) > 8000:
            print(f"⚠️  Prompt过长 ({len(prompt)}字符)，可能导致API调用失败")
            prompt = prompt[:7500] + "\n\n请基于以上信息提供指导。"

        payload = {
            "model": "qwen-plus-2025-01-25",
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            },
            "parameters": {
                "temperature": 0.7,
                "max_tokens": 1500,  # 减少token数量避免超限
                "top_p": 0.9
            }
        }

        for attempt in range(max_retries):
            try:
                print(f"🔄 API调用尝试 {attempt + 1}/{max_retries}...")

                response = requests.post(
                    self.api_url,
                    headers=self.headers,
                    json=payload,
                    timeout=60  # 增加超时时间
                )

                # 详细的状态码处理
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if "output" in result and "text" in result["output"]:
                            content = result["output"]["text"]
                            if content and len(content.strip()) > 50:  # 确保内容有意义
                                print(f"✅ API调用成功，返回内容长度: {len(content)}字符")
                                return content
                            else:
                                print(f"⚠️  API返回内容过短: {content[:100]}...")
                        else:
                            print(f"❌ API响应格式异常: {result}")
                    except json.JSONDecodeError as e:
                        print(f"❌ JSON解析失败: {str(e)}")
                        print(f"原始响应: {response.text[:500]}...")

                elif response.status_code == 400:
                    print(f"❌ 请求参数错误 (400): {response.text}")
                    # 400错误通常不需要重试
                    break

                elif response.status_code == 401:
                    print(f"❌ API密钥无效 (401): {response.text}")
                    # 401错误不需要重试
                    break

                elif response.status_code == 429:
                    print(f"⚠️  API调用频率限制 (429): {response.text}")
                    # 429错误需要更长的等待时间
                    wait_time = min(60, 10 * (2 ** attempt))
                    print(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    continue

                elif response.status_code == 500:
                    print(f"⚠️  服务器内部错误 (500): {response.text}")
                    # 500错误可以重试

                elif response.status_code == 503:
                    print(f"⚠️  服务暂时不可用 (503): {response.text}")
                    # 503错误需要等待更长时间
                    wait_time = min(120, 20 * (2 ** attempt))
                    print(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    continue

                else:
                    print(f"❌ 未知状态码 ({response.status_code}): {response.text}")

            except requests.exceptions.Timeout:
                print(f"⚠️  请求超时 (尝试 {attempt + 1}/{max_retries})")

            except requests.exceptions.ConnectionError as e:
                print(f"⚠️  网络连接错误 (尝试 {attempt + 1}/{max_retries}): {str(e)}")

            except requests.exceptions.RequestException as e:
                print(f"⚠️  请求异常 (尝试 {attempt + 1}/{max_retries}): {str(e)}")

            except Exception as e:
                print(f"❌ 未知异常 (尝试 {attempt + 1}/{max_retries}): {str(e)}")
                import traceback
                traceback.print_exc()

            # 重试前等待
            if attempt < max_retries - 1:
                wait_time = min(30, 3 * (2 ** attempt))  # 指数退避，最大30秒
                print(f"⏳ 等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)

        print(f"❌ API调用最终失败，已尝试 {max_retries} 次")
        return None

    def generate_pronunciation_case(self, age_months: int) -> Optional[Dict[str, Any]]:
        """生成一个发音纠正案例"""
        
        # 根据年龄选择合适的词汇
        age_group = self._get_age_group(age_months)
        if age_group not in self.pronunciation_data:
            return None
            
        # 随机选择词汇和错误发音
        words = self.pronunciation_data[age_group]["words"]
        target_word = random.choice(words)
        
        # 获取该词汇的常见错误发音
        errors = self.pronunciation_data[age_group]["common_errors"]
        if target_word in errors:
            mispronunciation = random.choice(errors[target_word])
        else:
            # 如果没有预设错误，生成一个通用错误
            mispronunciation = self._generate_generic_error(target_word)
        
        # 构建prompt
        prompt = self.five_step_prompt.format(
            age=age_months,
            target_word=target_word,
            mispronunciation=mispronunciation,
            correct_pronunciation=target_word
        )
        
        # 调用API生成指导内容
        print(f"正在为 {age_months}个月孩子的'{target_word}'发音问题生成指导...")
        guidance = self.call_qwen_api(prompt)
        
        if not guidance:
            print(f"生成失败: {target_word}")
            return None
        
        # 构建完整的案例数据
        case_id = self._generate_id(f"{age_months}_{target_word}_{mispronunciation}")
        
        case_data = {
            "id": case_id,
            "type": "llm_generated_case",
            "source": {
                "generator": "Qwen API",
                "generation_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "credibility_score": 8,
                "method": "五步发音纠正法"
            },
            "case_info": {
                "child_age_months": age_months,
                "target_word": target_word,
                "mispronunciation": mispronunciation,
                "correct_pronunciation": target_word,
                "age_group": age_group
            },
            "llm_guidance": {
                "raw_response": guidance,
                "prompt_used": prompt
            },
            "metadata": {
                "generation_method": "Qwen API + 五步纠正法",
                "target_age_range": f"{age_months-3}-{age_months+3}个月",
                "difficulty_level": self._get_difficulty_level(age_months),
                "language": "zh-CN"
            }
        }
        
        print(f"✓ 成功生成: {target_word} ({mispronunciation})")
        return case_data

    def generate_batch_cases(self, num_cases: int = 10, age_range: tuple = (12, 36),
                           save_interval: int = 10, resume_file: str = None) -> List[Dict[str, Any]]:
        """批量生成发音纠正案例 - 改进的错误恢复和进度保存"""

        generated_cases = []
        min_age, max_age = age_range
        failed_attempts = 0
        max_failures = num_cases // 2  # 允许的最大失败次数

        # 尝试从断点恢复
        if resume_file and os.path.exists(resume_file):
            try:
                with open(resume_file, 'r', encoding='utf-8') as f:
                    resume_data = json.load(f)
                    generated_cases = resume_data.get('cases', [])
                    print(f"📂 从断点恢复，已有 {len(generated_cases)} 个案例")
            except Exception as e:
                print(f"⚠️  断点恢复失败: {str(e)}")

        start_index = len(generated_cases)
        print(f"🚀 开始生成发音纠正案例...")
        print(f"📊 目标数量: {num_cases}")
        print(f"📅 年龄范围: {min_age}-{max_age}个月")
        print(f"💾 自动保存间隔: 每{save_interval}个案例")
        print(f"🔄 从第 {start_index + 1} 个开始生成")
        print("=" * 60)

        for i in range(start_index, num_cases):
            try:
                # 随机选择年龄，确保分布均匀
                available_ages = [12, 15, 18, 21, 24, 27, 30, 33, 36]
                valid_ages = [age for age in available_ages if min_age <= age <= max_age]
                if not valid_ages:
                    valid_ages = [random.randint(min_age, max_age)]

                age = random.choice(valid_ages)

                print(f"\n🎯 生成第 {i+1}/{num_cases} 个案例 (年龄: {age}个月)")

                case = self.generate_pronunciation_case(age)
                if case:
                    generated_cases.append(case)
                    success_rate = len(generated_cases) / (i + 1) * 100
                    print(f"✅ 成功！当前进度: {len(generated_cases)}/{num_cases} (成功率: {success_rate:.1f}%)")

                    # 定期保存进度
                    if len(generated_cases) % save_interval == 0:
                        self._save_progress(generated_cases, f"temp_progress_{len(generated_cases)}.json")

                else:
                    failed_attempts += 1
                    print(f"❌ 第 {i+1} 个案例生成失败 (累计失败: {failed_attempts})")

                    # 如果失败次数过多，停止生成
                    if failed_attempts >= max_failures:
                        print(f"⚠️  失败次数过多 ({failed_attempts})，停止生成")
                        break

                # 动态调整等待时间
                if failed_attempts > 5:
                    wait_time = min(10, failed_attempts)
                    print(f"⏳ 等待 {wait_time} 秒后继续...")
                    time.sleep(wait_time)
                else:
                    time.sleep(2)  # 基础等待时间

            except KeyboardInterrupt:
                print(f"\n⚠️  用户中断，保存当前进度...")
                break

            except Exception as e:
                print(f"❌ 生成过程异常: {str(e)}")
                failed_attempts += 1
                continue

        # 最终统计
        success_rate = len(generated_cases) / num_cases * 100 if num_cases > 0 else 0
        print("\n" + "=" * 60)
        print(f"🎉 批量生成完成！")
        print(f"📊 成功生成: {len(generated_cases)}/{num_cases} 个案例")
        print(f"📈 成功率: {success_rate:.1f}%")
        print(f"❌ 失败次数: {failed_attempts}")

        # 保存最终结果
        if generated_cases:
            final_file = f"generated_cases_final3_{len(generated_cases)}.json"
            self._save_progress(generated_cases, final_file)
            print(f"💾 最终结果已保存到: {final_file}")

        return generated_cases

    def _save_progress(self, cases: List[Dict[str, Any]], filename: str):
        """保存生成进度"""
        try:
            os.makedirs("data", exist_ok=True)
            progress_data = {
                "save_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "total_cases": len(cases),
                "cases": cases
            }

            filepath = f"data/{filename}"
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, ensure_ascii=False, indent=2)

            print(f"💾 进度已保存: {filepath}")

        except Exception as e:
            print(f"⚠️  保存进度失败: {str(e)}")

    def _get_age_group(self, age_months: int) -> str:
        """根据年龄获取对应的年龄组（更精细的年龄分组）"""
        if age_months <= 12:
            return "12_months"
        elif age_months <= 15:
            return "15_months"
        elif age_months <= 18:
            return "18_months"
        elif age_months <= 21:
            return "21_months"
        elif age_months <= 24:
            return "24_months"
        elif age_months <= 27:
            return "27_months"
        elif age_months <= 30:
            return "30_months"
        elif age_months <= 33:
            return "33_months"
        else:
            return "36_months"

    def _generate_generic_error(self, word: str) -> str:
        """为没有预设错误的词汇生成通用错误（基于儿童发音发育规律）"""

        # 基于专业研究的发音错误模式
        error_patterns = {
            # 声母替换（最常见）
            'zh': ['z', 'd', 'l'],      # 翘舌音 → 平舌音/舌尖音
            'ch': ['c', 't', 'k'],      # 翘舌音 → 平舌音/塞音
            'sh': ['s', 'f', 'h'],      # 翘舌音 → 平舌音/摩擦音
            'j': ['d', 'z', 'l'],       # 舌面音 → 舌尖音
            'q': ['t', 'c', 'k'],       # 舌面音 → 塞音
            'x': ['s', 'f', 'h'],       # 舌面音 → 摩擦音
            'g': ['d', 'b', 'w'],       # 舌根音 → 舌尖音/双唇音
            'k': ['t', 'p', 'f'],       # 舌根音 → 塞音
            'h': ['f', 's', 'w'],       # 舌根音 → 摩擦音
            'f': ['b', 'p', 'w'],       # 唇齿音 → 双唇音
            'r': ['l', 'y', 'w'],       # 卷舌音 → 边音/半元音
            'l': ['n', 'y', 'w'],       # 边音 → 鼻音/半元音
            'n': ['l', 'd', 'm'],       # 鼻音混淆
            'm': ['n', 'b', 'w'],       # 双唇鼻音混淆

            # 韵母错误
            'ong': ['on', 'ang', 'eng'], # 后鼻音 → 前鼻音
            'eng': ['en', 'ang', 'ong'], # 前鼻音混淆
            'ang': ['an', 'eng', 'ong'], # 前后鼻音混淆
            'ing': ['in', 'eng', 'ong'], # 前后鼻音混淆
            'ian': ['an', 'iang', 'in'], # 复韵母简化
            'iang': ['ang', 'ian', 'in'], # 复韵母简化
            'uang': ['ang', 'uan', 'ong'], # 复韵母简化
            'uan': ['an', 'uang', 'un'],   # 复韵母简化

            # 声调错误（用平声表示）
            '一声': '(平声)', '二声': '(平声)',
            '三声': '(平声)', '四声': '(平声)'
        }

        error_word = word

        # 尝试应用声母替换
        for correct_sound, error_list in error_patterns.items():
            if len(correct_sound) <= 2 and correct_sound in word:
                error_sound = random.choice(error_list)
                error_word = word.replace(correct_sound, error_sound, 1)
                break

        # 如果没有找到匹配的模式，使用通用错误
        if error_word == word:
            # 常见的通用替换
            simple_substitutions = [
                ('zh', 'z'), ('ch', 'c'), ('sh', 's'),
                ('j', 'd'), ('q', 't'), ('x', 's'),
                ('g', 'd'), ('k', 't'), ('f', 'b'),
                ('r', 'l'), ('n', 'l'), ('ang', 'an')
            ]

            for correct, error in simple_substitutions:
                if correct in word:
                    error_word = word.replace(correct, error, 1)
                    break

        return error_word if error_word != word else f"{word}(发音不清)"

    def _get_difficulty_level(self, age_months: int) -> str:
        """根据年龄获取难度等级"""
        if age_months <= 18:
            return "初级"
        elif age_months <= 30:
            return "中级"
        else:
            return "高级"

    def _generate_id(self, content: str) -> str:
        """生成唯一ID"""
        return f"qwen_{hashlib.md5(content.encode('utf-8')).hexdigest()[:10]}"

    def save_generated_data(self, cases: List[Dict[str, Any]], filename: str):
        """保存生成的数据"""
        output_path = f"data/{filename}"
        
        # 添加数据集元信息
        dataset = {
            "dataset_info": {
                "name": "Qwen API生成的发音纠正案例",
                "generation_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "total_cases": len(cases),
                "method": "五步发音纠正法 + Qwen API",
                "target_age": "0-3岁婴幼儿"
            },
            "cases": cases
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(dataset, f, ensure_ascii=False, indent=2)
        
        print(f"数据已保存到: {output_path}")

def main():
    """主函数 - 需要用户提供API密钥"""
    
    # 注意：请替换为您的实际API密钥
    API_KEY = "sk-5eba46fbcff649d5bf28313bc865de10"
    
    if API_KEY == "YOUR_QWEN_API_KEY_HERE" or not API_KEY:
        print("请先设置您的Qwen API密钥！")
        print("在代码中将 'YOUR_QWEN_API_KEY_HERE' 替换为您的实际API密钥")
        return
    
    # 创建生成器
    generator = QwenPronunciationGenerator(API_KEY)
    
    # 生成案例 - 使用优化的参数
    cases = generator.generate_batch_cases(
        num_cases=200,
        age_range=(12, 36),
        save_interval=5,  # 每5个案例保存一次
        resume_file="data/temp_progress.json"  # 支持断点恢复
    )
    
    if cases:
        # 保存数据
        generator.save_generated_data(cases, "qwen_generated_pronunciation_cases3.json")
        
        # 显示统计信息
        print(f"\n生成统计:")
        print(f"总案例数: {len(cases)}")
        
        age_stats = {}
        for case in cases:
            age = case["case_info"]["child_age_months"]
            age_stats[age] = age_stats.get(age, 0) + 1
        
        print("年龄分布:")
        for age, count in sorted(age_stats.items()):
            print(f"  {age}个月: {count}个案例")
    else:
        print("没有成功生成任何案例，请检查API配置")

if __name__ == "__main__":
    main()
