#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集合并脚本
将所有收集和生成的数据合并为最终的0-3岁发音纠正指导数据集
"""

import json
import os
from typing import Dict, List, Any
from datetime import datetime

class DatasetMerger:
    """数据集合并器"""
    
    def __init__(self):
        self.merged_data = []
        self.statistics = {
            "total_records": 0,
            "by_type": {},
            "by_age_group": {},
            "by_source": {}
        }
    
    def load_json_file(self, filepath: str) -> List[Dict[str, Any]]:
        """加载JSON文件"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
                if isinstance(data, list):
                    return data
                else:
                    return [data]
        except FileNotFoundError:
            print(f"文件未找到: {filepath}")
            return []
        except json.JSONDecodeError:
            print(f"JSON格式错误: {filepath}")
            return []
    
    def merge_datasets(self, data_files: List[str]) -> List[Dict[str, Any]]:
        """合并多个数据集"""
        all_data = []
        
        for filepath in data_files:
            if os.path.exists(filepath):
                data = self.load_json_file(filepath)
                all_data.extend(data)
                print(f"已加载 {filepath}: {len(data)}条记录")
            else:
                print(f"文件不存在: {filepath}")
        
        return all_data
    
    def filter_0_3_years_data(self, data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """筛选0-3岁相关数据"""
        filtered_data = []
        
        for record in data:
            is_relevant = False
            
            # 检查年龄相关性
            if record.get("type") == "qa_pair":
                child_info = record.get("question", {}).get("child_info", {})
                age_months = child_info.get("age_months", 0)
                if 0 <= age_months <= 36:
                    is_relevant = True
            
            elif record.get("type") in ["correction_case", "correction_method", "training_guide", "assessment_tool"]:
                # 检查目标年龄
                content = record.get("content", {})
                if "target_age" in content:
                    target_age = content["target_age"]
                    min_months = target_age.get("min_months", 0)
                    max_months = target_age.get("max_months", 72)
                    if min_months <= 36 or max_months <= 36:
                        is_relevant = True
                
                # 检查案例年龄
                if "case_info" in record:
                    case_age = record["case_info"].get("child_age_months", 0)
                    if 0 <= case_age <= 36:
                        is_relevant = True
            
            elif record.get("type") in ["official_standard", "professional_literature"]:
                # 检查年龄焦点
                content = record.get("content", {})
                if "age_focus" in content:
                    age_focus = content["age_focus"]
                    min_months = age_focus.get("min_months", 0)
                    max_months = age_focus.get("max_months", 72)
                    if min_months <= 36:
                        is_relevant = True
            
            if is_relevant:
                filtered_data.append(record)
        
        return filtered_data
    
    def calculate_statistics(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算数据统计信息"""
        stats = {
            "total_records": len(data),
            "by_type": {},
            "by_age_group": {
                "0-12个月": 0,
                "12-24个月": 0,
                "24-36个月": 0
            },
            "by_credibility": {},
            "by_difficulty": {}
        }
        
        for record in data:
            # 按类型统计
            record_type = record.get("type", "unknown")
            stats["by_type"][record_type] = stats["by_type"].get(record_type, 0) + 1
            
            # 按年龄组统计
            age_months = self._extract_age(record)
            if age_months:
                if age_months <= 12:
                    stats["by_age_group"]["0-12个月"] += 1
                elif age_months <= 24:
                    stats["by_age_group"]["12-24个月"] += 1
                elif age_months <= 36:
                    stats["by_age_group"]["24-36个月"] += 1
            
            # 按可信度统计
            credibility = record.get("source", {}).get("credibility_score", 0)
            if credibility > 0:
                cred_range = f"{int(credibility)}-{int(credibility)+1}分"
                stats["by_credibility"][cred_range] = stats["by_credibility"].get(cred_range, 0) + 1
            
            # 按难度统计
            difficulty = record.get("metadata", {}).get("difficulty_level", "")
            if difficulty:
                stats["by_difficulty"][difficulty] = stats["by_difficulty"].get(difficulty, 0) + 1
        
        return stats
    
    def _extract_age(self, record: Dict[str, Any]) -> int:
        """提取记录中的年龄信息"""
        if record.get("type") == "qa_pair":
            return record.get("question", {}).get("child_info", {}).get("age_months", 0)
        elif "case_info" in record:
            return record["case_info"].get("child_age_months", 0)
        elif "content" in record and "target_age" in record["content"]:
            return record["content"]["target_age"].get("min_months", 0)
        return 0
    
    def add_metadata_to_dataset(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """为数据集添加元数据"""
        dataset_metadata = {
            "dataset_info": {
                "name": "0-3岁婴幼儿发音纠正指导数据集",
                "version": "1.0",
                "creation_date": datetime.now().strftime("%Y-%m-%d"),
                "description": "专门针对0-3岁婴幼儿的发音纠正指导数据集，基于五步纠正法构建",
                "target_age_range": "0-36个月",
                "methodology": "分步矫正 + 语境检验 + 鼓励语",
                "data_sources": [
                    "权威医疗机构研究",
                    "专业文献资料",
                    "实际临床案例",
                    "基于循证研究的生成数据"
                ]
            },
            "statistics": self.calculate_statistics(data),
            "usage_guidelines": {
                "target_users": ["家长", "语言治疗师", "儿科医生", "早教老师"],
                "application_scenarios": [
                    "家庭发音纠正指导",
                    "专业语言治疗参考",
                    "早期发音问题筛查",
                    "LLM模型训练数据"
                ],
                "safety_notes": [
                    "所有建议均基于专业研究",
                    "严重问题请咨询专业医师",
                    "避免过度纠正造成心理压力",
                    "尊重儿童个体发育差异"
                ]
            },
            "data": data
        }
        
        return dataset_metadata
    
    def save_merged_dataset(self, data: Dict[str, Any], output_path: str):
        """保存合并后的数据集"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"数据集已保存到: {output_path}")
        print(f"总记录数: {data['statistics']['total_records']}")

def main():
    """主函数"""
    merger = DatasetMerger()
    
    # 定义要合并的数据文件
    data_files = [
        "data/collected_real_data.json",
        "data/pronunciation_correction_0_3_years.json", 
        "data/qa_pronunciation_0_3_years.json",
        "data/generated_correction_cases.json",
        "data/generated_qa_pairs.json"
    ]
    
    print("开始合并数据集...")
    
    # 合并所有数据
    all_data = merger.merge_datasets(data_files)
    print(f"合并前总数据: {len(all_data)}条")
    
    # 筛选0-3岁相关数据
    filtered_data = merger.filter_0_3_years_data(all_data)
    print(f"筛选后0-3岁数据: {len(filtered_data)}条")
    
    # 添加数据集元数据
    final_dataset = merger.add_metadata_to_dataset(filtered_data)
    
    # 保存最终数据集
    merger.save_merged_dataset(final_dataset, "data/final_0_3_pronunciation_dataset.json")
    
    # 打印统计信息
    stats = final_dataset["statistics"]
    print("\n=== 数据集统计信息 ===")
    print(f"总记录数: {stats['total_records']}")
    print("\n按类型分布:")
    for data_type, count in stats["by_type"].items():
        print(f"  {data_type}: {count}条")
    
    print("\n按年龄组分布:")
    for age_group, count in stats["by_age_group"].items():
        print(f"  {age_group}: {count}条")
    
    if stats["by_credibility"]:
        print("\n按可信度分布:")
        for cred_range, count in stats["by_credibility"].items():
            print(f"  {cred_range}: {count}条")

if __name__ == "__main__":
    main()
