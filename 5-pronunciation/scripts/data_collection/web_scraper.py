#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿发音纠正数据收集器
用于从各种网络来源收集相关数据
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import logging
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin, urlparse
import re
from datetime import datetime
import hashlib

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class WebScraper:
    """网页数据收集器"""
    
    def __init__(self, delay: float = 1.0):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
        self.delay = delay  # 请求间隔，避免过于频繁
        self.collected_data = []
        
    def _generate_id(self, content: str) -> str:
        """生成唯一ID"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()[:12]
    
    def _clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""
        
        # 移除多余空白字符
        text = re.sub(r'\s+', ' ', text.strip())
        # 移除特殊字符
        text = re.sub(r'[^\w\s\u4e00-\u9fff，。！？；：""''（）【】]', '', text)
        return text
    
    def scrape_official_documents(self, urls: List[str]) -> List[Dict[str, Any]]:
        """收集官方文档数据"""
        official_data = []
        
        for url in urls:
            try:
                logger.info(f"正在收集官方文档: {url}")
                response = self.session.get(url, timeout=10)
                response.raise_for_status()
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # 提取标题
                title = soup.find('title')
                title_text = title.get_text().strip() if title else "未知标题"
                
                # 提取主要内容
                content_selectors = [
                    '.content', '.main-content', '.article-content',
                    '#content', '#main', '.post-content'
                ]
                
                content_text = ""
                for selector in content_selectors:
                    content_elem = soup.select_one(selector)
                    if content_elem:
                        content_text = content_elem.get_text()
                        break
                
                if not content_text:
                    # 如果没有找到特定选择器，尝试提取所有段落
                    paragraphs = soup.find_all('p')
                    content_text = ' '.join([p.get_text() for p in paragraphs])
                
                content_text = self._clean_text(content_text)
                
                # 构造数据结构
                data_item = {
                    "id": f"official_{self._generate_id(url)}",
                    "type": "official_standard",
                    "source": {
                        "organization": self._extract_organization(url),
                        "document_title": title_text,
                        "publication_date": self._extract_date(soup),
                        "url": url,
                        "credibility_score": 10  # 官方文档默认最高可信度
                    },
                    "content": {
                        "raw_text": content_text,
                        "extracted_info": self._extract_developmental_info(content_text)
                    },
                    "metadata": {
                        "language": "zh-CN",
                        "region": "中国大陆",
                        "last_updated": datetime.now().strftime("%Y-%m-%d"),
                        "version": "1.0",
                        "collection_method": "web_scraping"
                    }
                }
                
                official_data.append(data_item)
                time.sleep(self.delay)
                
            except Exception as e:
                logger.error(f"收集官方文档失败 {url}: {str(e)}")
                continue
        
        return official_data
    
    def scrape_medical_qa(self, platform_configs: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """收集医疗问答数据"""
        qa_data = []
        
        for config in platform_configs:
            platform_name = config.get("name", "未知平台")
            search_urls = config.get("search_urls", [])
            
            logger.info(f"正在收集{platform_name}的问答数据")
            
            for search_url in search_urls:
                try:
                    response = self.session.get(search_url, timeout=10)
                    response.raise_for_status()
                    
                    soup = BeautifulSoup(response.content, 'html.parser')
                    
                    # 根据平台特点提取问答对
                    qa_items = self._extract_qa_pairs(soup, platform_name)
                    
                    for qa_item in qa_items:
                        qa_item["id"] = f"qa_{self._generate_id(qa_item['question']['problem_description'])}"
                        qa_item["type"] = "qa_pair"
                        qa_item["source"]["platform"] = platform_name
                        qa_item["metadata"] = {
                            "collection_date": datetime.now().strftime("%Y-%m-%d"),
                            "collection_method": "web_scraping",
                            "validation_status": "待验证"
                        }
                    
                    qa_data.extend(qa_items)
                    time.sleep(self.delay)
                    
                except Exception as e:
                    logger.error(f"收集{platform_name}问答失败 {search_url}: {str(e)}")
                    continue
        
        return qa_data
    
    def _extract_organization(self, url: str) -> str:
        """从URL提取组织机构名称"""
        domain_org_map = {
            "nhc.gov.cn": "国家卫生健康委员会",
            "moe.gov.cn": "中华人民共和国教育部",
            "unicef.cn": "联合国儿童基金会中国办事处",
            "cdc.gov.cn": "中国疾病预防控制中心"
        }
        
        domain = urlparse(url).netloc
        for key, org in domain_org_map.items():
            if key in domain:
                return org
        
        return "未知机构"
    
    def _extract_date(self, soup: BeautifulSoup) -> str:
        """提取发布日期"""
        date_selectors = [
            '.publish-date', '.date', '.time',
            '[class*="date"]', '[class*="time"]'
        ]
        
        for selector in date_selectors:
            date_elem = soup.select_one(selector)
            if date_elem:
                date_text = date_elem.get_text()
                # 尝试提取日期格式
                date_match = re.search(r'(\d{4}[-/]\d{1,2}[-/]\d{1,2})', date_text)
                if date_match:
                    return date_match.group(1).replace('/', '-')
        
        return datetime.now().strftime("%Y-%m-%d")
    
    def _extract_developmental_info(self, text: str) -> Dict[str, Any]:
        """从文本中提取发育信息"""
        info = {
            "age_milestones": [],
            "pronunciation_standards": [],
            "intervention_methods": []
        }
        
        # 提取年龄里程碑
        age_patterns = [
            r'(\d+)个月.*?([^。]+发音|[^。]+语言|[^。]+说话)',
            r'(\d+)岁.*?([^。]+发音|[^。]+语言|[^。]+说话)'
        ]
        
        for pattern in age_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                age_str, milestone = match
                age_months = int(age_str) * 12 if '岁' in pattern else int(age_str)
                info["age_milestones"].append({
                    "age_months": age_months,
                    "milestone": self._clean_text(milestone)
                })
        
        # 提取发音标准
        pronunciation_patterns = [
            r'([a-zA-Z/]+音).*?([^。]+准确|[^。]+清楚|[^。]+标准)',
        ]
        
        for pattern in pronunciation_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                phoneme, standard = match
                info["pronunciation_standards"].append({
                    "phoneme": phoneme,
                    "standard": self._clean_text(standard)
                })
        
        return info
    
    def _extract_qa_pairs(self, soup: BeautifulSoup, platform: str) -> List[Dict[str, Any]]:
        """提取问答对（需要根据具体平台定制）"""
        qa_pairs = []
        
        # 这里需要根据不同平台的HTML结构来定制
        # 以下是通用的提取逻辑示例
        
        question_selectors = ['.question', '.q', '[class*="question"]']
        answer_selectors = ['.answer', '.a', '[class*="answer"]']
        
        questions = []
        answers = []
        
        for selector in question_selectors:
            questions.extend(soup.select(selector))
        
        for selector in answer_selectors:
            answers.extend(soup.select(selector))
        
        # 配对问题和答案
        for i, (q_elem, a_elem) in enumerate(zip(questions, answers)):
            question_text = self._clean_text(q_elem.get_text())
            answer_text = self._clean_text(a_elem.get_text())
            
            if len(question_text) > 20 and len(answer_text) > 50:  # 过滤太短的内容
                qa_pair = {
                    "source": {
                        "platform": platform,
                        "question_date": datetime.now().strftime("%Y-%m-%d"),
                        "answer_date": datetime.now().strftime("%Y-%m-%d"),
                        "credibility_score": 7  # 默认可信度
                    },
                    "question": {
                        "child_info": self._extract_child_info(question_text),
                        "problem_description": question_text,
                        "specific_concerns": self._extract_concerns(question_text)
                    },
                    "answer": {
                        "assessment": self._extract_assessment(answer_text),
                        "recommendations": self._extract_recommendations(answer_text),
                        "professional_advice": answer_text[:200] + "..." if len(answer_text) > 200 else answer_text
                    }
                }
                
                qa_pairs.append(qa_pair)
        
        return qa_pairs
    
    def _extract_child_info(self, question_text: str) -> Dict[str, Any]:
        """从问题中提取儿童信息"""
        child_info = {
            "age_months": 0,
            "gender": "unknown",
            "developmental_status": "未知",
            "language_environment": "未知"
        }
        
        # 提取年龄
        age_patterns = [
            r'(\d+)个月',
            r'(\d+)岁',
            r'(\d+)周岁'
        ]
        
        for pattern in age_patterns:
            match = re.search(pattern, question_text)
            if match:
                age = int(match.group(1))
                if '个月' in pattern:
                    child_info["age_months"] = age
                else:
                    child_info["age_months"] = age * 12
                break
        
        # 提取性别
        if '儿子' in question_text or '男孩' in question_text:
            child_info["gender"] = "male"
        elif '女儿' in question_text or '女孩' in question_text:
            child_info["gender"] = "female"
        
        return child_info
    
    def _extract_concerns(self, question_text: str) -> List[str]:
        """提取关注点"""
        concerns = []
        
        concern_keywords = [
            "发音不清", "说话不清楚", "构音障碍", "语言发育",
            "发音问题", "口齿不清", "吐字不清"
        ]
        
        for keyword in concern_keywords:
            if keyword in question_text:
                concerns.append(keyword)
        
        return concerns if concerns else ["发音问题"]
    
    def _extract_assessment(self, answer_text: str) -> Dict[str, Any]:
        """提取评估信息"""
        return {
            "severity": "待评估",
            "urgency": "待评估",
            "age_appropriateness": "待评估",
            "explanation": answer_text[:100] + "..." if len(answer_text) > 100 else answer_text
        }
    
    def _extract_recommendations(self, answer_text: str) -> List[Dict[str, Any]]:
        """提取建议"""
        return [{
            "type": "专业建议",
            "description": answer_text[:200] + "..." if len(answer_text) > 200 else answer_text
        }]
    
    def save_data(self, filename: str):
        """保存收集的数据"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.collected_data, f, ensure_ascii=False, indent=2)
            logger.info(f"数据已保存到 {filename}")
        except Exception as e:
            logger.error(f"保存数据失败: {str(e)}")

def main():
    """主函数"""
    scraper = WebScraper(delay=2.0)  # 2秒间隔
    
    # 示例：收集官方文档
    official_urls = [
        # 这里添加实际的官方文档URL
        # "https://www.nhc.gov.cn/example",
        # "https://www.moe.gov.cn/example"
    ]
    
    # 示例：医疗平台配置
    platform_configs = [
        {
            "name": "示例医疗平台",
            "search_urls": [
                # 这里添加实际的搜索URL
                # "https://example.com/search?q=儿童发音问题"
            ]
        }
    ]
    
    # 收集数据
    if official_urls:
        official_data = scraper.scrape_official_documents(official_urls)
        scraper.collected_data.extend(official_data)
    
    if platform_configs:
        qa_data = scraper.scrape_medical_qa(platform_configs)
        scraper.collected_data.extend(qa_data)
    
    # 保存数据
    if scraper.collected_data:
        scraper.save_data("data/collected_data.json")
        logger.info(f"共收集到 {len(scraper.collected_data)} 条数据")
    else:
        logger.info("未收集到数据，请检查URL配置")

if __name__ == "__main__":
    main()
