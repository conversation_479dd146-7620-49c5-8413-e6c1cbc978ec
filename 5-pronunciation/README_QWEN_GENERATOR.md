# 🎯 基于Qwen API的发音纠正指导生成系统

## 📋 项目概述

这是一个使用Qwen API生成真实AI驱动的婴幼儿发音纠正指导的系统。与之前的模板化生成不同，这个系统：

- ✅ **真正的LLM生成**: 使用Qwen API生成个性化指导内容
- ✅ **基于五步纠正法**: 应用您提供的科学方法作为prompt
- ✅ **针对0-3岁**: 专门为婴幼儿发音特点设计
- ✅ **高质量输出**: 专业、实用、易于家长执行

## 🏗️ 系统架构

```
📁 项目结构
├── scripts/data_generation/
│   └── qwen_pronunciation_generator.py    # 核心生成器
├── config/
│   └── qwen_config.py                     # API配置管理
├── test_qwen_generator.py                 # 快速测试脚本
├── QWEN_GENERATOR_USAGE.md               # 详细使用指南
└── README_QWEN_GENERATOR.md              # 本文件
```

## 🚀 快速开始

### 1. 设置API密钥
```bash
export QWEN_API_KEY="your_actual_qwen_api_key"
```

### 2. 快速测试
```bash
python3 test_qwen_generator.py
```

### 3. 批量生成
```bash
python3 scripts/data_generation/qwen_pronunciation_generator.py
```

## 🎯 核心特性

### 五步纠正法集成
基于您提供的论文方法，每个生成的指导都包含：

1. **Say the word aloud** - 清晰示范正确发音
2. **Decide if you know it** - 确认孩子理解
3. **Think of words that sound similar** - 联想相似发音
4. **Choose the closest-sounding real word** - 选择最佳练习词汇
5. **Check if it makes sense in context** - 语境验证

### 真实发音错误数据
预设了0-3岁常见的发音错误模式：

```python
"18_months": {
    "words": ["苹果", "谢谢", "汽车", "小狗"],
    "common_errors": {
        "谢谢": ["se se", "de de", "te te"],
        "苹果": ["ping o", "bin guo", "ping duo"]
    }
}
```

### 专业Prompt设计
使用精心设计的prompt确保生成内容的专业性：

```
你是一位专业的儿童语言治疗师，请基于五步发音纠正法...
请提供：
1. 问题分析（为什么会出现这种错误）
2. 五步纠正法的具体实施步骤  
3. 3-4个具体的鼓励语句
4. 预期改善时间
5. 注意事项
```

## 📊 生成数据示例

### 输入
- 年龄：18个月
- 目标词汇：谢谢
- 错误发音：se se

### LLM生成的输出
```json
{
  "id": "qwen_abc123def4",
  "case_info": {
    "child_age_months": 18,
    "target_word": "谢谢", 
    "mispronunciation": "se se",
    "correct_pronunciation": "谢谢"
  },
  "llm_guidance": {
    "raw_response": "针对18个月孩子说'谢谢'变成'se se'的问题分析：\n\n1. 问题分析：\n这是典型的复杂音素发音困难。'x'音需要舌面与硬腭形成摩擦，对18个月的孩子来说技术要求较高...\n\n2. 五步纠正法实施：\n第一步：大声说出'谢谢'...\n第二步：确认孩子理解...\n...",
    "prompt_used": "完整的prompt内容..."
  }
}
```

## 🔧 配置选项

### API模型选择
```python
MODEL_PARAMS = {
    "model": "qwen-turbo",    # 快速、经济
    # "model": "qwen-plus",   # 平衡性能
    # "model": "qwen-max",    # 最高质量
    "temperature": 0.7,       # 创造性控制
    "max_tokens": 2000        # 输出长度
}
```

### 生成参数调整
```python
# 批量生成设置
num_cases = 10              # 生成数量
age_range = (12, 36)        # 年龄范围
rate_limit_delay = 1        # API调用间隔
```

## 📈 使用场景

### 1. LLM训练数据
- 为发音纠正指导模型提供高质量训练数据
- 生成多样化的问题场景和解决方案
- 确保数据的专业性和实用性

### 2. 专业工具开发
- 语言治疗师的辅助工具
- 家长教育应用的内容生成
- 早教机构的培训材料

### 3. 研究和分析
- 儿童发音问题模式分析
- 纠正方法效果评估
- 个性化指导方案研究

## 💡 最佳实践

### 成本控制
- 首次使用建议生成5-10个案例测试质量
- 使用qwen-turbo模型降低成本
- 合理设置max_tokens避免过长输出

### 质量保证
- 定期review生成内容的专业性
- 根据实际需求调整prompt
- 建立质量评估标准

### 扩展建议
- 添加更多年龄段和发音类型
- 集成语音识别评估功能
- 开发个性化推荐系统

## 🔍 与之前系统的对比

| 特性 | 模板化生成 | Qwen API生成 |
|------|------------|--------------|
| 内容质量 | 固定模板 | AI个性化生成 |
| 专业性 | 预设规则 | 基于大模型知识 |
| 多样性 | 有限变化 | 无限可能 |
| 成本 | 免费 | API调用费用 |
| 可控性 | 完全可控 | 通过prompt控制 |
| 扩展性 | 需要编程 | 调整prompt即可 |

## 🎉 项目价值

### 技术创新
- 首次将五步发音纠正法与大模型结合
- 创建了专业的儿童语言治疗prompt体系
- 建立了可扩展的AI内容生成框架

### 实用价值
- 为千万家庭提供专业的发音纠正指导
- 降低专业语言治疗的门槛
- 支持早期语言发育干预

### 商业潜力
- 可开发为SaaS服务
- 支持多语言扩展
- 具备个性化定制能力

---

**开始使用**: 运行 `python3 test_qwen_generator.py` 开始您的第一次生成测试！

**获取帮助**: 查看 `QWEN_GENERATOR_USAGE.md` 了解详细使用方法。

**注意事项**: 请确保遵守Qwen API使用条款，合理控制API调用频率和成本。
