#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
扩展后的Qwen发音纠正生成器使用示例
展示如何使用新增的发音错误模式和年龄段
"""

import sys
import json
from datetime import datetime

# 添加项目路径
sys.path.append('scripts/data_generation')
from qwen_pronunciation_generator import QwenPronunciationGenerator

def demonstrate_extended_data():
    """演示扩展后的数据内容"""
    print("🎯 扩展后的发音错误数据库演示")
    print("=" * 60)
    
    # 创建生成器实例
    generator = QwenPronunciationGenerator("demo_key")
    
    # 展示数据统计
    print("\n📊 数据规模统计:")
    total_words = 0
    total_errors = 0
    
    for age_group, data in generator.pronunciation_data.items():
        word_count = len(data['words'])
        error_count = sum(len(errors) for errors in data['common_errors'].values())
        total_words += word_count
        total_errors += error_count
        
        print(f"  {age_group}: {word_count}个词汇, {error_count}个错误示例")
    
    print(f"\n📈 总计: {total_words}个词汇, {total_errors}个错误示例")
    print(f"📅 年龄段: {len(generator.pronunciation_data)}个精细分组")
    
    # 展示各年龄段的特色词汇
    print("\n🎪 各年龄段特色词汇展示:")
    
    showcase_ages = ["12_months", "18_months", "24_months", "30_months", "36_months"]
    
    for age in showcase_ages:
        if age in generator.pronunciation_data:
            data = generator.pronunciation_data[age]
            words = data['words'][:5]  # 显示前5个词汇
            print(f"\n  {age} ({age.split('_')[0]}个月):")
            print(f"    词汇示例: {', '.join(words)}")
            
            # 显示一个具体的错误示例
            if words and words[0] in data['common_errors']:
                word = words[0]
                errors = data['common_errors'][word][:3]  # 显示前3个错误
                print(f"    '{word}'的常见错误: {', '.join(errors)}")

def demonstrate_error_types():
    """演示专业错误分类"""
    print("\n🔬 专业错误分类演示:")
    print("=" * 60)
    
    generator = QwenPronunciationGenerator("demo_key")
    
    for error_type, info in generator.error_types.items():
        print(f"\n📋 {error_type.upper()}:")
        print(f"   定义: {info['description']}")
        print(f"   示例: {', '.join(info['examples'])}")

def demonstrate_age_specific_generation():
    """演示针对特定年龄的数据生成"""
    print("\n🎯 年龄特异性数据生成演示:")
    print("=" * 60)
    
    generator = QwenPronunciationGenerator("demo_key")
    
    # 演示不同年龄的词汇选择
    test_ages = [15, 21, 27, 33]
    
    for age in test_ages:
        age_group = generator._get_age_group(age)
        print(f"\n👶 {age}个月 → 使用 {age_group} 数据:")
        
        if age_group in generator.pronunciation_data:
            data = generator.pronunciation_data[age_group]
            sample_words = data['words'][:3]
            print(f"   可选词汇: {', '.join(sample_words)}")
            
            # 显示一个词汇的所有错误模式
            if sample_words:
                word = sample_words[0]
                if word in data['common_errors']:
                    errors = data['common_errors'][word]
                    print(f"   '{word}'的错误模式: {', '.join(errors)}")

def demonstrate_generic_error_generation():
    """演示通用错误生成功能"""
    print("\n🤖 智能错误生成演示:")
    print("=" * 60)
    
    generator = QwenPronunciationGenerator("demo_key")
    
    # 测试一些不在预设列表中的词汇
    test_words = ["蝴蝶", "彩虹", "星星", "月亮", "太阳"]
    
    print("对于预设词汇的错误生成:")
    for word in test_words:
        error = generator._generate_generic_error(word)
        print(f"   {word} → {error}")
    
    # 测试完全新的词汇
    new_words = ["恐龙", "机器人", "超市", "医院"]
    print("\n对于新词汇的智能错误生成:")
    for word in new_words:
        error = generator._generate_generic_error(word)
        print(f"   {word} → {error}")

def create_sample_prompt():
    """创建示例prompt"""
    print("\n📝 示例Prompt生成:")
    print("=" * 60)
    
    generator = QwenPronunciationGenerator("demo_key")
    
    # 模拟一个具体案例
    age = 24
    target_word = "哥哥"
    mispronunciation = "de de"
    
    prompt = generator.five_step_prompt.format(
        age=age,
        target_word=target_word,
        mispronunciation=mispronunciation,
        correct_pronunciation=target_word
    )
    
    print("生成的专业prompt示例:")
    print("-" * 40)
    print(prompt[:500] + "...")  # 显示前500字符
    print("-" * 40)
    print(f"这个prompt将发送给Qwen API，生成针对{age}个月孩子的专业指导")

def save_sample_data():
    """保存示例数据供参考"""
    print("\n💾 保存示例数据:")
    print("=" * 60)
    
    generator = QwenPronunciationGenerator("demo_key")
    
    # 创建示例数据集
    sample_data = {
        "data_info": {
            "creation_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "description": "扩展后的发音错误数据库示例",
            "total_age_groups": len(generator.pronunciation_data),
            "total_words": sum(len(data['words']) for data in generator.pronunciation_data.values()),
            "total_errors": sum(
                sum(len(errors) for errors in data['common_errors'].values()) 
                for data in generator.pronunciation_data.values()
            )
        },
        "age_groups": list(generator.pronunciation_data.keys()),
        "error_types": generator.error_types,
        "sample_data": {
            age_group: {
                "words": data['words'][:3],  # 每个年龄段显示3个词汇
                "sample_errors": {
                    word: data['common_errors'].get(word, [])[:2]  # 每个词汇显示2个错误
                    for word in data['words'][:2]  # 只显示前2个词汇的错误
                }
            }
            for age_group, data in list(generator.pronunciation_data.items())[:5]  # 显示前5个年龄段
        }
    }
    
    # 保存到文件
    with open("data/extended_pronunciation_sample.json", "w", encoding="utf-8") as f:
        json.dump(sample_data, f, ensure_ascii=False, indent=2)
    
    print("✅ 示例数据已保存到: data/extended_pronunciation_sample.json")
    print(f"📊 包含 {sample_data['data_info']['total_words']} 个词汇")
    print(f"🔢 包含 {sample_data['data_info']['total_errors']} 个错误示例")

def main():
    """主演示函数"""
    print("🚀 扩展后的Qwen发音纠正生成器完整演示")
    print("=" * 80)
    
    # 1. 展示扩展后的数据内容
    demonstrate_extended_data()
    
    # 2. 展示专业错误分类
    demonstrate_error_types()
    
    # 3. 展示年龄特异性生成
    demonstrate_age_specific_generation()
    
    # 4. 展示智能错误生成
    demonstrate_generic_error_generation()
    
    # 5. 展示prompt生成
    create_sample_prompt()
    
    # 6. 保存示例数据
    save_sample_data()
    
    print("\n🎉 演示完成！")
    print("\n📋 使用建议:")
    print("1. 设置您的Qwen API密钥")
    print("2. 运行 python3 test_qwen_generator.py 进行测试")
    print("3. 使用 python3 scripts/data_generation/qwen_pronunciation_generator.py 批量生成")
    print("4. 查看 PRONUNCIATION_ERROR_PATTERNS.md 了解详细的错误模式")

if __name__ == "__main__":
    main()
