#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen发音纠正生成器测试脚本
用于快速测试API连接和生成功能
"""

import os
import sys
import json
from datetime import datetime

# 添加项目路径
sys.path.append('scripts/data_generation')
from qwen_pronunciation_generator import QwenPronunciationGenerator

def test_api_connection(api_key: str):
    """测试API连接"""
    print("🔍 测试API连接...")
    
    generator = QwenPronunciationGenerator(api_key)
    
    # 简单的测试prompt
    test_prompt = "请简单回答：你好，我是测试API连接。"
    
    response = generator.call_qwen_api(test_prompt)
    
    if response:
        print("✅ API连接成功！")
        print(f"测试响应: {response[:100]}...")
        return True
    else:
        print("❌ API连接失败！")
        return False

def test_single_generation(api_key: str):
    """测试单个案例生成"""
    print("\n🎯 测试单个案例生成...")
    
    generator = QwenPronunciationGenerator(api_key)
    
    # 生成一个18个月的案例
    case = generator.generate_pronunciation_case(18)
    
    if case:
        print("✅ 案例生成成功！")
        print(f"目标词汇: {case['case_info']['target_word']}")
        print(f"错误发音: {case['case_info']['mispronunciation']}")
        print(f"生成时间: {case['source']['generation_date']}")
        
        # 显示生成的指导内容（前200字符）
        guidance = case['llm_guidance']['raw_response']
        print(f"指导内容预览: {guidance[:200]}...")
        
        return case
    else:
        print("❌ 案例生成失败！")
        return None

def test_batch_generation(api_key: str, num_cases: int = 3):
    """测试批量生成"""
    print(f"\n📦 测试批量生成 ({num_cases}个案例)...")
    
    generator = QwenPronunciationGenerator(api_key)
    
    cases = generator.generate_batch_cases(
        num_cases=num_cases,
        age_range=(15, 30)
    )
    
    if cases:
        print(f"✅ 批量生成成功！生成了 {len(cases)} 个案例")
        
        # 统计信息
        words = [case['case_info']['target_word'] for case in cases]
        ages = [case['case_info']['child_age_months'] for case in cases]
        
        print(f"生成的词汇: {', '.join(words)}")
        print(f"年龄分布: {', '.join(map(str, ages))}个月")
        
        return cases
    else:
        print("❌ 批量生成失败！")
        return []

def save_test_results(cases: list, filename: str = "test_results.json"):
    """保存测试结果"""
    if not cases:
        print("没有数据需要保存")
        return
    
    output_path = f"data/{filename}"
    
    # 确保目录存在
    os.makedirs("data", exist_ok=True)
    
    test_data = {
        "test_info": {
            "test_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "total_cases": len(cases),
            "test_type": "Qwen API生成测试"
        },
        "cases": cases
    }
    
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    
    print(f"📁 测试结果已保存到: {output_path}")

def main():
    """主测试函数"""
    print("🚀 Qwen发音纠正生成器测试")
    print("=" * 50)
    
    # 获取API密钥
    api_key = os.getenv("QWEN_API_KEY")
    
    if not api_key:
        print("❌ 未找到API密钥！")
        print("请设置环境变量: export QWEN_API_KEY='your_api_key_here'")
        print("或者在下面直接输入:")
        api_key = input("请输入您的Qwen API密钥: ").strip()
        
        if not api_key:
            print("❌ 未提供API密钥，测试终止")
            return
    
    print(f"🔑 使用API密钥: {api_key[:10]}...{api_key[-4:]}")
    
    # 测试步骤
    test_results = []
    
    # 1. 测试API连接
    if not test_api_connection(api_key):
        print("❌ API连接测试失败，请检查密钥和网络连接")
        return
    
    # 2. 测试单个生成
    single_case = test_single_generation(api_key)
    if single_case:
        test_results.append(single_case)
    
    # 3. 测试批量生成
    batch_cases = test_batch_generation(api_key, num_cases=2)
    test_results.extend(batch_cases)
    
    # 4. 保存测试结果
    if test_results:
        save_test_results(test_results)
        
        print("\n🎉 测试完成！")
        print(f"总共生成了 {len(test_results)} 个案例")
        print("您可以查看生成的数据质量，然后进行大批量生成")
    else:
        print("\n❌ 测试失败，没有成功生成任何案例")
        print("请检查API密钥、网络连接和配置")

if __name__ == "__main__":
    main()
