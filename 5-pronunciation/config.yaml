# 婴幼儿发音纠正数据集构建项目配置文件

project:
  name: "婴幼儿发音纠正指导数据集"
  version: "1.0.0"
  description: "用于训练发音纠正指导LLM的综合数据集"
  author: "数据集构建团队"
  created_date: "2024-01-15"

# 数据收集配置
data_collection:
  # 官方数据源
  official_sources:
    - name: "国家卫生健康委员会"
      base_url: "https://www.nhc.gov.cn"
      search_keywords: ["儿童发展", "语言发育", "早期干预"]
      credibility_score: 10
      
    - name: "教育部"
      base_url: "https://www.moe.gov.cn"
      search_keywords: ["学前教育", "儿童发展指南"]
      credibility_score: 10
      
    - name: "联合国儿童基金会"
      base_url: "https://www.unicef.cn"
      search_keywords: ["儿童发展里程碑", "早期发展"]
      credibility_score: 9

  # 专业文献数据源
  literature_sources:
    - name: "中华儿科杂志"
      search_keywords: ["构音障碍", "语言发育", "发音纠正"]
      min_impact_factor: 2.0
      credibility_score: 9
      
    - name: "中国康复医学杂志"
      search_keywords: ["儿童康复", "语言治疗", "发音训练"]
      min_impact_factor: 1.5
      credibility_score: 8

  # 问答平台配置
  qa_platforms:
    - name: "好大夫在线"
      base_url: "https://www.haodf.com"
      search_keywords: ["儿童发音", "说话不清楚", "构音障碍"]
      min_answerer_level: "主治医师"
      credibility_score: 8
      
    - name: "春雨医生"
      base_url: "https://www.chunyuyisheng.com"
      search_keywords: ["儿童语言发育", "发音问题"]
      min_answerer_level: "医师"
      credibility_score: 7

# 数据处理配置
data_processing:
  # 年龄范围设置
  age_range:
    min_months: 0
    max_months: 72
    
  # 语音标记规范
  phoneme_notation:
    primary: "IPA"  # 国际音标
    secondary: "pinyin"  # 拼音辅助
    
  # 文本清理规则
  text_cleaning:
    remove_html: true
    normalize_whitespace: true
    remove_special_chars: true
    min_text_length: 20
    max_text_length: 5000

# 质量控制配置
quality_control:
  # 可信度评分阈值
  credibility_thresholds:
    official_standard: 8
    professional_literature: 7
    qa_pair: 6
    
  # 完整性检查
  completeness_check:
    required_fields_coverage: 0.9
    content_depth_score: 0.8
    
  # 安全性检查
  safety_check:
    unsafe_keywords:
      - "强制"
      - "惩罚"
      - "打骂"
      - "威胁"
      - "过度训练"
    
  # 专家审核配置
  expert_review:
    medical_expert: true
    speech_therapist: true
    child_psychologist: true
    data_scientist: true

# 数据存储配置
storage:
  # 文件格式
  format: "json"
  encoding: "utf-8"
  
  # 目录结构
  directories:
    raw_data: "data/raw"
    processed_data: "data/processed"
    validated_data: "data/validated"
    final_dataset: "data/final"
    
  # 备份配置
  backup:
    enabled: true
    frequency: "daily"
    retention_days: 30

# 验证配置
validation:
  # JSON Schema验证
  schema_validation: true
  
  # 数据一致性检查
  consistency_check:
    age_range_logic: true
    phoneme_notation: true
    recommendation_safety: true
    
  # 统计验证
  statistical_validation:
    age_distribution_check: true
    credibility_distribution_check: true
    content_type_balance_check: true

# 输出配置
output:
  # 数据集格式
  dataset_formats:
    - "json"
    - "jsonl"
    - "csv"
    
  # 文档生成
  documentation:
    data_dictionary: true
    usage_guide: true
    quality_report: true
    
  # 示例数据
  sample_data:
    size: 100
    representative: true

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/dataset_construction.log"
  max_size: "10MB"
  backup_count: 5

# 性能配置
performance:
  # 并发设置
  max_workers: 4
  request_delay: 2.0  # 秒
  
  # 内存管理
  batch_size: 100
  max_memory_usage: "2GB"
  
  # 缓存设置
  cache_enabled: true
  cache_size: "500MB"

# 伦理和法律配置
ethics:
  # 隐私保护
  privacy_protection:
    data_anonymization: true
    remove_personal_info: true
    consent_required: true
    
  # 版权遵守
  copyright_compliance:
    fair_use_only: true
    attribution_required: true
    commercial_use: false
    
  # 数据使用限制
  usage_restrictions:
    research_only: false
    educational_use: true
    commercial_use: false
    redistribution: "with_permission"

# 国际化配置
internationalization:
  primary_language: "zh-CN"
  supported_languages:
    - "zh-CN"
    - "en-US"
  
  # 本地化设置
  localization:
    date_format: "YYYY-MM-DD"
    number_format: "chinese"
    currency: "CNY"

# 扩展配置
extensions:
  # 插件支持
  plugins_enabled: true
  
  # API接口
  api_enabled: false
  api_version: "v1"
  
  # 机器学习集成
  ml_integration:
    feature_extraction: true
    auto_labeling: false
    quality_prediction: true

# 测试配置
testing:
  # 单元测试
  unit_tests: true
  
  # 集成测试
  integration_tests: true
  
  # 性能测试
  performance_tests: true
  
  # 数据质量测试
  data_quality_tests: true

# 部署配置
deployment:
  environment: "development"
  
  # 容器化
  containerization:
    enabled: false
    platform: "docker"
    
  # 云服务
  cloud_services:
    enabled: false
    provider: "aliyun"
