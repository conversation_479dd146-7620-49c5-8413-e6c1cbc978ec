#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿发音纠正指导数据集使用示例
Example usage for Infant Pronunciation Correction Dataset
"""

import json
import os
from typing import List, Dict, Any
from collections import defaultdict, Counter
import random

class InfantPronunciationDataset:
    """婴幼儿发音纠正数据集加载器"""
    
    def __init__(self, dataset_dir: str = "."):
        self.dataset_dir = dataset_dir
        self.data = {}
        self.load_all_data()
    
    def load_all_data(self):
        """加载所有数据文件"""
        data_files = {
            'comprehensive': 'final_0_3_pronunciation_dataset.json',
            'generated_large': 'generated_cases_final_200.json', 
            'generated_standard': 'qwen_generated_pronunciation_cases.json',
            'real_clinical': 'collected_real_data.json',
            'core_examples': 'pronunciation_correction_0_3_years.json'
        }
        
        for key, filename in data_files.items():
            filepath = os.path.join(self.dataset_dir, filename)
            if os.path.exists(filepath):
                with open(filepath, 'r', encoding='utf-8') as f:
                    self.data[key] = json.load(f)
                print(f"✅ 加载 {filename}")
            else:
                print(f"❌ 文件不存在: {filename}")
    
    def get_all_cases(self) -> List[Dict[str, Any]]:
        """获取所有案例数据"""
        all_cases = []
        
        # 从综合数据集获取
        if 'comprehensive' in self.data and 'data' in self.data['comprehensive']:
            all_cases.extend(self.data['comprehensive']['data'])
        
        # 从生成数据集获取
        for key in ['generated_large', 'generated_standard']:
            if key in self.data and 'cases' in self.data[key]:
                all_cases.extend(self.data[key]['cases'])
        
        # 从真实数据获取
        if 'real_clinical' in self.data:
            if isinstance(self.data['real_clinical'], list):
                all_cases.extend(self.data['real_clinical'])
            elif 'data' in self.data['real_clinical']:
                all_cases.extend(self.data['real_clinical']['data'])
        
        return all_cases
    
    def filter_by_age(self, cases: List[Dict], min_age: int = 12, max_age: int = 36) -> List[Dict]:
        """按年龄过滤案例"""
        filtered = []
        for case in cases:
            if 'case_info' in case and 'child_age_months' in case['case_info']:
                age = case['case_info']['child_age_months']
                if min_age <= age <= max_age:
                    filtered.append(case)
        return filtered
    
    def filter_by_quality(self, cases: List[Dict], min_score: int = 7) -> List[Dict]:
        """按质量分数过滤案例"""
        filtered = []
        for case in cases:
            if 'source' in case and 'credibility_score' in case['source']:
                score = case['source']['credibility_score']
                if score >= min_score:
                    filtered.append(case)
            else:
                # 如果没有质量分数，默认包含
                filtered.append(case)
        return filtered
    
    def group_by_age(self, cases: List[Dict]) -> Dict[int, List[Dict]]:
        """按年龄分组"""
        age_groups = defaultdict(list)
        for case in cases:
            if 'case_info' in case and 'child_age_months' in case['case_info']:
                age = case['case_info']['child_age_months']
                age_groups[age].append(case)
        return dict(age_groups)
    
    def group_by_error_type(self, cases: List[Dict]) -> Dict[str, List[Dict]]:
        """按错误类型分组"""
        error_groups = defaultdict(list)
        for case in cases:
            if 'case_info' in case and 'error_type' in case['case_info']:
                error_type = case['case_info']['error_type']
                error_groups[error_type].append(case)
            else:
                error_groups['unknown'].append(case)
        return dict(error_groups)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取数据集统计信息"""
        all_cases = self.get_all_cases()
        
        # 基础统计
        stats = {
            'total_cases': len(all_cases),
            'age_distribution': {},
            'error_type_distribution': {},
            'quality_distribution': {},
            'word_frequency': {}
        }
        
        # 年龄分布
        ages = []
        for case in all_cases:
            if 'case_info' in case and 'child_age_months' in case['case_info']:
                ages.append(case['case_info']['child_age_months'])
        
        age_counter = Counter(ages)
        stats['age_distribution'] = dict(age_counter)
        
        # 错误类型分布
        error_types = []
        for case in all_cases:
            if 'case_info' in case and 'error_type' in case['case_info']:
                error_types.append(case['case_info']['error_type'])
        
        error_counter = Counter(error_types)
        stats['error_type_distribution'] = dict(error_counter)
        
        # 质量分布
        quality_scores = []
        for case in all_cases:
            if 'source' in case and 'credibility_score' in case['source']:
                quality_scores.append(case['source']['credibility_score'])
        
        quality_counter = Counter(quality_scores)
        stats['quality_distribution'] = dict(quality_counter)
        
        # 词汇频率
        words = []
        for case in all_cases:
            if 'case_info' in case and 'target_word' in case['case_info']:
                words.append(case['case_info']['target_word'])
        
        word_counter = Counter(words)
        stats['word_frequency'] = dict(word_counter.most_common(20))
        
        return stats
    
    def create_training_split(self, test_ratio: float = 0.2, val_ratio: float = 0.1, 
                            random_seed: int = 42) -> Dict[str, List[Dict]]:
        """创建训练/验证/测试数据分割"""
        random.seed(random_seed)
        
        all_cases = self.get_all_cases()
        high_quality_cases = self.filter_by_quality(all_cases, min_score=7)
        
        # 按年龄分层采样
        age_groups = self.group_by_age(high_quality_cases)
        
        train_data = []
        val_data = []
        test_data = []
        
        for age, cases in age_groups.items():
            random.shuffle(cases)
            
            n_cases = len(cases)
            n_test = int(n_cases * test_ratio)
            n_val = int(n_cases * val_ratio)
            n_train = n_cases - n_test - n_val
            
            test_data.extend(cases[:n_test])
            val_data.extend(cases[n_test:n_test + n_val])
            train_data.extend(cases[n_test + n_val:])
        
        return {
            'train': train_data,
            'validation': val_data,
            'test': test_data
        }
    
    def export_for_training(self, output_dir: str = "training_data"):
        """导出用于训练的数据"""
        os.makedirs(output_dir, exist_ok=True)
        
        # 创建数据分割
        splits = self.create_training_split()
        
        # 保存分割数据
        for split_name, split_data in splits.items():
            output_file = os.path.join(output_dir, f"{split_name}.json")
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(split_data, f, ensure_ascii=False, indent=2)
            print(f"✅ 导出 {split_name}: {len(split_data)} 条记录")
        
        # 保存统计信息
        stats = self.get_statistics()
        stats_file = os.path.join(output_dir, "statistics.json")
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        print(f"✅ 导出统计信息: {stats_file}")

def main():
    """主函数 - 使用示例"""
    print("🚀 婴幼儿发音纠正数据集使用示例")
    print("=" * 50)
    
    # 初始化数据集
    dataset = InfantPronunciationDataset()
    
    # 获取所有案例
    all_cases = dataset.get_all_cases()
    print(f"\n📊 总案例数: {len(all_cases)}")
    
    # 按年龄过滤
    cases_18_24 = dataset.filter_by_age(all_cases, min_age=18, max_age=24)
    print(f"📅 18-24个月案例: {len(cases_18_24)}")
    
    # 按质量过滤
    high_quality = dataset.filter_by_quality(all_cases, min_score=7)
    print(f"⭐ 高质量案例 (≥7分): {len(high_quality)}")
    
    # 获取统计信息
    stats = dataset.get_statistics()
    print(f"\n📈 数据集统计:")
    print(f"  年龄分布: {len(stats['age_distribution'])} 个年龄段")
    print(f"  错误类型: {len(stats['error_type_distribution'])} 种")
    print(f"  质量分布: {stats['quality_distribution']}")
    
    # 显示最常见的词汇
    print(f"\n🔤 最常见词汇 (前10):")
    for word, count in list(stats['word_frequency'].items())[:10]:
        print(f"  {word}: {count}次")
    
    # 创建训练数据分割
    splits = dataset.create_training_split()
    print(f"\n🎯 训练数据分割:")
    for split_name, split_data in splits.items():
        print(f"  {split_name}: {len(split_data)} 条")
    
    # 导出训练数据
    dataset.export_for_training("training_data")
    print(f"\n✅ 训练数据已导出到 training_data/ 目录")
    
    # 显示示例案例
    if all_cases:
        print(f"\n📝 示例案例:")
        example = all_cases[0]
        if 'case_info' in example:
            case_info = example['case_info']
            print(f"  年龄: {case_info.get('child_age_months', 'N/A')}个月")
            print(f"  目标词: {case_info.get('target_word', 'N/A')}")
            print(f"  错误发音: {case_info.get('mispronunciation', 'N/A')}")
            print(f"  正确发音: {case_info.get('correct_pronunciation', 'N/A')}")

if __name__ == "__main__":
    main()
