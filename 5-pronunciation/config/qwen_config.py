#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen API配置文件
"""

import os
from typing import Dict, Any

class QwenConfig:
    """Qwen API配置管理"""
    
    def __init__(self):
        # API配置
        self.API_KEY = "sk-5eba46fbcff649d5bf28313bc865de10"
        self.API_URL = "https://dashscope.aliyuncs.com/api/v1/services/aigc/text-generation/generation"
        
        # 模型参数
        self.MODEL_PARAMS = {
            "model": "qwen-plus-2025-01-25",  # 可选: qwen-turbo, qwen-plus, qwen-max
            "temperature": 0.7,     # 控制创造性，0-1之间
            "max_tokens": 2000,     # 最大输出长度
            "top_p": 0.9           # 核采样参数
        }
        
        # 生成参数
        self.GENERATION_PARAMS = {
            "max_retries": 3,       # 最大重试次数
            "retry_delay": 2,       # 重试延迟（秒）
            "request_timeout": 30,  # 请求超时（秒）
            "rate_limit_delay": 2   # API调用间隔（秒）
        }
        
        # 数据生成配置
        self.DATA_CONFIG = {
            "default_batch_size": 10,
            "age_range": (12, 36),
            "output_dir": "data/qwen_generated"
        }
    
    def get_headers(self) -> Dict[str, str]:
        """获取API请求头"""
        return {
            "Authorization": f"Bearer {self.API_KEY}",
            "Content-Type": "application/json"
        }
    
    def validate_config(self) -> bool:
        """验证配置是否有效"""
        if not self.API_KEY:
            print("错误: 未设置QWEN_API_KEY环境变量")
            print("请设置环境变量: export QWEN_API_KEY='your_api_key_here'")
            return False
        
        if len(self.API_KEY) < 10:
            print("错误: API密钥格式不正确")
            return False
        
        return True
    
    def get_model_payload(self, prompt: str) -> Dict[str, Any]:
        """构建API请求载荷"""
        return {
            "model": self.MODEL_PARAMS["model"],
            "input": {
                "messages": [
                    {
                        "role": "user",
                        "content": prompt
                    }
                ]
            },
            "parameters": {
                "temperature": self.MODEL_PARAMS["temperature"],
                "max_tokens": self.MODEL_PARAMS["max_tokens"],
                "top_p": self.MODEL_PARAMS["top_p"]
            }
        }

# 全局配置实例
config = QwenConfig()
