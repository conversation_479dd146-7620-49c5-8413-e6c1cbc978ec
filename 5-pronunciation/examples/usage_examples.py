#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿发音纠正数据集使用示例
展示如何使用数据集进行各种查询和分析
"""

import json
import sys
import os
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class PronunciationDatasetHelper:
    """发音纠正数据集助手类"""
    
    def __init__(self, dataset_path: str):
        """初始化数据集助手"""
        self.dataset = self._load_dataset(dataset_path)
        self.official_standards = [item for item in self.dataset if item["type"] == "official_standard"]
        self.professional_literature = [item for item in self.dataset if item["type"] == "professional_literature"]
        self.qa_pairs = [item for item in self.dataset if item["type"] == "qa_pair"]
    
    def _load_dataset(self, path: str) -> List[Dict[str, Any]]:
        """加载数据集"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except FileNotFoundError:
            print(f"数据集文件未找到: {path}")
            return []
        except json.JSONDecodeError:
            print(f"数据集文件格式错误: {path}")
            return []
    
    def get_age_appropriate_standards(self, age_months: int) -> List[Dict[str, Any]]:
        """获取特定年龄的发育标准"""
        appropriate_standards = []
        
        for standard in self.official_standards:
            content = standard.get("content", {})
            age_range = content.get("age_range", {})
            
            min_age = age_range.get("min_months", 0)
            max_age = age_range.get("max_months", 72)
            
            if min_age <= age_months <= max_age:
                appropriate_standards.append(standard)
        
        return appropriate_standards
    
    def find_similar_cases(self, child_age: int, problem_keywords: List[str]) -> List[Dict[str, Any]]:
        """查找相似案例"""
        similar_cases = []
        
        for qa in self.qa_pairs:
            question = qa.get("question", {})
            child_info = question.get("child_info", {})
            problem_desc = question.get("problem_description", "")
            
            # 年龄匹配（±6个月）
            qa_age = child_info.get("age_months", 0)
            if abs(qa_age - child_age) <= 6:
                # 关键词匹配
                for keyword in problem_keywords:
                    if keyword in problem_desc:
                        similar_cases.append(qa)
                        break
        
        return similar_cases
    
    def get_intervention_methods(self, problem_type: str) -> List[Dict[str, Any]]:
        """获取干预方法"""
        methods = []
        
        for literature in self.professional_literature:
            content = literature.get("content", {})
            topic = content.get("topic", "")
            intervention_methods = content.get("intervention_methods", [])
            
            if problem_type.lower() in topic.lower():
                methods.extend(intervention_methods)
        
        return methods
    
    def assess_pronunciation_development(self, child_age: int, current_abilities: List[str]) -> Dict[str, Any]:
        """评估发音发展状况"""
        assessment = {
            "age_months": child_age,
            "assessment_date": datetime.now().strftime("%Y-%m-%d"),
            "expected_abilities": [],
            "current_status": "评估中",
            "recommendations": []
        }
        
        # 获取该年龄段的标准
        standards = self.get_age_appropriate_standards(child_age)
        
        for standard in standards:
            content = standard.get("content", {})
            milestones = content.get("developmental_milestones", [])
            pronunciation_standards = content.get("pronunciation_standards", [])
            
            # 收集预期能力
            for milestone in milestones:
                if "语音" in milestone.get("category", ""):
                    assessment["expected_abilities"].append(milestone.get("milestone", ""))
            
            # 检查发音标准
            for std in pronunciation_standards:
                expected_age = std.get("expected_age_months", 0)
                if expected_age <= child_age:
                    phoneme = std.get("phoneme", "")
                    if phoneme not in str(current_abilities):
                        assessment["recommendations"].append({
                            "type": "发音练习",
                            "target": phoneme,
                            "methods": std.get("correction_methods", [])
                        })
        
        # 评估状态
        if len(assessment["recommendations"]) == 0:
            assessment["current_status"] = "发展正常"
        elif len(assessment["recommendations"]) <= 2:
            assessment["current_status"] = "轻微落后"
        else:
            assessment["current_status"] = "需要关注"
        
        return assessment
    
    def generate_training_plan(self, child_age: int, problem_areas: List[str]) -> Dict[str, Any]:
        """生成训练计划"""
        plan = {
            "child_age_months": child_age,
            "problem_areas": problem_areas,
            "training_phases": [],
            "daily_activities": [],
            "progress_indicators": [],
            "duration_weeks": 8
        }
        
        # 查找相关干预方法
        for problem in problem_areas:
            methods = self.get_intervention_methods(problem)
            
            for method in methods:
                phase = {
                    "phase_name": method.get("method_name", "训练阶段"),
                    "description": method.get("description", ""),
                    "duration_weeks": method.get("duration_weeks", 4),
                    "frequency": method.get("frequency", "每日练习"),
                    "activities": self._generate_activities(problem, child_age)
                }
                plan["training_phases"].append(phase)
        
        # 生成日常活动建议
        plan["daily_activities"] = self._generate_daily_activities(child_age, problem_areas)
        
        # 生成进度指标
        plan["progress_indicators"] = self._generate_progress_indicators(problem_areas)
        
        return plan
    
    def _generate_activities(self, problem_area: str, age_months: int) -> List[str]:
        """生成具体活动建议"""
        activities = []
        
        if "发音" in problem_area:
            if age_months < 24:
                activities = [
                    "模仿简单音节",
                    "听音乐和儿歌",
                    "口部按摩游戏",
                    "吹泡泡练习"
                ]
            elif age_months < 36:
                activities = [
                    "发音对比游戏",
                    "镜子观察练习",
                    "舌头运动操",
                    "词汇重复练习"
                ]
            else:
                activities = [
                    "绕口令练习",
                    "故事复述",
                    "发音纠正游戏",
                    "同伴交流练习"
                ]
        
        return activities
    
    def _generate_daily_activities(self, age_months: int, problem_areas: List[str]) -> List[Dict[str, Any]]:
        """生成日常活动计划"""
        activities = [
            {
                "time": "早晨",
                "activity": "晨间问候和简单对话",
                "duration": "10分钟",
                "focus": "自然语言交流"
            },
            {
                "time": "上午",
                "activity": "针对性发音练习",
                "duration": "15分钟",
                "focus": "重点音素训练"
            },
            {
                "time": "下午",
                "activity": "游戏中的语言练习",
                "duration": "20分钟",
                "focus": "在游戏中自然练习"
            },
            {
                "time": "晚上",
                "activity": "睡前故事和儿歌",
                "duration": "15分钟",
                "focus": "语言输入和模仿"
            }
        ]
        
        return activities
    
    def _generate_progress_indicators(self, problem_areas: List[str]) -> List[Dict[str, Any]]:
        """生成进度评估指标"""
        indicators = []
        
        for area in problem_areas:
            if "发音" in area:
                indicators.append({
                    "indicator": "发音准确率",
                    "measurement": "每周记录目标音素的准确发音次数",
                    "target": "准确率提高20%",
                    "timeframe": "4周"
                })
            
            if "词汇" in area:
                indicators.append({
                    "indicator": "词汇量增长",
                    "measurement": "记录新学会的词汇数量",
                    "target": "每周新增5-10个词汇",
                    "timeframe": "持续监测"
                })
        
        return indicators

def example_usage():
    """使用示例"""
    print("=== 婴幼儿发音纠正数据集使用示例 ===\n")
    
    # 初始化助手
    helper = PronunciationDatasetHelper("examples/sample_data.json")
    
    if not helper.dataset:
        print("无法加载数据集，请检查文件路径")
        return
    
    print(f"数据集加载成功，共 {len(helper.dataset)} 条记录")
    print(f"- 官方标准: {len(helper.official_standards)} 条")
    print(f"- 专业文献: {len(helper.professional_literature)} 条")
    print(f"- 问答对: {len(helper.qa_pairs)} 条\n")
    
    # 示例1: 查询特定年龄的发育标准
    print("=== 示例1: 查询30个月儿童的发育标准 ===")
    standards = helper.get_age_appropriate_standards(30)
    for standard in standards:
        source = standard.get("source", {})
        print(f"来源: {source.get('organization', '未知')}")
        print(f"文档: {source.get('document_title', '未知')}")
        
        content = standard.get("content", {})
        milestones = content.get("developmental_milestones", [])
        for milestone in milestones[:2]:  # 只显示前2个
            print(f"- {milestone.get('milestone', '')}")
        print()
    
    # 示例2: 查找相似案例
    print("=== 示例2: 查找相似案例 ===")
    similar_cases = helper.find_similar_cases(36, ["发音", "说话"])
    for case in similar_cases[:1]:  # 只显示第一个案例
        question = case.get("question", {})
        child_info = question.get("child_info", {})
        print(f"案例: {child_info.get('age_months', 0)}个月儿童")
        print(f"问题: {question.get('problem_description', '')[:100]}...")
        
        answer = case.get("answer", {})
        assessment = answer.get("assessment", {})
        print(f"评估: {assessment.get('explanation', '')[:100]}...")
        print()
    
    # 示例3: 发音发展评估
    print("=== 示例3: 发音发展评估 ===")
    assessment = helper.assess_pronunciation_development(30, ["妈妈", "爸爸", "水水"])
    print(f"儿童年龄: {assessment['age_months']}个月")
    print(f"发展状况: {assessment['current_status']}")
    print(f"建议数量: {len(assessment['recommendations'])}")
    for rec in assessment['recommendations'][:2]:  # 只显示前2个建议
        print(f"- 练习 {rec.get('target', '')} 音")
    print()
    
    # 示例4: 生成训练计划
    print("=== 示例4: 生成训练计划 ===")
    plan = helper.generate_training_plan(30, ["构音障碍"])
    print(f"训练计划时长: {plan['duration_weeks']}周")
    print(f"训练阶段数: {len(plan['training_phases'])}")
    print(f"日常活动数: {len(plan['daily_activities'])}")
    
    for activity in plan['daily_activities'][:2]:  # 只显示前2个活动
        print(f"- {activity['time']}: {activity['activity']} ({activity['duration']})")
    print()

if __name__ == "__main__":
    example_usage()
