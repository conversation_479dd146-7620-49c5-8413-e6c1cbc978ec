#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可靠版本：生成300个高质量的婴幼儿精细运动行为分析+指导问答
采用更简化的prompt和更稳定的API调用策略
"""

import json
import os
import time
import dashscope
from dashscope import Generation

class ReliableBehaviorQAGenerator:
    def __init__(self, api_key: str = None):
        """初始化可靠版本生成器"""
        if api_key:
            dashscope.api_key = api_key
        elif os.getenv('QWEN_API_KEY'):
            dashscope.api_key = os.getenv('QWEN_API_KEY')
        else:
            dashscope.api_key = 'sk-5eba46fbcff649d5bf28313bc865de10'
        
        self.target_samples = 300
        self.generated_data = {
            'metadata': {
                'name': '婴幼儿精细运动行为分析+指导问答数据集 - 可靠版300条',
                'version': '3.0',
                'target_samples': 300,
                'description': '基于实际行为观察的高质量精细运动分析和指导问答',
                'created_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'enhancement_model': 'qwen-max',
                'focus': '行为观察+专业分析+具体指导'
            },
            'data': []
        }
    
    def call_qwen_api_simple(self, prompt: str, max_retries: int = 3) -> str:
        """简化的API调用，提高成功率"""
        for attempt in range(max_retries):
            try:
                response = Generation.call(
                    model='qwen-max',
                    prompt=prompt,
                    max_tokens=2000,
                    temperature=0.7
                )
                
                if response.status_code == 200:
                    content = response.output.text
                    if content and len(content) > 100:
                        return content
                    else:
                        print(f"内容过短，重试 {attempt + 1}/{max_retries}")
                else:
                    print(f"API失败: {response.message}, 重试 {attempt + 1}/{max_retries}")
                    
            except Exception as e:
                print(f"API异常: {e}, 重试 {attempt + 1}/{max_retries}")
            
            if attempt < max_retries - 1:
                time.sleep(3)  # 重试间隔
        
        return ""
    
    def generate_single_qa(self, age_months: int, scenario_type: str) -> dict:
        """生成单个问答对"""
        prompt = f"""
你是儿童发育专家。请为{age_months}个月宝宝创建1个基于实际行为观察的精细运动分析问答。

场景类型：{scenario_type}

要求：
1. 问题：家长描述具体观察到的宝宝行为，表达困惑
2. 答案：包含四个部分
   - 行为分析：解释行为意义
   - 发育评估：判断是否正常
   - 指导建议：3个具体方法
   - 观察要点：后续观察重点

格式：
Q: [家长观察描述]
A: **行为分析**：... **发育评估**：... **指导建议**：... **观察要点**：...
"""
        
        content = self.call_qwen_api_simple(prompt)
        
        if content and 'Q:' in content and 'A:' in content:
            lines = content.strip().split('\n')
            question = ""
            answer = ""
            
            current_section = None
            for line in lines:
                if line.startswith('Q:'):
                    current_section = 'question'
                    question = line[2:].strip()
                elif line.startswith('A:'):
                    current_section = 'answer'
                    answer = line[2:].strip()
                elif current_section == 'question' and line.strip():
                    question += " " + line.strip()
                elif current_section == 'answer' and line.strip():
                    answer += " " + line.strip()
            
            if question and answer and len(question) > 30 and len(answer) > 100:
                return {
                    'question': question,
                    'answer': answer,
                    'category': 'behavior_analysis',
                    'age_group': f"{age_months}个月",
                    'scenario_type': scenario_type,
                    'difficulty': 'intermediate',
                    'generated_by': 'qwen_api_single'
                }
        
        return None
    
    def generate_batch_qa(self, count: int = 5) -> list:
        """批量生成问答对"""
        prompt = f"""
你是儿童发育专家。请创建{count}个基于实际行为观察的婴幼儿精细运动分析问答。

涵盖不同月龄：6个月、12个月、18个月、24个月、30个月

每个问答要求：
1. 问题：家长描述具体观察到的宝宝行为
2. 答案：包含行为分析、发育评估、指导建议、观察要点四部分

格式：
Q: [家长观察]
A: **行为分析**：... **发育评估**：... **指导建议**：... **观察要点**：...
---
"""
        
        qa_pairs = []
        content = self.call_qwen_api_simple(prompt)
        
        if content:
            qa_blocks = content.split('---')
            for block in qa_blocks:
                if 'Q:' in block and 'A:' in block:
                    lines = block.strip().split('\n')
                    question = ""
                    answer = ""
                    
                    current_section = None
                    for line in lines:
                        if line.startswith('Q:'):
                            current_section = 'question'
                            question = line[2:].strip()
                        elif line.startswith('A:'):
                            current_section = 'answer'
                            answer = line[2:].strip()
                        elif current_section == 'question' and line.strip():
                            question += " " + line.strip()
                        elif current_section == 'answer' and line.strip():
                            answer += " " + line.strip()
                    
                    if question and answer and len(question) > 30 and len(answer) > 100:
                        # 尝试从问题中提取年龄
                        age_group = 'general'
                        for age in ['6个月', '12个月', '18个月', '24个月', '30个月']:
                            if age in question:
                                age_group = age
                                break
                        
                        qa_pairs.append({
                            'question': question,
                            'answer': answer,
                            'category': 'behavior_analysis',
                            'age_group': age_group,
                            'difficulty': 'intermediate',
                            'generated_by': 'qwen_api_batch'
                        })
        
        return qa_pairs
    
    def generate_300_dataset(self):
        """生成300条数据集"""
        print("🚀 开始生成300条行为分析+指导问答（可靠版本）...")
        
        all_qa_pairs = []
        
        # 方法1：批量生成（主要方法）
        print("📦 批量生成问答...")
        batch_count = 0
        while len(all_qa_pairs) < 250 and batch_count < 60:  # 最多60批次
            batch_count += 1
            print(f"   批次 {batch_count}: ", end="")
            
            batch_qa = self.generate_batch_qa(5)
            all_qa_pairs.extend(batch_qa)
            
            print(f"生成 {len(batch_qa)} 个，累计 {len(all_qa_pairs)} 个")
            
            if len(batch_qa) > 0:
                time.sleep(2)  # 成功后短暂等待
            else:
                time.sleep(5)  # 失败后长等待
        
        print(f"   ✅ 批量生成完成: {len(all_qa_pairs)} 个")
        
        # 方法2：单个生成（补充）
        if len(all_qa_pairs) < 300:
            print("🎯 单个生成补充...")
            remaining = 300 - len(all_qa_pairs)
            
            scenarios = [
                "抓握行为", "手眼协调", "双手配合", "工具使用", "精细控制",
                "力量调节", "空间判断", "模仿学习", "问题解决", "注意集中"
            ]
            
            ages = [6, 8, 10, 12, 15, 18, 21, 24, 27, 30, 33, 36]
            
            for i in range(remaining):
                age = ages[i % len(ages)]
                scenario = scenarios[i % len(scenarios)]
                
                print(f"   单个生成 {i+1}/{remaining}: {age}个月-{scenario}")
                
                single_qa = self.generate_single_qa(age, scenario)
                if single_qa:
                    all_qa_pairs.append(single_qa)
                
                time.sleep(2)
                
                if len(all_qa_pairs) >= 300:
                    break
        
        # 整合数据集
        self.generated_data['metadata']['actual_samples'] = len(all_qa_pairs)
        self.generated_data['data'] = all_qa_pairs
        
        print(f"✅ 数据集生成完成！实际生成 {len(all_qa_pairs)} 条数据")
        
        return self.generated_data
    
    def save_dataset(self, filename: str = 'behavior_analysis_300_reliable.json'):
        """保存数据集"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.generated_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 数据集已保存到: {filename}")
        
        # 生成报告
        self.generate_report()
    
    def generate_report(self):
        """生成报告"""
        data = self.generated_data['data']
        
        # 统计分析
        category_stats = {}
        age_stats = {}
        
        for item in data:
            category = item.get('category', 'unknown')
            category_stats[category] = category_stats.get(category, 0) + 1
            
            age_group = item.get('age_group', 'unknown')
            age_stats[age_group] = age_stats.get(age_group, 0) + 1
        
        report = f"""# 300条婴幼儿精细运动行为分析+指导数据集报告（可靠版）

## 📊 基本信息
- **数据集名称**: {self.generated_data['metadata']['name']}
- **目标样本数**: {self.generated_data['metadata']['target_samples']}
- **实际样本数**: {self.generated_data['metadata']['actual_samples']}
- **生成时间**: {self.generated_data['metadata']['created_time']}
- **生成模型**: {self.generated_data['metadata']['enhancement_model']}

## 📈 数据分布

### 类别分布
"""
        
        for category, count in sorted(category_stats.items()):
            percentage = (count / len(data)) * 100
            report += f"- **{category}**: {count} 个 ({percentage:.1f}%)\n"
        
        report += "\n### 年龄组分布\n"
        for age_group, count in sorted(age_stats.items()):
            percentage = (count / len(data)) * 100
            report += f"- **{age_group}**: {count} 个 ({percentage:.1f}%)\n"
        
        report += f"""
## 🎯 数据特色

### 💡 基于实际行为观察
- 每个问答都包含具体的婴幼儿行为描述
- 真实反映家长的观察和疑问
- 提供专业的行为分析和发育评估

### 🔍 结构化专业分析
每个答案都包含四个部分：
1. **行为分析**：解释行为的发育意义
2. **发育评估**：判断是否符合正常发育水平
3. **指导建议**：提供具体可操作的训练方法
4. **观察要点**：指导后续观察和专业求助时机

### 🚀 应用价值
- **LLM训练**: 训练模型的行为分析和专业判断能力
- **智能问答**: 处理真实的家长咨询场景
- **专业培训**: 教授行为观察和分析技能

## 💡 质量保证
- 基于儿童发育学理论
- 符合临床实践标准
- 语言通俗易懂
- 建议具体可操作

---
生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        with open('behavior_analysis_300_reliable_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("📊 数据集报告已生成: behavior_analysis_300_reliable_report.md")

def main():
    """主函数"""
    print("🎯 可靠版本：生成300条高质量婴幼儿精细运动行为分析+指导问答")
    print("="*80)
    
    generator = ReliableBehaviorQAGenerator()
    
    # 生成300条数据
    generator.generate_300_dataset()
    
    # 保存数据集
    generator.save_dataset()
    
    print("\n🎉 300条高质量行为分析+指导数据集生成完成！")
    print("📁 生成的文件:")
    print("   - behavior_analysis_300_reliable.json (300条数据集)")
    print("   - behavior_analysis_300_reliable_report.md (详细统计报告)")
    print("\n🌟 数据集特色:")
    print("   ✅ 基于实际行为观察")
    print("   ✅ 结构化专业分析")
    print("   ✅ 具体可操作指导")
    print("   ✅ 可靠稳定生成")

if __name__ == "__main__":
    main()
