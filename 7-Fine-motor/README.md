# 网页内容爬虫工具

这是一个用于爬取网页内容的Python脚本，专门用于提取和保存网页文章的结构化信息。

## 功能特点

- 🌐 支持爬取任意网页内容
- 📝 自动提取文章标题、作者、发布日期等元信息
- 🖼️ 提取并保存网页中的图片链接
- 💾 支持多种输出格式（JSON、TXT）
- 🔧 可配置的请求头，模拟真实浏览器访问
- ⚡ 支持自定义文件名和输出路径

## 安装依赖

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install requests beautifulsoup4 lxml
```

## 使用方法

### 基本用法

```bash
python web_scraper.py
```

默认会爬取脚本中预设的URL（健康界网站的宝宝精细动作发育文章）。

### 自定义URL

修改 `web_scraper.py` 文件中的 `main()` 函数：

```python
def main():
    # 修改这里的URL为你想要爬取的网页
    url = "https://example.com/your-target-page"
    
    scraper = WebScraper()
    result = scraper.scrape_article(url, "custom_filename")
```

### 程序化使用

```python
from web_scraper import WebScraper

# 创建爬虫实例
scraper = WebScraper()

# 爬取指定URL
url = "https://example.com/article"
result = scraper.scrape_article(url, "output_filename")

if result:
    print(f"爬取成功！标题：{result['title']}")
    print(f"内容长度：{len(result['content'])} 字符")
    print(f"图片数量：{len(result['images'])} 张")
```

## 输出文件

脚本会生成两个文件：

1. **JSON格式** (`filename.json`)：包含完整的结构化数据
2. **文本格式** (`filename.txt`)：便于阅读的纯文本格式

### JSON文件结构

```json
{
  "url": "原始网页URL",
  "title": "文章标题",
  "author": "作者信息",
  "publish_date": "发布日期",
  "content": "文章正文内容",
  "images": [
    {
      "src": "图片URL",
      "alt": "图片描述",
      "title": "图片标题"
    }
  ],
  "scraped_time": "爬取时间"
}
```

## 示例输出

运行脚本后，你会看到类似以下的输出：

```
开始爬取: https://www.cn-healthcare.com/articlewm/20210505/content-1216802.html
数据已保存到: baby_fine_motor_development.json 和 baby_fine_motor_development.txt

爬取成功!
标题: 0-3岁宝宝精细动作发育标准+测评方法+训练大全合集，家长快来自测
作者: 健康科普朱医生
内容长度: 43707 字符
图片数量: 101 张
```

## 配置选项

### 请求头设置

脚本默认使用以下请求头来模拟浏览器访问：

```python
self.session.headers.update({
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36...',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
    # ... 其他头部信息
})
```

### 超时设置

默认请求超时时间为30秒，可以在 `get_page_content()` 方法中修改：

```python
response = self.session.get(url, timeout=30)  # 修改这里的数值
```

## 注意事项

1. **遵守robots.txt**：请确保你的爬取行为符合目标网站的robots.txt规定
2. **请求频率**：避免过于频繁的请求，建议在请求间添加适当的延时
3. **法律合规**：仅用于学习和研究目的，请遵守相关法律法规
4. **网站结构**：不同网站的HTML结构不同，可能需要调整解析逻辑

## 故障排除

### 常见问题

1. **SSL证书错误**：
   ```python
   # 在requests.get()中添加verify=False参数（不推荐用于生产环境）
   response = self.session.get(url, timeout=30, verify=False)
   ```

2. **编码问题**：
   ```python
   # 手动设置编码
   response.encoding = 'utf-8'
   ```

3. **反爬虫机制**：
   - 增加请求间隔时间
   - 使用代理IP
   - 更换User-Agent

## 扩展功能

你可以根据需要扩展以下功能：

- 支持批量URL爬取
- 添加数据库存储
- 实现增量更新
- 添加图片下载功能
- 支持更多输出格式（CSV、XML等）

## 许可证

本项目仅供学习和研究使用。使用时请遵守目标网站的使用条款和相关法律法规。
