#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成200条婴幼儿精细运动发展指导数据
专门配置用于生成约200条高质量数据样本
"""

import json
import os
import time
import dashscope
from dashscope import Generation

class Generate200SamplesBuilder:
    def __init__(self, api_key: str = None):
        """初始化200条数据生成器"""
        if api_key:
            dashscope.api_key = api_key
        elif os.getenv('QWEN_API_KEY'):
            dashscope.api_key = os.getenv('QWEN_API_KEY')
        else:
            raise ValueError("请设置Qwen API密钥")
        
        self.target_samples = 200
        self.generated_data = {
            'metadata': {
                'name': '婴幼儿精细运动发展指导数据集 - 200条版本',
                'version': '2.0',
                'target_samples': 200,
                'description': '使用Qwen大模型生成的200条高质量婴幼儿精细运动发展指导数据',
                'created_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'enhancement_model': 'qwen-max'
            },
            'data': []
        }
    
    def call_qwen_api(self, prompt: str, max_tokens: int = 2000) -> str:
        """调用Qwen API"""
        try:
            messages = [
                {
                    'role': 'system',
                    'content': '你是一位资深的儿童发育专家和育儿指导师，拥有丰富的0-3岁婴幼儿精细运动发展临床经验。请基于专业知识生成高质量、实用的指导内容。'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ]
            
            response = Generation.call(
                model='qwen-max',
                messages=messages,
                result_format='message',
                max_tokens=max_tokens,
                temperature=0.7
            )
            
            if response.status_code == 200:
                return response.output.choices[0].message.content
            else:
                print(f"API调用失败: {response.message}")
                return ""
                
        except Exception as e:
            print(f"API调用异常: {e}")
            return ""
    
    def generate_monthly_qa_batch(self, month: int, batch_size: int = 8) -> list:
        """为特定月龄生成一批问答"""
        prompt = f"""
        为{month}个月宝宝的精细运动发育生成{batch_size}个不同类型的专业问答对：
        
        请涵盖以下类型：
        1. 发育标准询问（这个月龄应该达到什么水平）
        2. 测评方法（如何评估宝宝的发育情况）
        3. 家长指导（家长如何在家观察和引导）
        4. 训练方法（具体的训练活动和游戏）
        5. 异常识别（如何识别发育异常的信号）
        6. 问题解决（遇到发育问题如何处理）
        7. 安全注意（训练中的安全事项）
        8. 个体差异（如何理解宝宝的个体发育差异）
        
        每个问答要：
        - 专业准确，基于儿童发育学理论
        - 实用具体，对家长有指导价值
        - 语言通俗，易于理解
        - 内容完整，包含具体操作方法
        
        格式：Q: 问题\nA: 答案\n---
        """
        
        qa_pairs = []
        content = self.call_qwen_api(prompt, max_tokens=3000)
        
        if content:
            qa_blocks = content.split('---')
            for i, block in enumerate(qa_blocks):
                if 'Q:' in block and 'A:' in block:
                    lines = block.strip().split('\n')
                    question = ""
                    answer = ""
                    
                    for line in lines:
                        if line.startswith('Q:'):
                            question = line[2:].strip()
                        elif line.startswith('A:'):
                            answer = line[2:].strip()
                        elif answer and line.strip():
                            answer += " " + line.strip()
                    
                    if question and answer:
                        categories = ['milestone_inquiry', 'evaluation_method', 'parent_guidance', 
                                    'training_method', 'abnormal_detection', 'problem_solving',
                                    'safety_notes', 'individual_differences']
                        
                        qa_pairs.append({
                            'question': question,
                            'answer': answer,
                            'category': categories[i % len(categories)],
                            'age_group': f"{month}个月",
                            'difficulty': 'intermediate',
                            'generated_by': 'qwen_api_batch'
                        })
        
        return qa_pairs
    
    def generate_age_range_qa_batch(self, age_range: str, batch_size: int = 6) -> list:
        """为年龄段生成训练指导问答"""
        prompt = f"""
        为{age_range}宝宝的精细运动发育训练生成{batch_size}个专业问答对：
        
        请涵盖：
        1. 训练目标和重点
        2. 日常训练活动
        3. 游戏化训练方法
        4. 训练进度评估
        5. 常见训练问题
        6. 家庭环境布置
        
        每个问答要包含具体的操作指导和实用建议。
        
        格式：Q: 问题\nA: 答案\n---
        """
        
        qa_pairs = []
        content = self.call_qwen_api(prompt, max_tokens=2500)
        
        if content:
            qa_blocks = content.split('---')
            for i, block in enumerate(qa_blocks):
                if 'Q:' in block and 'A:' in block:
                    lines = block.strip().split('\n')
                    question = ""
                    answer = ""
                    
                    for line in lines:
                        if line.startswith('Q:'):
                            question = line[2:].strip()
                        elif line.startswith('A:'):
                            answer = line[2:].strip()
                        elif answer and line.strip():
                            answer += " " + line.strip()
                    
                    if question and answer:
                        qa_pairs.append({
                            'question': question,
                            'answer': answer,
                            'category': 'training_guidance',
                            'age_group': age_range,
                            'difficulty': 'intermediate',
                            'generated_by': 'qwen_api_batch'
                        })
        
        return qa_pairs
    
    def generate_expert_qa_batch(self, topic: str, batch_size: int = 4) -> list:
        """生成专家级问答"""
        prompt = f"""
        围绕"{topic}"这个专业主题，生成{batch_size}个深度的专家级问答对：
        
        要求：
        1. 问题要有专业深度，体现专业水准
        2. 答案要科学严谨，引用相关研究和理论
        3. 内容要对专业人士和家长都有价值
        4. 包含实际应用建议和案例
        
        格式：Q: 问题\nA: 答案\n---
        """
        
        qa_pairs = []
        content = self.call_qwen_api(prompt, max_tokens=2500)
        
        if content:
            qa_blocks = content.split('---')
            for block in qa_blocks:
                if 'Q:' in block and 'A:' in block:
                    lines = block.strip().split('\n')
                    question = ""
                    answer = ""
                    
                    for line in lines:
                        if line.startswith('Q:'):
                            question = line[2:].strip()
                        elif line.startswith('A:'):
                            answer = line[2:].strip()
                        elif answer and line.strip():
                            answer += " " + line.strip()
                    
                    if question and answer:
                        qa_pairs.append({
                            'question': question,
                            'answer': answer,
                            'category': 'expert_knowledge',
                            'topic': topic,
                            'age_group': 'professional',
                            'difficulty': 'expert',
                            'generated_by': 'qwen_api_batch'
                        })
        
        return qa_pairs
    
    def generate_200_samples(self):
        """生成200条数据样本"""
        print("🚀 开始生成200条婴幼儿精细运动发展指导数据...")
        
        all_qa_pairs = []
        
        # 1. 为关键月龄生成问答（每个月龄8个问答）
        print("📅 为关键月龄生成问答...")
        key_months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 16, 18, 21, 24]  # 17个月龄
        
        for month in key_months:
            print(f"   生成{month}个月宝宝问答...")
            monthly_qa = self.generate_monthly_qa_batch(month, batch_size=6)  # 每个月龄6个问答
            all_qa_pairs.extend(monthly_qa)
            time.sleep(1)  # API调用间隔
        
        print(f"   ✅ 月龄问答生成完成: {len(all_qa_pairs)} 个")
        
        # 2. 为年龄段生成训练指导问答
        print("📚 生成年龄段训练指导...")
        age_ranges = ['0-3个月', '3-6个月', '6-9个月', '9-12个月', '12-18个月', '18-24个月', '24-36个月']
        
        for age_range in age_ranges:
            print(f"   生成{age_range}训练指导...")
            range_qa = self.generate_age_range_qa_batch(age_range, batch_size=4)  # 每个年龄段4个问答
            all_qa_pairs.extend(range_qa)
            time.sleep(1)
        
        print(f"   ✅ 年龄段问答生成完成: 当前总数 {len(all_qa_pairs)} 个")
        
        # 3. 生成专家级问答
        print("🧠 生成专家级问答...")
        expert_topics = [
            '精细运动发育迟缓的早期识别',
            '精细运动与认知发育的关系',
            '早产儿的精细运动发育特点',
            '精细运动训练的科学原理',
            '家庭环境对精细运动发育的影响',
            '精细运动发育的个体差异',
            '数字化工具在精细运动评估中的应用'
        ]
        
        for topic in expert_topics:
            print(f"   生成专题: {topic[:15]}...")
            expert_qa = self.generate_expert_qa_batch(topic, batch_size=3)  # 每个主题3个问答
            all_qa_pairs.extend(expert_qa)
            time.sleep(1)
        
        print(f"   ✅ 专家问答生成完成: 当前总数 {len(all_qa_pairs)} 个")
        
        # 4. 如果还没达到200条，补充通用问答
        if len(all_qa_pairs) < self.target_samples:
            remaining = self.target_samples - len(all_qa_pairs)
            print(f"📝 补充通用问答: 还需 {remaining} 个")
            
            general_prompt = f"""
            生成{remaining}个婴幼儿精细运动发育的通用问答对，涵盖：
            
            1. 精细运动发育的基本概念
            2. 不同发育阶段的特点
            3. 常见发育问题及解决方案
            4. 家长常见疑问解答
            5. 专业评估和干预建议
            
            每个问答要实用、专业，对家长和专业人士都有价值。
            
            格式：Q: 问题\nA: 答案\n---
            """
            
            content = self.call_qwen_api(general_prompt, max_tokens=4000)
            if content:
                qa_blocks = content.split('---')
                for block in qa_blocks:
                    if 'Q:' in block and 'A:' in block and len(all_qa_pairs) < self.target_samples:
                        lines = block.strip().split('\n')
                        question = ""
                        answer = ""
                        
                        for line in lines:
                            if line.startswith('Q:'):
                                question = line[2:].strip()
                            elif line.startswith('A:'):
                                answer = line[2:].strip()
                            elif answer and line.strip():
                                answer += " " + line.strip()
                        
                        if question and answer:
                            all_qa_pairs.append({
                                'question': question,
                                'answer': answer,
                                'category': 'general_guidance',
                                'age_group': 'general',
                                'difficulty': 'intermediate',
                                'generated_by': 'qwen_api_supplement'
                            })
        
        # 5. 整合数据
        self.generated_data['metadata']['actual_samples'] = len(all_qa_pairs)
        self.generated_data['data'] = all_qa_pairs
        
        print(f"✅ 数据生成完成！实际生成 {len(all_qa_pairs)} 条数据")
        
        return self.generated_data
    
    def save_dataset(self, filename: str = 'fine_motor_200_samples.json'):
        """保存200条数据集"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.generated_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 200条数据集已保存到: {filename}")
        
        # 生成统计报告
        self.generate_report()
    
    def generate_report(self):
        """生成数据集报告"""
        data = self.generated_data['data']
        
        # 统计分析
        category_stats = {}
        age_stats = {}
        difficulty_stats = {}
        
        for item in data:
            category = item.get('category', 'unknown')
            category_stats[category] = category_stats.get(category, 0) + 1
            
            age_group = item.get('age_group', 'unknown')
            age_stats[age_group] = age_stats.get(age_group, 0) + 1
            
            difficulty = item.get('difficulty', 'unknown')
            difficulty_stats[difficulty] = difficulty_stats.get(difficulty, 0) + 1
        
        report = f"""# 200条婴幼儿精细运动发展指导数据集报告

## 📊 基本信息
- **目标样本数**: {self.generated_data['metadata']['target_samples']}
- **实际样本数**: {self.generated_data['metadata']['actual_samples']}
- **生成时间**: {self.generated_data['metadata']['created_time']}
- **生成模型**: {self.generated_data['metadata']['enhancement_model']}

## 📈 数据分布

### 类别分布
"""
        
        for category, count in sorted(category_stats.items()):
            percentage = (count / len(data)) * 100
            report += f"- **{category}**: {count} 个 ({percentage:.1f}%)\n"
        
        report += "\n### 年龄组分布\n"
        for age_group, count in sorted(age_stats.items()):
            percentage = (count / len(data)) * 100
            report += f"- **{age_group}**: {count} 个 ({percentage:.1f}%)\n"
        
        report += "\n### 难度分布\n"
        for difficulty, count in sorted(difficulty_stats.items()):
            percentage = (count / len(data)) * 100
            report += f"- **{difficulty}**: {count} 个 ({percentage:.1f}%)\n"
        
        report += f"""
## 🎯 数据特点
- **全面覆盖**: 涵盖0-36个月各关键发育阶段
- **专业深度**: 包含专家级深度内容
- **实用导向**: 注重实际应用和操作指导
- **AI增强**: 使用Qwen大模型智能生成

## 🚀 应用建议
1. **LLM训练**: 可直接用于婴幼儿发育指导模型训练
2. **知识库构建**: 适合构建专业问答系统
3. **教育培训**: 可用于家长教育和专业培训
4. **临床辅助**: 支持儿科医生和早教师使用
"""
        
        with open('fine_motor_200_samples_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("📊 数据集报告已生成: fine_motor_200_samples_report.md")

def main():
    """主函数"""
    print("🎯 生成200条婴幼儿精细运动发展指导数据")
    print("="*50)
    
    # 检查API密钥
    api_key = os.getenv('QWEN_API_KEY') or 'sk-5eba46fbcff649d5bf28313bc865de10'
    if not api_key:
        print("❌ 请设置QWEN_API_KEY环境变量")
        print("💡 使用方法: export QWEN_API_KEY='your_api_key'")
        return
    
    try:
        builder = Generate200SamplesBuilder(api_key)
        
        # 生成200条数据
        dataset = builder.generate_200_samples()
        
        # 保存数据集
        builder.save_dataset()
        
        print("\n🎉 200条数据生成完成！")
        print("📁 生成的文件:")
        print("   - fine_motor_200_samples.json (200条数据集)")
        print("   - fine_motor_200_samples_report.md (统计报告)")
        
    except Exception as e:
        print(f"❌ 生成失败: {e}")

if __name__ == "__main__":
    main()
