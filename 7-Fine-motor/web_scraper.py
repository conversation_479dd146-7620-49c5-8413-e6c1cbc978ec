#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
网页内容爬虫脚本
用于爬取健康界网站的文章内容
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import re
from urllib.parse import urljoin, urlparse
import os
from pathlib import Path

class WebScraper:
    def __init__(self):
        self.session = requests.Session()
        # 设置请求头，模拟浏览器访问
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def get_page_content(self, url):
        """获取网页内容"""
        try:
            response = self.session.get(url, timeout=30)
            response.raise_for_status()
            response.encoding = 'utf-8'
            return response.text
        except requests.RequestException as e:
            print(f"请求失败: {e}")
            return None
    
    def parse_article(self, html_content, url):
        """解析文章内容"""
        soup = BeautifulSoup(html_content, 'html.parser')
        
        article_data = {
            'url': url,
            'title': '',
            'author': '',
            'publish_date': '',
            'content': '',
            'images': [],
            'scraped_time': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 提取标题
        title_elem = soup.find('h1') or soup.find('title')
        if title_elem:
            article_data['title'] = title_elem.get_text().strip()
        
        # 提取作者信息
        author_elem = soup.find(class_='author') or soup.find(text=re.compile('健康科普朱医生'))
        if author_elem:
            if hasattr(author_elem, 'get_text'):
                article_data['author'] = author_elem.get_text().strip()
            else:
                article_data['author'] = str(author_elem).strip()
        
        # 提取发布日期
        date_elem = soup.find(class_='date') or soup.find(text=re.compile(r'\d{4}'))
        if date_elem:
            if hasattr(date_elem, 'get_text'):
                article_data['publish_date'] = date_elem.get_text().strip()
            else:
                article_data['publish_date'] = str(date_elem).strip()
        
        # 提取正文内容
        content_parts = []
        
        # 查找文章主体内容
        article_body = soup.find('div', class_='article-content') or soup.find('div', class_='content') or soup.body
        
        if article_body:
            # 提取所有段落文本
            for element in article_body.find_all(['p', 'div', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']):
                text = element.get_text().strip()
                if text and len(text) > 10:  # 过滤掉太短的文本
                    content_parts.append(text)
            
            # 提取图片链接
            for img in article_body.find_all('img'):
                img_src = img.get('src')
                if img_src:
                    # 转换为绝对URL
                    img_url = urljoin(url, img_src)
                    article_data['images'].append({
                        'src': img_url,
                        'alt': img.get('alt', ''),
                        'title': img.get('title', '')
                    })
        
        article_data['content'] = '\n\n'.join(content_parts)
        
        return article_data
    
    def save_to_file(self, data, filename):
        """保存数据到文件"""
        # 保存为JSON格式
        json_filename = f"{filename}.json"
        with open(json_filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        # 保存为纯文本格式
        txt_filename = f"{filename}.txt"
        with open(txt_filename, 'w', encoding='utf-8') as f:
            f.write(f"标题: {data['title']}\n")
            f.write(f"作者: {data['author']}\n")
            f.write(f"发布日期: {data['publish_date']}\n")
            f.write(f"原文链接: {data['url']}\n")
            f.write(f"爬取时间: {data['scraped_time']}\n")
            f.write("="*50 + "\n\n")
            f.write(data['content'])
            
            if data['images']:
                f.write("\n\n" + "="*50 + "\n")
                f.write("图片链接:\n")
                for i, img in enumerate(data['images'], 1):
                    f.write(f"{i}. {img['src']}\n")
                    if img['alt']:
                        f.write(f"   描述: {img['alt']}\n")
        
        print(f"数据已保存到: {json_filename} 和 {txt_filename}")

    def download_core_images(self, images, output_dir="images"):
        """下载核心图片（只下载文章相关的图片）"""
        # 创建图片保存目录
        img_dir = Path(output_dir)
        img_dir.mkdir(exist_ok=True)

        downloaded_images = []
        core_image_count = 0

        for i, img_info in enumerate(images):
            img_url = img_info['src']

            # 只下载文章核心图片（wximg目录下的图片）
            if 'wximg' in img_url or 'upload/20210505' in img_url:
                try:
                    print(f"正在下载核心图片 {core_image_count + 1}: {img_url}")

                    # 获取图片
                    response = self.session.get(img_url, timeout=30)
                    response.raise_for_status()

                    # 生成文件名
                    img_name = img_url.split('/')[-1]
                    if not img_name or '.' not in img_name:
                        # 如果没有扩展名，尝试从Content-Type获取
                        content_type = response.headers.get('content-type', '')
                        if 'jpeg' in content_type or 'jpg' in content_type:
                            img_name = f"image_{core_image_count + 1}.jpg"
                        elif 'png' in content_type:
                            img_name = f"image_{core_image_count + 1}.png"
                        elif 'gif' in content_type:
                            img_name = f"image_{core_image_count + 1}.gif"
                        else:
                            img_name = f"image_{core_image_count + 1}.jpg"

                    # 保存图片
                    img_path = img_dir / img_name
                    with open(img_path, 'wb') as f:
                        f.write(response.content)

                    downloaded_images.append({
                        'original_url': img_url,
                        'local_path': str(img_path),
                        'filename': img_name,
                        'alt': img_info.get('alt', ''),
                        'title': img_info.get('title', '')
                    })

                    core_image_count += 1
                    print(f"✅ 下载成功: {img_name}")

                    # 添加延时，避免请求过于频繁
                    time.sleep(0.5)

                except Exception as e:
                    print(f"❌ 下载失败 {img_url}: {e}")
                    continue

        print(f"\n🎯 核心图片下载完成！共下载 {core_image_count} 张图片到 {output_dir} 目录")
        return downloaded_images

    def scrape_article(self, url, output_filename=None, download_images=False):
        """爬取单篇文章"""
        print(f"开始爬取: {url}")
        
        # 获取网页内容
        html_content = self.get_page_content(url)
        if not html_content:
            print("获取网页内容失败")
            return None
        
        # 解析文章
        article_data = self.parse_article(html_content, url)
        
        # 生成文件名
        if not output_filename:
            # 从URL或标题生成文件名
            if article_data['title']:
                # 清理标题中的特殊字符
                clean_title = re.sub(r'[^\w\s-]', '', article_data['title'])
                clean_title = re.sub(r'[-\s]+', '_', clean_title)
                output_filename = clean_title[:50]  # 限制文件名长度
            else:
                output_filename = f"article_{int(time.time())}"
        
        # 保存数据
        self.save_to_file(article_data, output_filename)

        # 下载核心图片（如果需要）
        if download_images and article_data['images']:
            print("\n开始下载核心图片...")
            downloaded_images = self.download_core_images(article_data['images'], f"{output_filename}_images")
            article_data['downloaded_images'] = downloaded_images

        return article_data

def main():
    """主函数"""
    # 目标URL
    url = "https://www.cn-healthcare.com/articlewm/20210505/content-1216802.html"
    
    # 创建爬虫实例
    scraper = WebScraper()
    
    # 爬取文章并下载核心图片
    result = scraper.scrape_article(url, "baby_fine_motor_development", download_images=True)
    
    if result:
        print("\n爬取成功!")
        print(f"标题: {result['title']}")
        print(f"作者: {result['author']}")
        print(f"内容长度: {len(result['content'])} 字符")
        print(f"图片数量: {len(result['images'])} 张")
    else:
        print("爬取失败!")

if __name__ == "__main__":
    main()
