#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
婴幼儿精细运动发展指导数据集使用示例
Infant Fine Motor Development Guidance Dataset Usage Examples
"""

import json
import os
from typing import List, Dict, Any
from collections import Counter
import random

class FineMotoDatasetLoader:
    """数据集加载和处理类"""
    
    def __init__(self, dataset_dir: str = "."):
        self.dataset_dir = dataset_dir
        self.datasets = {}
        self.load_all_datasets()
    
    def load_all_datasets(self):
        """加载所有数据集"""
        dataset_files = [
            "behavior_analysis_300_reliable.json",
            "fine_motor_200_samples.json", 
            "parent_education_dataset.json",
            "professional_training_dataset.json",
            "behavior_analysis_qa_dataset.json",
            "early_intervention_dataset.json",
            "milestone_tracking_dataset.json",
            "behavior_analysis_examples.json"
        ]
        
        for filename in dataset_files:
            filepath = os.path.join(self.dataset_dir, filename)
            if os.path.exists(filepath):
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        dataset_name = filename.replace('.json', '')
                        self.datasets[dataset_name] = data
                        print(f"✅ 已加载: {filename}")
                except Exception as e:
                    print(f"❌ 加载失败 {filename}: {e}")
            else:
                print(f"⚠️  文件不存在: {filename}")
    
    def get_dataset_info(self):
        """获取数据集基本信息"""
        info = {}
        total_samples = 0
        
        for name, data in self.datasets.items():
            if 'data' in data:
                sample_count = len(data['data'])
                metadata = data.get('metadata', {})
                info[name] = {
                    'samples': sample_count,
                    'name': metadata.get('name', name),
                    'description': metadata.get('description', 'No description')
                }
                total_samples += sample_count
            else:
                info[name] = {
                    'samples': 0,
                    'name': name,
                    'description': 'Invalid format'
                }
        
        info['total_samples'] = total_samples
        return info
    
    def get_samples_by_age(self, age_group: str) -> List[Dict]:
        """根据年龄组获取样本"""
        samples = []
        for dataset_name, data in self.datasets.items():
            if 'data' in data:
                for sample in data['data']:
                    if sample.get('age_group') == age_group:
                        sample['source_dataset'] = dataset_name
                        samples.append(sample)
        return samples
    
    def get_samples_by_category(self, category: str) -> List[Dict]:
        """根据类别获取样本"""
        samples = []
        for dataset_name, data in self.datasets.items():
            if 'data' in data:
                for sample in data['data']:
                    if sample.get('category') == category:
                        sample['source_dataset'] = dataset_name
                        samples.append(sample)
        return samples
    
    def search_samples(self, keyword: str) -> List[Dict]:
        """搜索包含关键词的样本"""
        samples = []
        for dataset_name, data in self.datasets.items():
            if 'data' in data:
                for sample in data['data']:
                    question = sample.get('question', '')
                    answer = sample.get('answer', '')
                    if keyword in question or keyword in answer:
                        sample['source_dataset'] = dataset_name
                        samples.append(sample)
        return samples
    
    def get_random_samples(self, count: int = 5) -> List[Dict]:
        """获取随机样本"""
        all_samples = []
        for dataset_name, data in self.datasets.items():
            if 'data' in data:
                for sample in data['data']:
                    sample['source_dataset'] = dataset_name
                    all_samples.append(sample)
        
        return random.sample(all_samples, min(count, len(all_samples)))
    
    def analyze_dataset_distribution(self):
        """分析数据集分布"""
        age_groups = Counter()
        categories = Counter()
        difficulties = Counter()
        
        for dataset_name, data in self.datasets.items():
            if 'data' in data:
                for sample in data['data']:
                    age_groups[sample.get('age_group', 'unknown')] += 1
                    categories[sample.get('category', 'unknown')] += 1
                    difficulties[sample.get('difficulty', 'unknown')] += 1
        
        return {
            'age_groups': dict(age_groups),
            'categories': dict(categories),
            'difficulties': dict(difficulties)
        }

def main():
    """主函数 - 演示数据集使用方法"""
    print("🚀 婴幼儿精细运动发展指导数据集使用示例")
    print("=" * 60)
    
    # 初始化数据集加载器
    loader = FineMotoDatasetLoader()
    
    # 1. 显示数据集基本信息
    print("\n📊 数据集基本信息:")
    info = loader.get_dataset_info()
    for name, details in info.items():
        if name != 'total_samples':
            print(f"  • {details['name']}: {details['samples']} 条样本")
    print(f"\n📈 总计样本数: {info['total_samples']} 条")
    
    # 2. 分析数据分布
    print("\n📈 数据分布分析:")
    distribution = loader.analyze_dataset_distribution()
    
    print("  年龄组分布:")
    for age, count in sorted(distribution['age_groups'].items()):
        print(f"    - {age}: {count} 条")
    
    print("  类别分布:")
    for category, count in sorted(distribution['categories'].items()):
        print(f"    - {category}: {count} 条")
    
    # 3. 按年龄组查询示例
    print("\n🔍 按年龄组查询示例 (6个月):")
    age_samples = loader.get_samples_by_age("6个月")
    print(f"  找到 {len(age_samples)} 条相关样本")
    if age_samples:
        sample = age_samples[0]
        print(f"  示例问题: {sample['question'][:50]}...")
        print(f"  来源数据集: {sample['source_dataset']}")
    
    # 4. 按类别查询示例
    print("\n🔍 按类别查询示例 (behavior_analysis):")
    category_samples = loader.get_samples_by_category("behavior_analysis")
    print(f"  找到 {len(category_samples)} 条相关样本")
    if category_samples:
        sample = category_samples[0]
        print(f"  示例问题: {sample['question'][:50]}...")
    
    # 5. 关键词搜索示例
    print("\n🔍 关键词搜索示例 (抓握):")
    search_samples = loader.search_samples("抓握")
    print(f"  找到 {len(search_samples)} 条相关样本")
    if search_samples:
        sample = search_samples[0]
        print(f"  示例问题: {sample['question'][:50]}...")
    
    # 6. 随机样本展示
    print("\n🎲 随机样本展示:")
    random_samples = loader.get_random_samples(3)
    for i, sample in enumerate(random_samples, 1):
        print(f"  样本 {i}:")
        print(f"    问题: {sample['question'][:60]}...")
        print(f"    年龄组: {sample.get('age_group', 'N/A')}")
        print(f"    类别: {sample.get('category', 'N/A')}")
        print(f"    来源: {sample['source_dataset']}")
        print()

def export_combined_dataset():
    """导出合并的数据集"""
    print("\n💾 导出合并数据集...")
    
    loader = FineMotoDatasetLoader()
    combined_data = {
        'metadata': {
            'name': '婴幼儿精细运动发展指导综合数据集',
            'version': '1.0',
            'description': '合并所有子数据集的综合版本',
            'total_samples': 0,
            'source_datasets': list(loader.datasets.keys())
        },
        'data': []
    }
    
    # 合并所有数据
    for dataset_name, data in loader.datasets.items():
        if 'data' in data:
            for sample in data['data']:
                sample['source_dataset'] = dataset_name
                combined_data['data'].append(sample)
    
    combined_data['metadata']['total_samples'] = len(combined_data['data'])
    
    # 保存合并数据集
    output_file = 'combined_fine_motor_dataset.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(combined_data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 合并数据集已保存: {output_file}")
    print(f"📊 总样本数: {combined_data['metadata']['total_samples']}")

def create_training_splits():
    """创建训练/验证/测试数据分割"""
    print("\n🔄 创建数据分割...")
    
    loader = FineMotoDatasetLoader()
    
    # 获取所有样本
    all_samples = []
    for dataset_name, data in loader.datasets.items():
        if 'data' in data:
            for sample in data['data']:
                sample['source_dataset'] = dataset_name
                all_samples.append(sample)
    
    # 随机打乱
    random.shuffle(all_samples)
    
    # 分割比例: 70% 训练, 15% 验证, 15% 测试
    total = len(all_samples)
    train_size = int(total * 0.7)
    val_size = int(total * 0.15)
    
    train_data = all_samples[:train_size]
    val_data = all_samples[train_size:train_size + val_size]
    test_data = all_samples[train_size + val_size:]
    
    # 保存分割数据
    splits = {
        'train': train_data,
        'validation': val_data,
        'test': test_data
    }
    
    for split_name, split_data in splits.items():
        output_file = f'{split_name}_split.json'
        split_dataset = {
            'metadata': {
                'split': split_name,
                'samples': len(split_data),
                'percentage': len(split_data) / total * 100
            },
            'data': split_data
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(split_dataset, f, ensure_ascii=False, indent=2)
        
        print(f"✅ {split_name} 数据集: {len(split_data)} 条样本 ({len(split_data)/total*100:.1f}%)")

if __name__ == "__main__":
    main()
    export_combined_dataset()
    create_training_splits()
    
    print("\n🎉 数据集使用示例完成！")
    print("\n📝 生成的文件:")
    print("  • combined_fine_motor_dataset.json - 合并数据集")
    print("  • train_split.json - 训练集")
    print("  • validation_split.json - 验证集")
    print("  • test_split.json - 测试集")
