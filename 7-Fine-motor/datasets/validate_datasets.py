#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据集验证脚本
Dataset Validation Script
"""

import json
import os
from typing import Dict, List, Any

def validate_dataset_structure(data: Dict, filename: str) -> List[str]:
    """验证数据集结构"""
    errors = []
    
    # 检查基本结构
    if 'data' not in data:
        errors.append(f"{filename}: 缺少 'data' 字段")
        return errors
    
    if not isinstance(data['data'], list):
        errors.append(f"{filename}: 'data' 字段应该是列表")
        return errors
    
    # 检查样本结构
    for i, sample in enumerate(data['data']):
        if not isinstance(sample, dict):
            errors.append(f"{filename}: 样本 {i} 不是字典格式")
            continue
            
        # 检查必需字段
        required_fields = ['question', 'answer']
        for field in required_fields:
            if field not in sample:
                errors.append(f"{filename}: 样本 {i} 缺少 '{field}' 字段")
        
        # 检查字段类型
        if 'question' in sample and not isinstance(sample['question'], str):
            errors.append(f"{filename}: 样本 {i} 的 'question' 应该是字符串")
        
        if 'answer' in sample and not isinstance(sample['answer'], str):
            errors.append(f"{filename}: 样本 {i} 的 'answer' 应该是字符串")
        
        # 检查内容长度
        if 'question' in sample and len(sample['question']) < 10:
            errors.append(f"{filename}: 样本 {i} 的问题过短")
        
        if 'answer' in sample and len(sample['answer']) < 20:
            errors.append(f"{filename}: 样本 {i} 的答案过短")
    
    return errors

def validate_behavior_analysis_format(data: Dict, filename: str) -> List[str]:
    """验证行为分析数据集的特殊格式"""
    errors = []
    
    if 'behavior_analysis' not in filename:
        return errors
    
    for i, sample in enumerate(data.get('data', [])):
        answer = sample.get('answer', '')
        
        # 检查结构化答案格式
        required_sections = ['行为分析', '发育评估', '指导建议', '观察要点']
        for section in required_sections:
            if f"**{section}**" not in answer:
                errors.append(f"{filename}: 样本 {i} 缺少 '{section}' 部分")
    
    return errors

def check_data_quality(data: Dict, filename: str) -> Dict[str, Any]:
    """检查数据质量指标"""
    if 'data' not in data:
        return {}
    
    samples = data['data']
    
    # 基本统计
    total_samples = len(samples)
    
    # 问题长度统计
    question_lengths = [len(sample.get('question', '')) for sample in samples]
    answer_lengths = [len(sample.get('answer', '')) for sample in samples]
    
    # 年龄组分布
    age_groups = {}
    categories = {}
    
    for sample in samples:
        age_group = sample.get('age_group', 'unknown')
        category = sample.get('category', 'unknown')
        
        age_groups[age_group] = age_groups.get(age_group, 0) + 1
        categories[category] = categories.get(category, 0) + 1
    
    return {
        'total_samples': total_samples,
        'avg_question_length': sum(question_lengths) / len(question_lengths) if question_lengths else 0,
        'avg_answer_length': sum(answer_lengths) / len(answer_lengths) if answer_lengths else 0,
        'min_question_length': min(question_lengths) if question_lengths else 0,
        'max_question_length': max(question_lengths) if question_lengths else 0,
        'min_answer_length': min(answer_lengths) if answer_lengths else 0,
        'max_answer_length': max(answer_lengths) if answer_lengths else 0,
        'age_groups_count': len(age_groups),
        'categories_count': len(categories),
        'top_age_groups': sorted(age_groups.items(), key=lambda x: x[1], reverse=True)[:5],
        'top_categories': sorted(categories.items(), key=lambda x: x[1], reverse=True)[:5]
    }

def main():
    """主验证函数"""
    print("🔍 开始验证数据集...")
    print("=" * 60)
    
    # 获取所有JSON文件
    json_files = [f for f in os.listdir('.') if f.endswith('.json')]
    
    total_errors = 0
    total_samples = 0
    
    for filename in sorted(json_files):
        print(f"\n📁 验证文件: {filename}")
        
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 结构验证
            errors = validate_dataset_structure(data, filename)
            
            # 特殊格式验证
            behavior_errors = validate_behavior_analysis_format(data, filename)
            errors.extend(behavior_errors)
            
            # 质量检查
            quality = check_data_quality(data, filename)
            
            if errors:
                print(f"  ❌ 发现 {len(errors)} 个错误:")
                for error in errors[:5]:  # 只显示前5个错误
                    print(f"    - {error}")
                if len(errors) > 5:
                    print(f"    ... 还有 {len(errors) - 5} 个错误")
                total_errors += len(errors)
            else:
                print("  ✅ 结构验证通过")
            
            # 显示质量指标
            if quality:
                print(f"  📊 样本数: {quality['total_samples']}")
                print(f"  📏 平均问题长度: {quality['avg_question_length']:.1f} 字符")
                print(f"  📏 平均答案长度: {quality['avg_answer_length']:.1f} 字符")
                print(f"  🏷️  年龄组数: {quality['age_groups_count']}")
                print(f"  🏷️  类别数: {quality['categories_count']}")
                
                total_samples += quality['total_samples']
        
        except json.JSONDecodeError as e:
            print(f"  ❌ JSON格式错误: {e}")
            total_errors += 1
        except Exception as e:
            print(f"  ❌ 读取错误: {e}")
            total_errors += 1
    
    # 总结
    print("\n" + "=" * 60)
    print("📋 验证总结:")
    print(f"  📁 验证文件数: {len(json_files)}")
    print(f"  📊 总样本数: {total_samples}")
    print(f"  ❌ 总错误数: {total_errors}")
    
    if total_errors == 0:
        print("  🎉 所有数据集验证通过！")
    else:
        print(f"  ⚠️  发现 {total_errors} 个问题需要修复")
    
    # 推荐使用的数据集
    print("\n🌟 推荐使用的高质量数据集:")
    recommended = [
        "behavior_analysis_300_reliable.json",
        "fine_motor_200_samples.json", 
        "combined_fine_motor_dataset.json"
    ]
    
    for rec in recommended:
        if rec in json_files:
            print(f"  ✅ {rec}")
        else:
            print(f"  ❌ {rec} (文件不存在)")

if __name__ == "__main__":
    main()
