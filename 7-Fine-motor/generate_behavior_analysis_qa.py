#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成基于实际婴幼儿行为观察的精细运动分析问答
包含具体行为描述、专业分析和指导建议
"""

import json
import os
import time
import dashscope
from dashscope import Generation

class BehaviorAnalysisQAGenerator:
    def __init__(self, api_key: str = None):
        """初始化行为分析问答生成器"""
        if api_key:
            dashscope.api_key = api_key
        elif os.getenv('QWEN_API_KEY'):
            dashscope.api_key = os.getenv('QWEN_API_KEY')
        else:
            dashscope.api_key = 'sk-5eba46fbcff649d5bf28313bc865de10'
        
        self.behavior_qa_data = {
            'metadata': {
                'name': '婴幼儿精细运动行为分析问答数据集',
                'version': '1.0',
                'description': '基于实际婴幼儿行为观察的精细运动分析和指导问答',
                'created_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'enhancement_model': 'qwen-max',
                'focus': '实际行为观察+专业分析+指导建议'
            },
            'data': []
        }
    
    def call_qwen_api(self, prompt: str, max_tokens: int = 2500) -> str:
        """调用Qwen API"""
        try:
            messages = [
                {
                    'role': 'system',
                    'content': '你是一位资深的儿童发育专家，擅长观察和分析婴幼儿的精细运动行为。请基于具体的行为描述，提供专业的发育分析和实用的指导建议。'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ]
            
            response = Generation.call(
                model='qwen-max',
                messages=messages,
                result_format='message',
                max_tokens=max_tokens,
                temperature=0.7
            )
            
            if response.status_code == 200:
                return response.output.choices[0].message.content
            else:
                print(f"API调用失败: {response.message}")
                return ""
                
        except Exception as e:
            print(f"API调用异常: {e}")
            return ""
    
    def generate_behavior_scenarios(self, age_months: int, count: int = 5) -> list:
        """为特定月龄生成行为观察场景"""
        prompt = f"""
        请为{age_months}个月大的宝宝创建{count}个真实的精细运动行为观察场景。
        
        每个场景应该包含：
        1. 具体的行为描述（家长观察到的实际情况）
        2. 行为发生的环境和背景
        3. 宝宝的具体动作表现
        4. 家长的疑问或担忧
        
        场景要真实可信，涵盖不同类型的精细运动行为：
        - 抓握行为
        - 手指协调
        - 手眼配合
        - 物品操作
        - 双手协作
        
        格式：
        场景1：[具体描述宝宝的行为表现和家长观察到的情况]
        ---
        场景2：[具体描述...]
        ---
        """
        
        scenarios = []
        content = self.call_qwen_api(prompt, max_tokens=3000)
        
        if content:
            scenario_blocks = content.split('---')
            for block in scenario_blocks:
                scenario = block.strip()
                if scenario and len(scenario) > 50:
                    scenarios.append({
                        'age_months': age_months,
                        'scenario': scenario,
                        'type': 'behavior_observation'
                    })
        
        return scenarios
    
    def generate_analysis_qa_from_scenario(self, scenario_data: dict) -> dict:
        """基于行为场景生成分析问答"""
        age_months = scenario_data['age_months']
        scenario = scenario_data['scenario']
        
        prompt = f"""
        基于以下{age_months}个月宝宝的实际行为观察，请生成一个专业的分析问答：
        
        行为观察：{scenario}
        
        请创建一个问答对，包含：
        
        问题：以家长的口吻描述观察到的具体行为，并提出疑问
        - 要包含具体的行为细节
        - 体现家长的真实关切
        - 问题要自然、口语化
        
        答案：作为专业儿童发育专家的分析和指导，包含：
        1. 行为分析：这个行为说明了什么发育状况
        2. 发育评估：是否符合该月龄的正常发育水平
        3. 指导建议：具体的训练方法和注意事项
        4. 后续观察：家长需要继续关注的要点
        
        答案要专业但易懂，实用性强。
        
        格式：
        Q: [家长的具体问题]
        A: [专家的详细分析和指导]
        """
        
        content = self.call_qwen_api(prompt)
        
        if content and 'Q:' in content and 'A:' in content:
            lines = content.strip().split('\n')
            question = ""
            answer = ""
            
            current_section = None
            for line in lines:
                if line.startswith('Q:'):
                    current_section = 'question'
                    question = line[2:].strip()
                elif line.startswith('A:'):
                    current_section = 'answer'
                    answer = line[2:].strip()
                elif current_section == 'question' and line.strip():
                    question += " " + line.strip()
                elif current_section == 'answer' and line.strip():
                    answer += " " + line.strip()
            
            if question and answer:
                return {
                    'question': question,
                    'answer': answer,
                    'category': 'behavior_analysis',
                    'age_group': f"{age_months}个月",
                    'difficulty': 'intermediate',
                    'type': 'real_behavior_case',
                    'generated_by': 'qwen_api_behavior'
                }
        
        return None
    
    def generate_comparative_behavior_qa(self) -> list:
        """生成对比性行为分析问答"""
        comparative_scenarios = [
            {
                'title': '同龄宝宝不同表现对比',
                'prompt': '''
                请创建3个对比性的行为分析问答，展示同一月龄宝宝的不同精细运动表现：
                
                每个问答包含：
                - 两个同龄宝宝的不同行为表现
                - 家长对差异的疑问
                - 专家对个体差异的分析
                - 针对性的指导建议
                
                格式：Q: [对比性问题] A: [专业分析]
                '''
            },
            {
                'title': '发育进程中的行为变化',
                'prompt': '''
                请创建3个关于宝宝精细运动发育进程的行为分析问答：
                
                每个问答包含：
                - 宝宝在不同时期的行为变化
                - 家长对发育进程的疑问
                - 专家对发育规律的解释
                - 如何支持正常发育的建议
                
                格式：Q: [发育变化问题] A: [专业解释]
                '''
            },
            {
                'title': '异常行为的识别分析',
                'prompt': '''
                请创建3个关于可能异常精细运动行为的分析问答：
                
                每个问答包含：
                - 可能提示发育问题的具体行为
                - 家长的担忧和疑问
                - 专家对异常信号的分析
                - 何时需要专业评估的建议
                
                格式：Q: [异常行为问题] A: [专业判断]
                '''
            }
        ]
        
        comparative_qa = []
        
        for scenario in comparative_scenarios:
            content = self.call_qwen_api(scenario['prompt'])
            
            if content:
                # 解析多个问答对
                qa_blocks = content.split('A:')
                for i in range(len(qa_blocks) - 1):
                    q_part = qa_blocks[i].split('Q:')[-1].strip()
                    a_part = qa_blocks[i + 1].split('Q:')[0].strip()
                    
                    if q_part and a_part:
                        comparative_qa.append({
                            'question': q_part,
                            'answer': a_part,
                            'category': 'comparative_behavior_analysis',
                            'age_group': 'multiple',
                            'difficulty': 'advanced',
                            'type': scenario['title'],
                            'generated_by': 'qwen_api_comparative'
                        })
            
            time.sleep(1)
        
        return comparative_qa
    
    def generate_intervention_qa(self) -> list:
        """生成干预指导问答"""
        intervention_prompt = """
        请创建5个基于实际行为观察的干预指导问答：
        
        每个问答应该包含：
        1. 家长观察到的具体问题行为
        2. 对该行为的专业分析
        3. 详细的干预训练方案
        4. 预期效果和时间框架
        5. 何时需要寻求专业帮助
        
        涵盖不同类型的干预需求：
        - 抓握能力不足
        - 手眼协调困难
        - 双手协作问题
        - 精细动作迟缓
        - 手指力量不足
        
        每个问答要实用、具体、可操作。
        
        格式：Q: [具体行为问题] A: [详细干预方案]
        """
        
        intervention_qa = []
        content = self.call_qwen_api(intervention_prompt, max_tokens=4000)
        
        if content:
            qa_blocks = content.split('A:')
            for i in range(len(qa_blocks) - 1):
                q_part = qa_blocks[i].split('Q:')[-1].strip()
                a_part = qa_blocks[i + 1].split('Q:')[0].strip()
                
                if q_part and a_part:
                    intervention_qa.append({
                        'question': q_part,
                        'answer': a_part,
                        'category': 'intervention_guidance',
                        'age_group': 'general',
                        'difficulty': 'advanced',
                        'type': 'behavior_intervention',
                        'generated_by': 'qwen_api_intervention'
                    })
        
        return intervention_qa
    
    def generate_behavior_analysis_dataset(self, target_samples: int = 100):
        """生成行为分析数据集"""
        print("🚀 开始生成基于实际行为观察的精细运动分析问答...")
        
        all_qa_pairs = []
        
        # 1. 为关键月龄生成行为场景和分析问答
        print("👶 为关键月龄生成行为观察问答...")
        key_months = [2, 4, 6, 8, 10, 12, 15, 18, 21, 24, 30, 36]  # 12个关键月龄
        
        for month in key_months:
            print(f"   生成{month}个月宝宝行为分析...")
            
            # 生成行为场景
            scenarios = self.generate_behavior_scenarios(month, count=4)
            
            # 为每个场景生成分析问答
            for scenario in scenarios:
                qa = self.generate_analysis_qa_from_scenario(scenario)
                if qa:
                    all_qa_pairs.append(qa)
            
            time.sleep(1)  # API调用间隔
        
        print(f"   ✅ 月龄行为分析生成完成: {len(all_qa_pairs)} 个")
        
        # 2. 生成对比性行为分析问答
        print("🔍 生成对比性行为分析...")
        comparative_qa = self.generate_comparative_behavior_qa()
        all_qa_pairs.extend(comparative_qa)
        print(f"   ✅ 对比分析生成完成: 当前总数 {len(all_qa_pairs)} 个")
        
        # 3. 生成干预指导问答
        print("🎯 生成干预指导问答...")
        intervention_qa = self.generate_intervention_qa()
        all_qa_pairs.extend(intervention_qa)
        print(f"   ✅ 干预指导生成完成: 当前总数 {len(all_qa_pairs)} 个")
        
        # 4. 如果需要更多样本，生成补充内容
        if len(all_qa_pairs) < target_samples:
            remaining = target_samples - len(all_qa_pairs)
            print(f"📝 生成补充行为分析: 还需 {remaining} 个")
            
            supplement_prompt = f"""
            请生成{remaining}个基于实际婴幼儿行为观察的精细运动分析问答：
            
            每个问答要包含：
            1. 具体的行为观察描述
            2. 专业的发育分析
            3. 实用的指导建议
            
            涵盖不同场景：
            - 日常生活中的精细动作
            - 游戏中的手部行为
            - 进食时的手部协调
            - 探索活动中的手指使用
            
            格式：Q: [具体行为观察问题] A: [专业分析和指导]
            """
            
            content = self.call_qwen_api(supplement_prompt, max_tokens=4000)
            if content:
                qa_blocks = content.split('A:')
                for i in range(len(qa_blocks) - 1):
                    if len(all_qa_pairs) >= target_samples:
                        break
                    
                    q_part = qa_blocks[i].split('Q:')[-1].strip()
                    a_part = qa_blocks[i + 1].split('Q:')[0].strip()
                    
                    if q_part and a_part:
                        all_qa_pairs.append({
                            'question': q_part,
                            'answer': a_part,
                            'category': 'behavior_analysis_supplement',
                            'age_group': 'general',
                            'difficulty': 'intermediate',
                            'type': 'behavior_observation',
                            'generated_by': 'qwen_api_supplement'
                        })
        
        # 5. 整合数据
        self.behavior_qa_data['metadata']['actual_samples'] = len(all_qa_pairs)
        self.behavior_qa_data['data'] = all_qa_pairs
        
        print(f"✅ 行为分析数据集生成完成！实际生成 {len(all_qa_pairs)} 条数据")
        
        return self.behavior_qa_data
    
    def save_dataset(self, filename: str = 'behavior_analysis_qa_dataset.json'):
        """保存行为分析数据集"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.behavior_qa_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 行为分析数据集已保存到: {filename}")
        
        # 生成统计报告
        self.generate_report()
    
    def generate_report(self):
        """生成数据集报告"""
        data = self.behavior_qa_data['data']
        
        # 统计分析
        category_stats = {}
        age_stats = {}
        type_stats = {}
        
        for item in data:
            category = item.get('category', 'unknown')
            category_stats[category] = category_stats.get(category, 0) + 1
            
            age_group = item.get('age_group', 'unknown')
            age_stats[age_group] = age_stats.get(age_group, 0) + 1
            
            item_type = item.get('type', 'unknown')
            type_stats[item_type] = type_stats.get(item_type, 0) + 1
        
        report = f"""# 婴幼儿精细运动行为分析问答数据集报告

## 📊 基本信息
- **数据集名称**: {self.behavior_qa_data['metadata']['name']}
- **实际样本数**: {self.behavior_qa_data['metadata']['actual_samples']}
- **生成时间**: {self.behavior_qa_data['metadata']['created_time']}
- **生成模型**: {self.behavior_qa_data['metadata']['enhancement_model']}
- **数据特色**: {self.behavior_qa_data['metadata']['focus']}

## 📈 数据分布

### 类别分布
"""
        
        for category, count in sorted(category_stats.items()):
            percentage = (count / len(data)) * 100
            report += f"- **{category}**: {count} 个 ({percentage:.1f}%)\n"
        
        report += "\n### 年龄组分布\n"
        for age_group, count in sorted(age_stats.items()):
            percentage = (count / len(data)) * 100
            report += f"- **{age_group}**: {count} 个 ({percentage:.1f}%)\n"
        
        report += "\n### 内容类型分布\n"
        for item_type, count in sorted(type_stats.items()):
            percentage = (count / len(data)) * 100
            report += f"- **{item_type}**: {count} 个 ({percentage:.1f}%)\n"
        
        report += f"""
## 🎯 数据集特色

### 💡 基于实际行为观察
- 每个问答都包含具体的婴幼儿行为描述
- 真实反映家长的观察和疑问
- 提供专业的行为分析和发育评估

### 🔍 专业分析深度
- 行为背后的发育意义解读
- 与正常发育标准的对比分析
- 个体差异的专业解释

### 🎯 实用指导导向
- 具体可操作的训练建议
- 家庭环境下的实施方法
- 何时需要专业干预的判断

### 📋 多样化场景覆盖
- 日常生活中的精细动作观察
- 游戏活动中的行为表现
- 不同月龄的发育特点
- 异常行为的识别和处理

## 🚀 应用价值

### 1. LLM训练优势
- **真实性**: 基于实际行为观察，训练出的模型更贴近实际应用
- **专业性**: 包含专业分析，提升模型的专业判断能力
- **实用性**: 注重指导建议，增强模型的实用价值

### 2. 智能问答系统
- **场景识别**: 能够识别和分析具体的行为描述
- **专业判断**: 提供基于行为的发育评估
- **个性化指导**: 针对具体行为给出定制化建议

### 3. 家长教育工具
- **行为理解**: 帮助家长理解宝宝行为的发育意义
- **观察指导**: 教会家长如何观察和记录宝宝行为
- **及时干预**: 识别需要关注的行为信号

## 💡 使用建议

1. **结合基础数据集**: 与理论知识数据集配合使用，获得更全面的训练效果
2. **重点关注实例**: 这类数据特别适合训练模型的实际应用能力
3. **持续更新**: 可以根据实际使用反馈，不断补充新的行为观察案例
4. **专业验证**: 建议专业人士参与验证，确保分析的准确性

## 🔄 扩展方向

- 增加视频行为分析的文字描述
- 补充更多特殊情况的行为观察
- 加入跨文化背景的行为差异分析
- 扩展到其他发育领域的行为观察
"""
        
        with open('behavior_analysis_qa_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("📊 行为分析数据集报告已生成: behavior_analysis_qa_report.md")

def main():
    """主函数"""
    print("🎯 婴幼儿精细运动行为分析问答数据集生成器")
    print("="*60)
    
    generator = BehaviorAnalysisQAGenerator()
    
    # 生成行为分析数据集
    dataset = generator.generate_behavior_analysis_dataset(target_samples=100)
    
    # 保存数据集
    generator.save_dataset()
    
    print("\n🎉 行为分析问答数据集生成完成！")
    print("📁 生成的文件:")
    print("   - behavior_analysis_qa_dataset.json (行为分析数据集)")
    print("   - behavior_analysis_qa_report.md (统计报告)")
    print("\n🌟 数据集特色:")
    print("   ✅ 基于实际行为观察")
    print("   ✅ 专业分析和指导")
    print("   ✅ 真实场景应用")
    print("   ✅ 个性化建议")

if __name__ == "__main__":
    main()
