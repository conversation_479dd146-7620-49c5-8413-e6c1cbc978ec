#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成300个高质量的婴幼儿精细运动行为分析+指导问答
专注于实际行为观察、专业分析和具体指导
"""

import json
import os
import time
import random
import dashscope
from dashscope import Generation

class Generate300BehaviorQA:
    def __init__(self, api_key: str = None):
        """初始化300条行为分析问答生成器"""
        if api_key:
            dashscope.api_key = api_key
        elif os.getenv('QWEN_API_KEY'):
            dashscope.api_key = os.getenv('QWEN_API_KEY')
        else:
            dashscope.api_key = 'sk-5eba46fbcff649d5bf28313bc865de10'
        
        self.target_samples = 300
        self.generated_data = {
            'metadata': {
                'name': '婴幼儿精细运动行为分析+指导问答数据集 - 300条版本',
                'version': '2.0',
                'target_samples': 300,
                'description': '基于实际行为观察的高质量精细运动分析和指导问答',
                'created_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'enhancement_model': 'qwen-max',
                'focus': '行为观察+专业分析+具体指导'
            },
            'data': []
        }
        
        # 定义各种行为场景类型
        self.behavior_scenarios = [
            "抓握行为观察", "手眼协调表现", "双手配合活动", "工具使用学习",
            "精细动作控制", "手指灵活性", "力量控制", "空间判断",
            "模仿学习", "自主探索", "问题解决", "注意力集中"
        ]
        
        # 定义不同的生活场景
        self.life_contexts = [
            "进食时间", "游戏活动", "日常护理", "学习活动",
            "户外探索", "社交互动", "睡前准备", "洗澡时间"
        ]
    
    def call_qwen_api(self, prompt: str, max_tokens: int = 2500, retry_count: int = 3) -> str:
        """调用Qwen API，带重试机制"""
        for attempt in range(retry_count):
            try:
                messages = [
                    {
                        'role': 'system',
                        'content': '你是一位资深的儿童发育专家和行为分析师，拥有丰富的0-3岁婴幼儿精细运动发展临床经验。请基于具体的行为观察，提供专业的分析和实用的指导建议。每个回答都要包含行为分析、发育评估、指导建议和观察要点四个部分。'
                    },
                    {
                        'role': 'user',
                        'content': prompt
                    }
                ]

                response = Generation.call(
                    model='qwen-max',
                    messages=messages,
                    result_format='message',
                    max_tokens=max_tokens,
                    temperature=0.7
                )

                if response.status_code == 200:
                    content = response.output.choices[0].message.content
                    if content and len(content) > 100:  # 确保返回内容有意义
                        return content
                    else:
                        print(f"API返回内容过短，重试... (尝试 {attempt + 1}/{retry_count})")
                else:
                    print(f"API调用失败: {response.message} (尝试 {attempt + 1}/{retry_count})")

            except Exception as e:
                print(f"API调用异常: {e} (尝试 {attempt + 1}/{retry_count})")

            if attempt < retry_count - 1:
                time.sleep(2)  # 重试前等待

        return ""
    
    def generate_detailed_behavior_batch(self, age_months: int, batch_size: int = 5) -> list:
        """为特定月龄生成详细的行为分析问答批次"""

        prompt = f"""
        为{age_months}个月大的宝宝创建{batch_size}个基于实际行为观察的精细运动分析问答。

        每个问答要求：
        1. 问题：家长描述具体观察到的宝宝行为，表达困惑或担忧
        2. 答案：包含四个部分
           - 行为分析：解释行为的发育意义
           - 发育评估：判断是否正常
           - 指导建议：3个具体训练方法
           - 观察要点：后续观察重点

        格式：
        Q: [家长观察描述]
        A: **行为分析**：... **发育评估**：... **指导建议**：... **观察要点**：...
        ---
        """
        
        qa_pairs = []
        content = self.call_qwen_api(prompt, max_tokens=4000)
        
        if content:
            qa_blocks = content.split('---')
            for block in qa_blocks:
                if 'Q:' in block and 'A:' in block:
                    lines = block.strip().split('\n')
                    question = ""
                    answer = ""
                    
                    current_section = None
                    for line in lines:
                        if line.startswith('Q:'):
                            current_section = 'question'
                            question = line[2:].strip()
                        elif line.startswith('A:'):
                            current_section = 'answer'
                            answer = line[2:].strip()
                        elif current_section == 'question' and line.strip():
                            question += " " + line.strip()
                        elif current_section == 'answer' and line.strip():
                            answer += " " + line.strip()
                    
                    if question and answer and len(question) > 50 and len(answer) > 200:
                        qa_pairs.append({
                            'question': question,
                            'answer': answer,
                            'category': 'detailed_behavior_analysis',
                            'age_group': f"{age_months}个月",
                            'difficulty': 'intermediate',
                            'behavior_types': ['精细运动观察'],
                            'life_contexts': ['日常生活'],
                            'generated_by': 'qwen_api_detailed'
                        })
        
        return qa_pairs
    
    def generate_comparative_analysis_batch(self, batch_size: int = 15) -> list:
        """生成对比分析问答批次"""
        prompt = f"""
        创建{batch_size}个基于行为对比的精细运动分析问答。
        
        类型包括：
        1. 同龄不同表现对比（5个）
        2. 不同月龄发育对比（5个）
        3. 正常vs异常行为对比（5个）
        
        每个问答要求：
        - 问题：描述具体的对比情况，体现家长的困惑
        - 答案：包含行为分析、发育评估、指导建议、观察要点四部分
        - 重点解释个体差异、发育规律、识别异常信号
        
        确保内容专业、实用、易懂。
        
        格式：Q: [对比性问题] A: [结构化专业回答]
        ---
        """
        
        qa_pairs = []
        content = self.call_qwen_api(prompt, max_tokens=4000)
        
        if content:
            qa_blocks = content.split('---')
            for block in qa_blocks:
                if 'Q:' in block and 'A:' in block:
                    lines = block.strip().split('\n')
                    question = ""
                    answer = ""
                    
                    current_section = None
                    for line in lines:
                        if line.startswith('Q:'):
                            current_section = 'question'
                            question = line[2:].strip()
                        elif line.startswith('A:'):
                            current_section = 'answer'
                            answer = line[2:].strip()
                        elif current_section == 'question' and line.strip():
                            question += " " + line.strip()
                        elif current_section == 'answer' and line.strip():
                            answer += " " + line.strip()
                    
                    if question and answer and len(answer) > 200:
                        qa_pairs.append({
                            'question': question,
                            'answer': answer,
                            'category': 'comparative_behavior_analysis',
                            'age_group': 'multiple',
                            'difficulty': 'advanced',
                            'type': 'behavior_comparison',
                            'generated_by': 'qwen_api_comparative'
                        })
        
        return qa_pairs
    
    def generate_intervention_guidance_batch(self, batch_size: int = 20) -> list:
        """生成干预指导问答批次"""
        prompt = f"""
        创建{batch_size}个基于问题行为的干预指导问答。
        
        涵盖以下问题类型：
        - 抓握能力不足或异常
        - 手眼协调困难
        - 双手配合问题
        - 精细动作迟缓
        - 工具使用困难
        - 注意力不集中
        - 力量控制问题
        - 空间判断偏差
        
        每个问答要求：
        1. 问题：家长描述具体的问题行为和担忧
        2. 答案：包含四个部分
           **行为分析**：分析问题的可能原因
           **发育评估**：判断严重程度和紧急性
           **指导建议**：提供分阶段的具体干预方案
           **观察要点**：何时需要专业评估，如何监测进展
        
        干预方案要具体、可操作、循序渐进。
        
        格式：Q: [问题行为描述] A: [专业干预指导]
        ---
        """
        
        qa_pairs = []
        content = self.call_qwen_api(prompt, max_tokens=4000)
        
        if content:
            qa_blocks = content.split('---')
            for block in qa_blocks:
                if 'Q:' in block and 'A:' in block:
                    lines = block.strip().split('\n')
                    question = ""
                    answer = ""
                    
                    current_section = None
                    for line in lines:
                        if line.startswith('Q:'):
                            current_section = 'question'
                            question = line[2:].strip()
                        elif line.startswith('A:'):
                            current_section = 'answer'
                            answer = line[2:].strip()
                        elif current_section == 'question' and line.strip():
                            question += " " + line.strip()
                        elif current_section == 'answer' and line.strip():
                            answer += " " + line.strip()
                    
                    if question and answer and len(answer) > 250:
                        qa_pairs.append({
                            'question': question,
                            'answer': answer,
                            'category': 'intervention_guidance',
                            'age_group': 'general',
                            'difficulty': 'advanced',
                            'type': 'problem_solving',
                            'generated_by': 'qwen_api_intervention'
                        })
        
        return qa_pairs
    
    def generate_special_situations_batch(self, batch_size: int = 15) -> list:
        """生成特殊情况分析问答批次"""
        prompt = f"""
        创建{batch_size}个特殊情况下的精细运动行为分析问答。
        
        特殊情况包括：
        - 早产儿的精细运动发育
        - 双胞胎/多胞胎的发育差异
        - 疾病后的恢复期表现
        - 环境变化对行为的影响
        - 季节性行为变化
        - 情绪状态对精细动作的影响
        - 睡眠不足时的表现
        - 新技能学习期的行为
        
        每个问答要求：
        - 问题：描述特殊情况下的具体行为观察
        - 答案：专业分析特殊性，提供针对性指导
        - 重点关注特殊情况的处理策略
        
        格式：Q: [特殊情况行为] A: [专业分析指导]
        ---
        """
        
        qa_pairs = []
        content = self.call_qwen_api(prompt, max_tokens=4000)
        
        if content:
            qa_blocks = content.split('---')
            for block in qa_blocks:
                if 'Q:' in block and 'A:' in block:
                    lines = block.strip().split('\n')
                    question = ""
                    answer = ""
                    
                    current_section = None
                    for line in lines:
                        if line.startswith('Q:'):
                            current_section = 'question'
                            question = line[2:].strip()
                        elif line.startswith('A:'):
                            current_section = 'answer'
                            answer = line[2:].strip()
                        elif current_section == 'question' and line.strip():
                            question += " " + line.strip()
                        elif current_section == 'answer' and line.strip():
                            answer += " " + line.strip()
                    
                    if question and answer and len(answer) > 200:
                        qa_pairs.append({
                            'question': question,
                            'answer': answer,
                            'category': 'special_situations',
                            'age_group': 'various',
                            'difficulty': 'expert',
                            'type': 'special_case_analysis',
                            'generated_by': 'qwen_api_special'
                        })
        
        return qa_pairs
    
    def generate_300_behavior_qa_dataset(self):
        """生成300条行为分析问答数据集"""
        print("🚀 开始生成300条高质量行为分析+指导问答...")
        
        all_qa_pairs = []
        
        # 1. 为各个月龄生成详细行为分析问答 (预计200条)
        print("👶 为各月龄生成详细行为分析问答...")
        key_months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 14, 16, 18, 20, 22, 24, 27, 30, 33, 36]  # 22个月龄
        
        for i, month in enumerate(key_months):
            print(f"   生成{month}个月宝宝行为分析... ({i+1}/{len(key_months)})")
            
            # 每个月龄生成8-10个问答
            batch_size = 9 if len(all_qa_pairs) < 180 else max(1, (200 - len(all_qa_pairs)) // (len(key_months) - i))
            monthly_qa = self.generate_detailed_behavior_batch(month, batch_size)
            all_qa_pairs.extend(monthly_qa)
            
            print(f"      本批次生成: {len(monthly_qa)} 个，累计: {len(all_qa_pairs)} 个")
            time.sleep(1.5)  # API调用间隔
            
            if len(all_qa_pairs) >= 200:
                break
        
        print(f"   ✅ 月龄行为分析完成: {len(all_qa_pairs)} 个")
        
        # 2. 生成对比分析问答 (预计40条)
        print("🔍 生成对比分析问答...")
        remaining_for_comparative = min(40, 250 - len(all_qa_pairs))
        
        for i in range(0, remaining_for_comparative, 15):
            batch_size = min(15, remaining_for_comparative - i)
            comparative_qa = self.generate_comparative_analysis_batch(batch_size)
            all_qa_pairs.extend(comparative_qa)
            print(f"   对比分析批次 {i//15 + 1}: {len(comparative_qa)} 个，累计: {len(all_qa_pairs)} 个")
            time.sleep(1.5)
        
        print(f"   ✅ 对比分析完成: 当前总数 {len(all_qa_pairs)} 个")
        
        # 3. 生成干预指导问答 (预计40条)
        print("🎯 生成干预指导问答...")
        remaining_for_intervention = min(40, 290 - len(all_qa_pairs))
        
        for i in range(0, remaining_for_intervention, 20):
            batch_size = min(20, remaining_for_intervention - i)
            intervention_qa = self.generate_intervention_guidance_batch(batch_size)
            all_qa_pairs.extend(intervention_qa)
            print(f"   干预指导批次 {i//20 + 1}: {len(intervention_qa)} 个，累计: {len(all_qa_pairs)} 个")
            time.sleep(1.5)
        
        print(f"   ✅ 干预指导完成: 当前总数 {len(all_qa_pairs)} 个")
        
        # 4. 生成特殊情况分析问答 (补充到300条)
        print("🌟 生成特殊情况分析问答...")
        remaining = self.target_samples - len(all_qa_pairs)
        
        if remaining > 0:
            special_qa = self.generate_special_situations_batch(remaining)
            all_qa_pairs.extend(special_qa)
            print(f"   特殊情况分析: {len(special_qa)} 个，累计: {len(all_qa_pairs)} 个")
        
        # 5. 整合最终数据集
        print("🔗 整合最终数据集...")
        self.generated_data['metadata']['actual_samples'] = len(all_qa_pairs)
        self.generated_data['data'] = all_qa_pairs
        
        print(f"✅ 300条行为分析问答数据集生成完成！实际生成 {len(all_qa_pairs)} 条数据")
        
        return self.generated_data
    
    def save_dataset(self, filename: str = 'behavior_analysis_300_dataset.json'):
        """保存300条数据集"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.generated_data, f, ensure_ascii=False, indent=2)
        
        print(f"💾 300条行为分析数据集已保存到: {filename}")
        
        # 生成统计报告
        self.generate_comprehensive_report()
    
    def generate_comprehensive_report(self):
        """生成综合统计报告"""
        data = self.generated_data['data']
        
        # 详细统计分析
        category_stats = {}
        age_stats = {}
        difficulty_stats = {}
        type_stats = {}
        
        for item in data:
            category = item.get('category', 'unknown')
            category_stats[category] = category_stats.get(category, 0) + 1
            
            age_group = item.get('age_group', 'unknown')
            age_stats[age_group] = age_stats.get(age_group, 0) + 1
            
            difficulty = item.get('difficulty', 'unknown')
            difficulty_stats[difficulty] = difficulty_stats.get(difficulty, 0) + 1
            
            item_type = item.get('type', 'unknown')
            type_stats[item_type] = type_stats.get(item_type, 0) + 1
        
        report = f"""# 300条婴幼儿精细运动行为分析+指导数据集报告

## 📊 基本信息
- **数据集名称**: {self.generated_data['metadata']['name']}
- **目标样本数**: {self.generated_data['metadata']['target_samples']}
- **实际样本数**: {self.generated_data['metadata']['actual_samples']}
- **生成时间**: {self.generated_data['metadata']['created_time']}
- **生成模型**: {self.generated_data['metadata']['enhancement_model']}
- **数据特色**: {self.generated_data['metadata']['focus']}

## 📈 详细数据分布

### 类别分布
"""
        
        for category, count in sorted(category_stats.items()):
            percentage = (count / len(data)) * 100
            report += f"- **{category}**: {count} 个 ({percentage:.1f}%)\n"
        
        report += "\n### 年龄组分布\n"
        for age_group, count in sorted(age_stats.items()):
            percentage = (count / len(data)) * 100
            report += f"- **{age_group}**: {count} 个 ({percentage:.1f}%)\n"
        
        report += "\n### 难度分布\n"
        for difficulty, count in sorted(difficulty_stats.items()):
            percentage = (count / len(data)) * 100
            report += f"- **{difficulty}**: {count} 个 ({percentage:.1f}%)\n"
        
        report += f"""
## 🎯 数据集特色

### 💡 基于实际行为观察
- 每个问答都包含具体的婴幼儿行为描述
- 真实反映家长的观察和疑问
- 提供专业的行为分析和发育评估

### 🔍 结构化专业分析
每个答案都包含四个标准化部分：
1. **行为分析**：解释行为的发育意义
2. **发育评估**：判断是否符合正常发育水平
3. **指导建议**：提供具体可操作的训练方法
4. **观察要点**：指导后续观察和专业求助时机

### 🎯 全面覆盖各种情况
- **常规发育**: 各月龄的典型行为分析
- **对比分析**: 个体差异和发育变化
- **问题干预**: 针对问题行为的具体指导
- **特殊情况**: 特殊环境下的行为分析

## 🚀 应用价值

### 1. LLM训练优势
- **场景理解**: 训练模型识别复杂的行为描述
- **专业分析**: 培养基于行为的发育评估能力
- **结构化输出**: 确保回答的专业性和完整性
- **个性化指导**: 针对具体情况提供定制化建议

### 2. 智能问答系统
- 处理真实的家长咨询场景
- 提供结构化的专业分析
- 给出具体可操作的指导建议
- 判断何时需要专业干预

### 3. 专业培训应用
- 教授行为观察和分析技能
- 提供标准化的评估框架
- 培养问题识别和解决能力
- 增强专业判断水平

## 💡 质量保证

### ✅ 内容质量
- 基于儿童发育学理论
- 符合临床实践标准
- 语言通俗易懂
- 建议具体可操作

### ✅ 数据多样性
- 覆盖0-36个月各发育阶段
- 包含多种行为类型和生活场景
- 涵盖正常、异常、特殊情况
- 体现个体差异和发育规律

### ✅ 实用性导向
- 解决实际育儿问题
- 提供可操作的指导方案
- 明确专业求助时机
- 支持家长自主判断

## 🔄 后续发展

### 短期优化
- 根据使用反馈优化内容质量
- 补充更多特殊情况案例
- 增加跨文化背景的行为差异

### 长期扩展
- 整合多模态数据（图片、视频描述）
- 扩展到其他发育领域
- 构建完整的发育评估体系
- 支持个性化发育追踪

---

**生成完成时间**: {time.strftime('%Y-%m-%d %H:%M:%S')}  
**数据质量**: 高质量专业内容  
**应用场景**: LLM训练、智能问答、专业培训  
**技术特色**: 行为观察+专业分析+具体指导
"""
        
        with open('behavior_analysis_300_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("📊 300条数据集详细报告已生成: behavior_analysis_300_report.md")

def main():
    """主函数"""
    print("🎯 生成300条高质量婴幼儿精细运动行为分析+指导问答")
    print("="*70)
    
    generator = Generate300BehaviorQA()
    
    # 生成300条数据
    dataset = generator.generate_300_behavior_qa_dataset()
    
    # 保存数据集
    generator.save_dataset()
    
    print("\n🎉 300条高质量行为分析+指导数据集生成完成！")
    print("📁 生成的文件:")
    print("   - behavior_analysis_300_dataset.json (300条数据集)")
    print("   - behavior_analysis_300_report.md (详细统计报告)")
    print("\n🌟 数据集特色:")
    print("   ✅ 基于实际行为观察")
    print("   ✅ 结构化专业分析")
    print("   ✅ 具体可操作指导")
    print("   ✅ 全面覆盖各种情况")

if __name__ == "__main__":
    main()
