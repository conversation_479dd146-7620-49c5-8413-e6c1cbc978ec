#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版婴幼儿精细运动发展指导数据集构建器
整合多个数据源，构建用于训练LLM的专业数据集
支持Qwen API增强和多源数据融合
"""

import json
import re
import os
import time
from pathlib import Path
from typing import Dict, List, Any
import dashscope
from dashscope import Generation

class EnhancedFineMotorDatasetBuilder:
    def __init__(self, api_key: str = None):
        """初始化数据集构建器"""
        if api_key:
            dashscope.api_key = api_key
        elif os.getenv('QWEN_API_KEY'):
            dashscope.api_key = os.getenv('QWEN_API_KEY')
        else:
            print("警告：未设置Qwen API密钥，将跳过AI增强功能")
            dashscope.api_key = None
        
        self.dataset = {
            'metadata': {
                'name': '婴幼儿精细运动发展指导数据集',
                'version': '2.0',
                'description': '专门用于训练婴幼儿精细运动发展指导LLM的综合数据集',
                'created_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'sources': ['web_scraped', 'academic_literature', 'qwen_enhanced'],
                'categories': ['monthly_standard', 'evaluation_method', 'training_guide', 'case_analysis', 'qa_pairs']
            },
            'data': []
        }
    
    def load_web_scraped_data(self, file_path: str) -> List[Dict]:
        """加载网页爬取的精细动作数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            content = data.get('content', '')
            extracted_data = []
            
            # 提取月龄标准
            month_pattern = r'▶\s*(\d+)个月宝宝.*?精细动作[：:](.*?)测评方法[：:](.*?)(?=▶\s*\d+个月宝宝|温馨提示|$)'
            matches = re.findall(month_pattern, content, re.DOTALL)
            
            for match in matches:
                month, action, evaluation = match
                extracted_data.append({
                    'type': 'monthly_standard',
                    'age_months': int(month),
                    'age_group': f"{month}个月",
                    'milestone': action.strip(),
                    'evaluation_method': evaluation.strip(),
                    'source': 'web_scraped'
                })
            
            # 提取训练指导
            training_pattern = r'(\d+-\d+个月)精细动作发展标准[：:](.*?)建议[：:](.*?)(?=\d+-\d+个月|$)'
            training_matches = re.findall(training_pattern, content, re.DOTALL)
            
            for match in training_matches:
                age_range, standard, suggestion = match
                extracted_data.append({
                    'type': 'training_guide',
                    'age_group': age_range.strip(),
                    'development_standard': standard.strip(),
                    'training_suggestion': suggestion.strip(),
                    'source': 'web_scraped'
                })
            
            return extracted_data
            
        except Exception as e:
            print(f"加载网页数据失败: {e}")
            return []
    
    def load_academic_data(self, file_paths: List[str]) -> List[Dict]:
        """加载学术文献数据"""
        academic_data = []
        
        for file_path in file_paths:
            if not os.path.exists(file_path):
                print(f"文件不存在，跳过: {file_path}")
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 处理不同格式的JSON文件
                if 'pages' in data:
                    # 学术文献格式
                    for page in data['pages']:
                        content_items = page.get('content', [])
                        for item in content_items:
                            text = item.get('text', '').strip()
                            item_type = item.get('type', '')
                            
                            # 过滤相关内容
                            if (len(text) > 50 and 
                                any(keyword in text for keyword in 
                                    ['精细动作', '精细运动', '手功能', '抓握', '手眼协调', '婴幼儿', '发育'])):
                                
                                academic_data.append({
                                    'type': 'academic_content',
                                    'content_type': item_type,
                                    'text': text,
                                    'source': os.path.basename(file_path),
                                    'page_id': page.get('page_id', 0)
                                })
                
                elif 'content' in data:
                    # 网页爬取格式
                    content = data['content']
                    if len(content) > 100:
                        academic_data.append({
                            'type': 'web_content',
                            'text': content,
                            'source': os.path.basename(file_path)
                        })
                        
            except Exception as e:
                print(f"加载学术数据失败 {file_path}: {e}")
                continue
        
        return academic_data
    
    def call_qwen_api(self, prompt: str, max_tokens: int = 2000) -> str:
        """调用Qwen API"""
        if not dashscope.api_key:
            return ""
        
        try:
            messages = [
                {
                    'role': 'system',
                    'content': '你是一位专业的儿童发育专家和育儿指导师，专门研究0-3岁婴幼儿精细运动发展。请基于提供的专业内容，生成高质量、实用的指导内容。'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ]
            
            response = Generation.call(
                model='qwen-max',
                messages=messages,
                result_format='message',
                max_tokens=max_tokens,
                temperature=0.7
            )
            
            if response.status_code == 200:
                return response.output.choices[0].message.content
            else:
                print(f"Qwen API调用失败: {response.message}")
                return ""
                
        except Exception as e:
            print(f"Qwen API调用异常: {e}")
            return ""
    
    def generate_qa_pairs(self, source_data: Dict) -> List[Dict]:
        """基于源数据生成问答对"""
        qa_pairs = []
        
        if source_data['type'] == 'monthly_standard':
            month = source_data['age_months']
            milestone = source_data['milestone']
            evaluation = source_data['evaluation_method']
            
            # 基础问答对
            qa_pairs.extend([
                {
                    'question': f"{month}个月宝宝的精细动作发育标准是什么？",
                    'answer': f"{month}个月宝宝的精细动作标准是：{milestone}",
                    'category': 'milestone_inquiry',
                    'age_group': f"{month}个月",
                    'difficulty': 'basic'
                },
                {
                    'question': f"如何测评{month}个月宝宝的精细动作发育？",
                    'answer': f"测评方法：{evaluation}",
                    'category': 'evaluation_method',
                    'age_group': f"{month}个月",
                    'difficulty': 'intermediate'
                }
            ])
            
            # 使用Qwen API生成增强问答
            if dashscope.api_key:
                prompt = f"""
                基于以下{month}个月宝宝精细动作发育信息，生成3个不同类型的专业问答对：
                
                发育标准：{milestone}
                测评方法：{evaluation}
                
                请生成以下类型的问答：
                1. 家长实际关心的问题（如何在家观察、什么时候需要担心等）
                2. 发育异常识别问题（如何判断发育迟缓、需要注意的警示信号）
                3. 训练指导问题（如何促进发育、适合的游戏活动等）
                
                每个问答要实用、专业，对家长有指导意义。
                格式：Q: 问题\nA: 答案\n---
                """
                
                enhanced_content = self.call_qwen_api(prompt)
                if enhanced_content:
                    # 解析生成的问答对
                    qa_blocks = enhanced_content.split('---')
                    for i, block in enumerate(qa_blocks):
                        if 'Q:' in block and 'A:' in block:
                            lines = block.strip().split('\n')
                            question = ""
                            answer = ""
                            for line in lines:
                                if line.startswith('Q:'):
                                    question = line[2:].strip()
                                elif line.startswith('A:'):
                                    answer = line[2:].strip()
                                elif answer and line.strip():
                                    answer += " " + line.strip()
                            
                            if question and answer:
                                categories = ['parent_concern', 'abnormal_detection', 'training_guidance']
                                qa_pairs.append({
                                    'question': question,
                                    'answer': answer,
                                    'category': categories[i] if i < len(categories) else 'enhanced_qa',
                                    'age_group': f"{month}个月",
                                    'difficulty': 'advanced',
                                    'enhanced_by': 'qwen_api'
                                })
        
        elif source_data['type'] == 'training_guide':
            age_group = source_data['age_group']
            standard = source_data['development_standard']
            suggestion = source_data['training_suggestion']
            
            qa_pairs.append({
                'question': f"{age_group}宝宝精细动作训练有什么具体建议？",
                'answer': f"发展标准：{standard}\n\n训练建议：{suggestion}",
                'category': 'training_guide',
                'age_group': age_group,
                'difficulty': 'intermediate'
            })
        
        return qa_pairs
    
    def generate_case_studies(self, monthly_data: List[Dict]) -> List[Dict]:
        """生成案例研究"""
        case_studies = []
        
        if not dashscope.api_key:
            return case_studies
        
        # 选择关键月龄生成案例
        key_months = [3, 6, 9, 12, 18, 24]
        
        for month in key_months:
            month_data = [d for d in monthly_data if d.get('age_months') == month]
            if month_data:
                data = month_data[0]
                
                prompt = f"""
                基于{month}个月宝宝精细动作发育标准，创建一个真实的临床案例研究：
                
                发育标准：{data.get('milestone', '')}
                测评方法：{data.get('evaluation_method', '')}
                
                请创建一个包含以下内容的完整案例：
                1. 宝宝基本情况（姓名可用小名，年龄、性别等）
                2. 家长观察到的具体表现和担忧
                3. 专业评估过程和结果
                4. 个性化指导建议和训练方案
                5. 3个月后的跟踪评估结果
                
                案例要真实可信，体现专业性，对其他家长有参考价值。
                """
                
                case_content = self.call_qwen_api(prompt, max_tokens=1500)
                if case_content:
                    case_studies.append({
                        'type': 'case_study',
                        'age_group': f"{month}个月",
                        'title': f"{month}个月宝宝精细动作发育案例分析",
                        'content': case_content,
                        'category': 'clinical_case'
                    })
                    
                    # 添加延时避免API限制
                    time.sleep(1)
        
        return case_studies
    
    def build_comprehensive_dataset(self):
        """构建综合数据集"""
        print("🚀 开始构建增强版婴幼儿精细运动发展指导数据集...")
        
        # 1. 加载网页爬取数据
        print("📥 加载网页爬取数据...")
        web_data = self.load_web_scraped_data('baby_fine_motor_development_final.json')
        print(f"   ✅ 加载了 {len(web_data)} 条网页数据")
        
        # 2. 加载学术文献数据
        print("📚 加载学术文献数据...")
        academic_files = [
            'tmp-convert-17532585662476209.json',
            'tmp-convert-17532586738269494.json',
            'tmp-convert-17532588008816385.json',
            'tmp-convert-17532710270943934.json',
            'baby_fine_motor_development_final.json'
        ]
        
        academic_data = self.load_academic_data(academic_files)
        print(f"   ✅ 加载了 {len(academic_data)} 条学术数据")
        
        # 3. 生成问答对
        print("💬 生成问答对...")
        all_qa_pairs = []
        
        # 从网页数据生成问答
        for data in web_data:
            qa_pairs = self.generate_qa_pairs(data)
            all_qa_pairs.extend(qa_pairs)
        
        # 从学术数据生成问答（限制数量）
        for data in academic_data[:10]:
            if data['type'] == 'academic_content' and len(data['text']) > 200:
                # 为学术内容生成问答
                if dashscope.api_key:
                    prompt = f"""
                    基于以下专业的婴幼儿精细运动发育学术内容，生成2个实用的问答对：
                    
                    内容：{data['text'][:1000]}
                    
                    请生成对家长或专业人士有帮助的问答，重点关注实际应用。
                    格式：Q: 问题\nA: 答案\n---
                    """
                    
                    enhanced_qa = self.call_qwen_api(prompt)
                    if enhanced_qa:
                        qa_blocks = enhanced_qa.split('---')
                        for block in qa_blocks:
                            if 'Q:' in block and 'A:' in block:
                                lines = block.strip().split('\n')
                                question = ""
                                answer = ""
                                for line in lines:
                                    if line.startswith('Q:'):
                                        question = line[2:].strip()
                                    elif line.startswith('A:'):
                                        answer = line[2:].strip()
                                    elif answer and line.strip():
                                        answer += " " + line.strip()
                                
                                if question and answer:
                                    all_qa_pairs.append({
                                        'question': question,
                                        'answer': answer,
                                        'category': 'academic_qa',
                                        'age_group': 'general',
                                        'difficulty': 'advanced',
                                        'source': data['source'],
                                        'enhanced_by': 'qwen_api'
                                    })
                    
                    time.sleep(0.5)  # API调用间隔
        
        print(f"   ✅ 生成了 {len(all_qa_pairs)} 个问答对")
        
        # 4. 生成案例研究
        print("📋 生成案例研究...")
        monthly_data = [d for d in web_data if d['type'] == 'monthly_standard']
        case_studies = self.generate_case_studies(monthly_data)
        print(f"   ✅ 生成了 {len(case_studies)} 个案例研究")
        
        # 5. 整合最终数据集
        print("🔗 整合最终数据集...")
        
        # 更新元数据
        self.dataset['metadata'].update({
            'total_samples': len(all_qa_pairs) + len(case_studies),
            'qa_pairs_count': len(all_qa_pairs),
            'case_studies_count': len(case_studies),
            'age_groups': ['0-3个月', '3-6个月', '6-9个月', '9-12个月', '12-18个月', '18-24个月', '24-36个月']
        })
        
        # 添加数据
        self.dataset['data'] = {
            'qa_pairs': all_qa_pairs,
            'case_studies': case_studies,
            'raw_sources': {
                'web_data_count': len(web_data),
                'academic_data_count': len(academic_data)
            }
        }
        
        print(f"✅ 数据集构建完成！总计 {self.dataset['metadata']['total_samples']} 个样本")
        
        return self.dataset
    
    def save_dataset(self, filename: str = 'enhanced_fine_motor_dataset.json'):
        """保存数据集"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.dataset, f, ensure_ascii=False, indent=2)
        
        print(f"💾 数据集已保存到: {filename}")
        
        # 生成统计报告
        self.generate_statistics_report()
    
    def generate_statistics_report(self):
        """生成统计报告"""
        if not self.dataset or 'data' not in self.dataset:
            return
        
        qa_pairs = self.dataset['data']['qa_pairs']
        case_studies = self.dataset['data']['case_studies']
        
        # 统计分析
        category_stats = {}
        age_stats = {}
        difficulty_stats = {}
        
        for qa in qa_pairs:
            # 类别统计
            category = qa.get('category', 'unknown')
            category_stats[category] = category_stats.get(category, 0) + 1
            
            # 年龄组统计
            age_group = qa.get('age_group', 'unknown')
            age_stats[age_group] = age_stats.get(age_group, 0) + 1
            
            # 难度统计
            difficulty = qa.get('difficulty', 'unknown')
            difficulty_stats[difficulty] = difficulty_stats.get(difficulty, 0) + 1
        
        # 生成报告
        report = f"""# 增强版婴幼儿精细运动发展指导数据集统计报告

## 📊 基本信息
- **数据集名称**: {self.dataset['metadata']['name']}
- **版本**: {self.dataset['metadata']['version']}
- **创建时间**: {self.dataset['metadata']['created_time']}
- **总样本数**: {self.dataset['metadata']['total_samples']}

## 📈 数据分布

### 问答对分析
- **总问答对数**: {len(qa_pairs)}

#### 类别分布
"""
        
        for category, count in sorted(category_stats.items()):
            percentage = (count / len(qa_pairs)) * 100
            report += f"- **{category}**: {count} 个 ({percentage:.1f}%)\n"
        
        report += "\n#### 年龄组分布\n"
        for age_group, count in sorted(age_stats.items()):
            percentage = (count / len(qa_pairs)) * 100
            report += f"- **{age_group}**: {count} 个 ({percentage:.1f}%)\n"
        
        report += "\n#### 难度分布\n"
        for difficulty, count in sorted(difficulty_stats.items()):
            percentage = (count / len(qa_pairs)) * 100
            report += f"- **{difficulty}**: {count} 个 ({percentage:.1f}%)\n"
        
        report += f"""
### 案例研究分析
- **案例总数**: {len(case_studies)}
- **覆盖年龄段**: {len(set(case['age_group'] for case in case_studies))} 个关键月龄

## 🎯 数据质量特点
- **多源融合**: 网页专业内容 + 学术文献 + AI智能增强
- **专业权威**: 基于医学教材和专业育儿网站
- **实用导向**: 包含具体测评方法和训练建议
- **全面覆盖**: 涵盖0-36个月各发育阶段
- **智能增强**: 使用Qwen大模型生成高质量扩展内容

## 🚀 应用场景
1. **LLM训练**: 专门用于婴幼儿精细运动发育指导的模型训练
2. **智能问答**: 构建育儿咨询和发育评估系统
3. **个性化指导**: 支持基于月龄的个性化建议生成
4. **专业工具**: 适用于儿科医生、早教师、育儿顾问等专业人士
5. **家长教育**: 为家长提供科学的发育指导和训练方法

## 📋 使用建议
- 建议结合实际临床经验进行模型微调
- 可根据具体应用场景筛选相关类别的数据
- 推荐定期更新数据集以保持内容的时效性
- 使用时请注意数据的专业性，建议专业人士参与验证
"""
        
        with open('enhanced_dataset_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("📊 详细统计报告已生成: enhanced_dataset_report.md")

def main():
    """主函数"""
    print("🏗️  增强版婴幼儿精细运动发展指导数据集构建器")
    print("="*60)
    
    # 可以通过环境变量或直接传入API密钥
    api_key = os.getenv('QWEN_API_KEY')
    if not api_key:
        print("💡 提示：设置QWEN_API_KEY环境变量可启用AI增强功能")
    
    builder = EnhancedFineMotorDatasetBuilder(api_key)
    
    # 构建数据集
    dataset = builder.build_comprehensive_dataset()
    
    # 保存数据集
    builder.save_dataset()
    
    print("\n🎉 增强版数据集构建完成！")
    print("📁 生成的文件:")
    print("   - enhanced_fine_motor_dataset.json (主数据集)")
    print("   - enhanced_dataset_report.md (详细统计报告)")
    print("\n💡 数据集特点:")
    print("   ✅ 多源数据融合")
    print("   ✅ AI智能增强")
    print("   ✅ 专业内容验证")
    print("   ✅ 实用指导导向")

if __name__ == "__main__":
    main()
