#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen增强版婴幼儿精细运动发展指导数据集构建器
使用Qwen API进行智能内容增强和扩展
"""

import json
import os
import time
import dashscope
from dashscope import Generation

class QwenEnhancedDatasetBuilder:
    def __init__(self, api_key: str = None):
        """初始化增强版构建器"""
        if api_key:
            dashscope.api_key = api_key
        elif os.getenv('QWEN_API_KEY'):
            dashscope.api_key = os.getenv('QWEN_API_KEY')
        else:
            raise ValueError("请设置Qwen API密钥")
        
        self.enhanced_dataset = {
            'metadata': {
                'name': 'Qwen增强版婴幼儿精细运动发展指导数据集',
                'version': '2.0',
                'description': '使用Qwen大模型增强的专业婴幼儿精细运动发展指导数据集',
                'created_time': time.strftime('%Y-%m-%d %H:%M:%S'),
                'enhancement_model': 'qwen-max',
                'base_dataset': 'basic_fine_motor_dataset.json'
            },
            'enhanced_data': []
        }
    
    def call_qwen_api(self, prompt: str, max_tokens: int = 2000) -> str:
        """调用Qwen API"""
        try:
            messages = [
                {
                    'role': 'system',
                    'content': '你是一位资深的儿童发育专家和育儿指导师，拥有丰富的0-3岁婴幼儿精细运动发展临床经验。请基于专业知识生成高质量、实用的指导内容。'
                },
                {
                    'role': 'user',
                    'content': prompt
                }
            ]
            
            response = Generation.call(
                model='qwen-max',
                messages=messages,
                result_format='message',
                max_tokens=max_tokens,
                temperature=0.7
            )
            
            if response.status_code == 200:
                return response.output.choices[0].message.content
            else:
                print(f"API调用失败: {response.message}")
                return ""
                
        except Exception as e:
            print(f"API调用异常: {e}")
            return ""
    
    def enhance_basic_qa(self, qa_pair: dict) -> list:
        """增强基础问答对"""
        enhanced_pairs = [qa_pair]  # 保留原始问答
        
        question = qa_pair['question']
        answer = qa_pair['answer']
        age_group = qa_pair.get('age_group', '')
        category = qa_pair.get('category', '')
        
        # 生成相关的扩展问答
        prompt = f"""
        基于以下婴幼儿精细运动发育的问答，生成5个相关但不同角度的专业问答对：

        原问题：{question}
        原答案：{answer}
        年龄组：{age_group}
        类别：{category}

        请生成以下类型的问答：
        1. 实际应用场景问题（家长在日常生活中如何观察和引导）
        2. 问题解决型问题（如果宝宝达不到标准怎么办）
        3. 深入理解问题（为什么这个发育阶段很重要）
        4. 训练方法问题（具体的训练游戏和活动）
        5. 注意事项问题（训练中需要注意的安全和细节）

        每个问答要专业、实用，对家长有指导价值。
        格式：Q: 问题\nA: 答案\n---
        """
        
        enhanced_content = self.call_qwen_api(prompt)
        if enhanced_content:
            qa_blocks = enhanced_content.split('---')
            for i, block in enumerate(qa_blocks):
                if 'Q:' in block and 'A:' in block:
                    lines = block.strip().split('\n')
                    new_question = ""
                    new_answer = ""
                    
                    for line in lines:
                        if line.startswith('Q:'):
                            new_question = line[2:].strip()
                        elif line.startswith('A:'):
                            new_answer = line[2:].strip()
                        elif new_answer and line.strip():
                            new_answer += " " + line.strip()
                    
                    if new_question and new_answer:
                        enhanced_types = ['practical_application', 'problem_solving', 'deep_understanding', 'training_method', 'safety_notes']
                        enhanced_pairs.append({
                            'question': new_question,
                            'answer': new_answer,
                            'category': f"{category}_enhanced_{enhanced_types[i] if i < len(enhanced_types) else 'general'}",
                            'age_group': age_group,
                            'difficulty': 'advanced',
                            'enhanced_by': 'qwen_api',
                            'original_category': category
                        })
        
        return enhanced_pairs
    
    def generate_case_studies(self, age_groups: list) -> list:
        """生成临床案例研究"""
        case_studies = []
        
        for age_group in age_groups:
            if '个月' in age_group and age_group != 'general':
                prompt = f"""
                为{age_group}宝宝创建一个真实的精细运动发育临床案例研究：
                
                请包含以下内容：
                1. 宝宝基本信息（化名、年龄、性别、家庭背景）
                2. 家长的观察和担忧
                3. 专业评估过程和发现
                4. 个性化指导方案
                5. 3个月后的跟踪结果
                6. 专家点评和建议
                
                案例要真实可信，体现专业性，对其他家长有参考价值。
                字数控制在800-1000字。
                """
                
                case_content = self.call_qwen_api(prompt, max_tokens=1500)
                if case_content:
                    case_studies.append({
                        'type': 'clinical_case',
                        'age_group': age_group,
                        'title': f'{age_group}宝宝精细运动发育案例分析',
                        'content': case_content,
                        'category': 'case_study',
                        'enhanced_by': 'qwen_api'
                    })
                    
                    time.sleep(1)  # API调用间隔
        
        return case_studies
    
    def generate_training_programs(self) -> list:
        """生成个性化训练方案"""
        training_programs = []
        
        age_ranges = [
            '0-3个月', '3-6个月', '6-9个月', '9-12个月', 
            '12-18个月', '18-24个月', '24-36个月'
        ]
        
        for age_range in age_ranges:
            prompt = f"""
            为{age_range}宝宝设计一套完整的精细运动发育训练方案：
            
            请包含：
            1. 训练目标和重点
            2. 每日训练计划（15-20分钟）
            3. 具体训练活动（5-8个）
            4. 所需材料和工具
            5. 注意事项和安全提醒
            6. 进度评估方法
            7. 常见问题解答
            
            方案要科学、实用、易于家长操作。
            """
            
            program_content = self.call_qwen_api(prompt, max_tokens=2000)
            if program_content:
                training_programs.append({
                    'type': 'training_program',
                    'age_group': age_range,
                    'title': f'{age_range}宝宝精细运动发育训练方案',
                    'content': program_content,
                    'category': 'training_guide',
                    'enhanced_by': 'qwen_api'
                })
                
                time.sleep(1)
        
        return training_programs
    
    def generate_expert_qa(self) -> list:
        """生成专家级问答"""
        expert_topics = [
            '精细运动发育迟缓的早期识别',
            '精细运动与认知发育的关系',
            '不同文化背景下的精细运动发育差异',
            '早产儿的精细运动发育特点',
            '精细运动训练的科学原理',
            '家庭环境对精细运动发育的影响',
            '精细运动发育的神经生理基础',
            '精细运动与语言发育的关联',
            '营养对精细运动发育的影响',
            '精细运动发育的个体差异',
            '精细运动训练中的安全注意事项',
            '数字化工具在精细运动评估中的应用'
        ]
        
        expert_qa = []
        
        for topic in expert_topics:
            prompt = f"""
            作为儿童发育专家，请就"{topic}"这个专业话题，生成3个深度的专业问答对：
            
            要求：
            1. 问题要有深度，体现专业水准
            2. 答案要科学严谨，引用相关研究
            3. 内容要对专业人士和家长都有价值
            4. 包含实际应用建议
            
            格式：Q: 问题\nA: 答案\n---
            """
            
            expert_content = self.call_qwen_api(prompt, max_tokens=2000)
            if expert_content:
                qa_blocks = expert_content.split('---')
                for block in qa_blocks:
                    if 'Q:' in block and 'A:' in block:
                        lines = block.strip().split('\n')
                        question = ""
                        answer = ""
                        
                        for line in lines:
                            if line.startswith('Q:'):
                                question = line[2:].strip()
                            elif line.startswith('A:'):
                                answer = line[2:].strip()
                            elif answer and line.strip():
                                answer += " " + line.strip()
                        
                        if question and answer:
                            expert_qa.append({
                                'question': question,
                                'answer': answer,
                                'category': 'expert_knowledge',
                                'topic': topic,
                                'age_group': 'professional',
                                'difficulty': 'expert',
                                'enhanced_by': 'qwen_api'
                            })
            
            time.sleep(1)
        
        return expert_qa
    
    def enhance_dataset(self, base_dataset_file: str = 'basic_fine_motor_dataset.json'):
        """增强基础数据集"""
        print("🚀 开始使用Qwen API增强数据集...")
        
        # 1. 加载基础数据集
        print("📥 加载基础数据集...")
        try:
            with open(base_dataset_file, 'r', encoding='utf-8') as f:
                base_dataset = json.load(f)
            
            base_qa_pairs = base_dataset['data']['qa_pairs']
            print(f"   ✅ 加载了 {len(base_qa_pairs)} 个基础问答对")
        except Exception as e:
            print(f"❌ 加载基础数据集失败: {e}")
            return
        
        # 2. 增强基础问答对（选择部分进行增强）
        print("💬 增强基础问答对...")
        enhanced_qa_pairs = []
        
        # 选择每个类别的代表性问答进行增强
        categories = set(qa['category'] for qa in base_qa_pairs)
        for category in categories:
            category_qa = [qa for qa in base_qa_pairs if qa['category'] == category]
            # 每个类别选择前4个进行增强（增加到4个以生成更多数据）
            for qa in category_qa[:4]:
                enhanced_pairs = self.enhance_basic_qa(qa)
                enhanced_qa_pairs.extend(enhanced_pairs)
                time.sleep(0.5)  # API调用间隔
        
        print(f"   ✅ 生成了 {len(enhanced_qa_pairs)} 个增强问答对")
        
        # 3. 生成案例研究
        print("📋 生成案例研究...")
        age_groups = ['1个月', '2个月', '3个月', '4个月', '5个月', '6个月', '7个月', '8个月',
                     '9个月', '10个月', '11个月', '12个月', '14个月', '16个月', '18个月',
                     '21个月', '24个月']  # 扩展到17个月龄
        case_studies = self.generate_case_studies(age_groups)
        print(f"   ✅ 生成了 {len(case_studies)} 个案例研究")
        
        # 4. 生成训练方案
        print("🎯 生成训练方案...")
        training_programs = self.generate_training_programs()
        print(f"   ✅ 生成了 {len(training_programs)} 个训练方案")
        
        # 5. 生成专家问答
        print("🧠 生成专家级问答...")
        expert_qa = self.generate_expert_qa()
        print(f"   ✅ 生成了 {len(expert_qa)} 个专家问答")
        
        # 6. 整合增强数据集
        print("🔗 整合增强数据集...")
        self.enhanced_dataset['metadata'].update({
            'total_enhanced_samples': len(enhanced_qa_pairs) + len(case_studies) + len(training_programs) + len(expert_qa),
            'enhanced_qa_count': len(enhanced_qa_pairs),
            'case_studies_count': len(case_studies),
            'training_programs_count': len(training_programs),
            'expert_qa_count': len(expert_qa),
            'base_samples_count': len(base_qa_pairs)
        })
        
        self.enhanced_dataset['enhanced_data'] = {
            'enhanced_qa_pairs': enhanced_qa_pairs,
            'case_studies': case_studies,
            'training_programs': training_programs,
            'expert_qa': expert_qa
        }
        
        # 保留原始数据集引用
        self.enhanced_dataset['base_dataset'] = base_dataset['metadata']
        
        total_samples = self.enhanced_dataset['metadata']['total_enhanced_samples']
        print(f"✅ 数据集增强完成！总计 {total_samples} 个增强样本")
        
        return self.enhanced_dataset
    
    def save_enhanced_dataset(self, filename: str = 'qwen_enhanced_fine_motor_dataset.json'):
        """保存增强数据集"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.enhanced_dataset, f, ensure_ascii=False, indent=2)
        
        print(f"💾 增强数据集已保存到: {filename}")
        
        # 生成增强报告
        self.generate_enhancement_report()
    
    def generate_enhancement_report(self):
        """生成增强报告"""
        metadata = self.enhanced_dataset['metadata']
        
        report = f"""# Qwen增强版婴幼儿精细运动发展指导数据集报告

## 🤖 AI增强信息
- **增强模型**: {metadata['enhancement_model']}
- **基础数据集**: {metadata['base_dataset']}
- **增强时间**: {metadata['created_time']}
- **总增强样本**: {metadata['total_enhanced_samples']}

## 📊 增强数据分布
- **增强问答对**: {metadata['enhanced_qa_count']} 个
- **临床案例**: {metadata['case_studies_count']} 个
- **训练方案**: {metadata['training_programs_count']} 个
- **专家问答**: {metadata['expert_qa_count']} 个

## 🎯 增强特点
1. **智能扩展**: 基于原始问答生成多角度相关问题
2. **案例丰富**: 真实临床案例增强实用性
3. **方案完整**: 系统化训练方案指导
4. **专业深度**: 专家级问答提升专业水准

## 🚀 应用优势
- **更全面**: 覆盖更多实际应用场景
- **更深入**: 包含专业级深度内容
- **更实用**: 提供具体操作指导
- **更智能**: AI增强保证内容质量

## 💡 使用建议
1. 结合基础数据集使用，获得完整训练效果
2. 根据应用场景选择合适的数据类别
3. 专业应用建议重点关注专家问答部分
4. 家长教育可重点使用案例和训练方案
"""
        
        with open('qwen_enhancement_report.md', 'w', encoding='utf-8') as f:
            f.write(report)
        
        print("📊 增强报告已生成: qwen_enhancement_report.md")

def main():
    """主函数"""
    print("🤖 Qwen增强版婴幼儿精细运动发展指导数据集构建器")
    print("="*60)
    
    # 检查API密钥
    api_key = os.getenv('QWEN_API_KEY')
    if not api_key:
        print("❌ 请设置QWEN_API_KEY环境变量")
        print("💡 使用方法: export QWEN_API_KEY='your_api_key'")
        return
    
    try:
        builder = QwenEnhancedDatasetBuilder(api_key)
        
        # 增强数据集
        enhanced_dataset = builder.enhance_dataset()
        
        # 保存增强数据集
        builder.save_enhanced_dataset()
        
        print("\n🎉 Qwen增强版数据集构建完成！")
        print("📁 生成的文件:")
        print("   - qwen_enhanced_fine_motor_dataset.json (增强数据集)")
        print("   - qwen_enhancement_report.md (增强报告)")
        print("\n🌟 增强特点:")
        print("   ✅ AI智能扩展")
        print("   ✅ 临床案例丰富")
        print("   ✅ 专业训练方案")
        print("   ✅ 专家级深度内容")
        
    except Exception as e:
        print(f"❌ 构建失败: {e}")

if __name__ == "__main__":
    main()
